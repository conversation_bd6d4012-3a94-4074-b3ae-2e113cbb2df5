(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4569],{2655:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var i=s(95155),r=s(12115),a=s(71644),l=s(35695),n=s(30285),d=s(62523),c=s(88539),o=s(6509),u=s(85339),x=s(51154);function m(){let e=(0,l.useRouter)(),[t,s]=(0,r.useState)(""),[a,m]=(0,r.useState)(""),[h,f]=(0,r.useState)(!1),[b,p]=(0,r.useState)(""),g=async s=>{if(s.preventDefault(),!t.trim()||!a.trim())return void p("Please fill in both title and content");f(!0),p("");try{let s=await (0,o.hG)({title:t.trim(),content:a.trim()});s&&e.push("/forum/".concat(s.id))}catch(e){p(e instanceof Error?e.message:"Failed to create question")}finally{f(!1)}};return(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"Question Title"}),(0,i.jsx)(d.p,{id:"title",type:"text",value:t,onChange:e=>s(e.target.value),placeholder:"What's your question about?",className:"w-full",disabled:h,maxLength:200}),(0,i.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Be specific and clear. This will help others understand your question."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-2",children:"Question Details"}),(0,i.jsx)(c.T,{id:"content",value:a,onChange:e=>m(e.target.value),placeholder:"Provide more details about your question. Include any relevant context, what you've tried, or specific information that would help others provide better answers.",rows:12,className:"w-full",disabled:h}),(0,i.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"The more details you provide, the better answers you'll receive."})]}),b&&(0,i.jsxs)("div",{className:"flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,i.jsx)(u.A,{className:"h-5 w-5 text-red-500"}),(0,i.jsx)("span",{className:"text-red-700",children:b})]}),(0,i.jsxs)("div",{className:"flex items-center gap-4 pt-6 border-t border-gray-200",children:[(0,i.jsxs)(n.$,{type:"submit",disabled:h||!t.trim()||!a.trim(),className:"flex items-center gap-2",children:[h&&(0,i.jsx)(x.A,{className:"h-4 w-4 animate-spin"}),h?"Publishing...":"Publish Question"]}),(0,i.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>e.back(),disabled:h,children:"Cancel"})]}),(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Tips for asking great questions:"}),(0,i.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,i.jsx)("li",{children:"• Be specific and clear in your title"}),(0,i.jsx)("li",{children:"• Provide context and background information"}),(0,i.jsx)("li",{children:"• Mention what you've already tried"}),(0,i.jsx)("li",{children:"• Ask one question at a time"}),(0,i.jsx)("li",{children:"• Use proper grammar and formatting"})]})]})]})})}var h=s(27600),f=s(66695),b=s(32919),p=s(6874),g=s.n(p);function v(){let{isAuthenticated:e,isLoading:t}=(0,h.A)(),s=(0,l.useRouter)();return((0,r.useEffect)(()=>{t||e||s.push("/login?redirect=/forum/new")},[e,t,s]),t)?(0,i.jsx)(a.A,{children:(0,i.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(x.A,{className:"h-6 w-6 animate-spin"}),(0,i.jsx)("p",{children:"Loading..."})]})})}):e?(0,i.jsx)(a.A,{children:(0,i.jsx)("div",{className:"p-6",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Ask a Question"}),(0,i.jsx)(m,{})]})})}):(0,i.jsx)(a.A,{children:(0,i.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,i.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,i.jsxs)(f.aR,{className:"text-center",children:[(0,i.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100",children:(0,i.jsx)(b.A,{className:"h-6 w-6 text-gray-600"})}),(0,i.jsx)(f.ZB,{children:"Authentication Required"})]}),(0,i.jsxs)(f.Wu,{className:"text-center space-y-4",children:[(0,i.jsx)("p",{className:"text-muted-foreground",children:"Please log in to ask questions in the community forum."}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(g(),{href:"/login?redirect=/forum/new",passHref:!0,children:(0,i.jsx)(n.$,{className:"w-full",children:"Log In"})}),(0,i.jsx)(g(),{href:"/register?redirect=/forum/new",passHref:!0,children:(0,i.jsx)(n.$,{variant:"outline",className:"w-full",children:"Create Account"})})]})]})]})})})}},26495:(e,t,s)=>{Promise.resolve().then(s.bind(s,2655))},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>a});var i=s(95155);s(12115);var r=s(59434);function a(e){let{className:t,type:s,...a}=e;return(0,i.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...a})}},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},88539:(e,t,s)=>{"use strict";s.d(t,{T:()=>a});var i=s(95155);s(12115);var r=s(59434);function a(e){let{className:t,...s}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,6874,2757,7600,2705,8441,1684,7358],()=>t(26495)),_N_E=e.O()}]);