(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5881],{6101:(e,t,s)=>{"use strict";s.d(t,{s:()=>a,t:()=>n});var i=s(12115);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let s=!1,i=e.map(e=>{let i=r(e,t);return s||"function"!=typeof i||(s=!0),i});if(s)return()=>{for(let t=0;t<i.length;t++){let s=i[t];"function"==typeof s?s():r(e[t],null)}}}}function a(...e){return i.useCallback(n(...e),e)}},12318:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},17951:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},18186:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var i=s(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),a=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,i.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:a,className:d="",children:u,iconNode:p,...x}=e;return(0,i.createElement)("svg",{ref:t,...c,width:r,height:r,stroke:s,strokeWidth:a?24*Number(n)/Number(r):n,className:l("lucide",d),...!u&&!o(x)&&{"aria-hidden":"true"},...x},[...p.map(e=>{let[t,s]=e;return(0,i.createElement)(t,s)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let s=(0,i.forwardRef)((s,n)=>{let{className:o,...c}=s;return(0,i.createElement)(d,{ref:n,iconNode:t,className:l("lucide-".concat(r(a(e))),"lucide-".concat(e),o),...c})});return s.displayName=a(e),s}},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var i=s(95155);s(12115);var r=s(74466),n=s(59434);let a=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...r}=e;return(0,i.jsx)("div",{className:(0,n.cn)(a({variant:s}),t),...r})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>l});var i=s(95155);s(12115);var r=s(99708),n=s(74466),a=s(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,a.cn)(l({variant:s,size:n,className:t})),...c})}},38564:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},38821:(e,t,s)=>{Promise.resolve().then(s.bind(s,65839))},51976:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var i=s(52596),r=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,i.$)(t))}},65839:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var i=s(95155),r=s(12115),n=s(30285),a=s(66695),l=s(26126),o=s(38564),c=s(18186),d=s(71366),u=s(51976),p=s(69074),x=s(12318),h=s(71539),m=s(17951),f=s(64614),g=s(77470);let v=[{id:"thought_leadership_post",name:"Thought Leadership Post",points:8,description:"Share industry insights and expertise",icon:(0,i.jsx)(o.A,{className:"h-4 w-4"}),color:"bg-purple-100 text-purple-600"},{id:"resource_post",name:"Resource Post",points:5,description:"Share valuable resources with the community",icon:(0,i.jsx)(c.A,{className:"h-4 w-4"}),color:"bg-blue-100 text-blue-600"},{id:"comment",name:"Comment",points:3,description:"Engage with community posts",icon:(0,i.jsx)(d.A,{className:"h-4 w-4"}),color:"bg-green-100 text-green-600"},{id:"upvote",name:"Upvote",points:3,description:"Appreciate quality content",icon:(0,i.jsx)(u.A,{className:"h-4 w-4"}),color:"bg-red-100 text-red-600"},{id:"news_post",name:"News Post",points:2,description:"Share industry news and updates",icon:(0,i.jsx)(p.A,{className:"h-4 w-4"}),color:"bg-orange-100 text-orange-600"},{id:"add_connection",name:"New Connection",points:1,description:"Connect with other members",icon:(0,i.jsx)(x.A,{className:"h-4 w-4"}),color:"bg-pink-100 text-pink-600"},{id:"daily_visit",name:"Daily Visit",points:1,description:"Stay active in the community",icon:(0,i.jsx)(h.A,{className:"h-4 w-4"}),color:"bg-yellow-100 text-yellow-600"}];function y(){let{isLoggedIn:e,user:t}=(0,f.w1)(),{notifications:s,unreadCount:o,socket:d}=(0,g.E)(),[u,p]=(0,r.useState)(null),[x,y]=(0,r.useState)([]),j=async e=>{if(!(null==t?void 0:t.id))return void alert("Please log in to test notifications");p(e.id);try{let s=await fetch("/api/wp-proxy/iq-score/test-award",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:t.id,activity_type:e.id})}),i=await s.json();i.success?y(t=>{var s;return[{activity:e.name,points:e.points,timestamp:new Date().toLocaleTimeString(),success:!0,newTotal:(null==(s=i.data)?void 0:s.new_score)||"Unknown"},...t.slice(0,9)]}):y(t=>[{activity:e.name,points:e.points,timestamp:new Date().toLocaleTimeString(),success:!1,error:i.message||"Failed to award points"},...t.slice(0,9)])}catch(t){console.error("Error awarding points:",t),y(t=>[{activity:e.name,points:e.points,timestamp:new Date().toLocaleTimeString(),success:!1,error:"Network error"},...t.slice(0,9)])}finally{p(null)}};return e?(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"IQ Points Notification Test"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Test the real-time notification system by triggering different point-awarding activities. Each activity will award points and send a socket notification."})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[(0,i.jsx)(a.Zp,{children:(0,i.jsx)(a.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(c.A,{className:"h-5 w-5 text-blue-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Current User"}),(0,i.jsx)("p",{className:"font-semibold",children:(null==t?void 0:t.display_name)||(null==t?void 0:t.username)})]})]})})}),(0,i.jsx)(a.Zp,{children:(0,i.jsx)(a.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(h.A,{className:"h-5 w-5 text-amber-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Unread Notifications"}),(0,i.jsx)("p",{className:"font-semibold",children:o})]})]})})}),(0,i.jsx)(a.Zp,{children:(0,i.jsx)(a.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(m.A,{className:"h-5 w-5 text-purple-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Total Notifications"}),(0,i.jsx)("p",{className:"font-semibold",children:s.length})]})]})})})]}),(0,i.jsxs)(a.Zp,{className:"mb-4",children:[(0,i.jsxs)(a.aR,{children:[(0,i.jsx)(a.ZB,{children:"Socket Debug"}),(0,i.jsx)(a.BT,{children:"Test socket connection and debug information."})]}),(0,i.jsxs)(a.Wu,{children:[(0,i.jsx)(n.$,{variant:"outline",onClick:()=>{d?(d.emit("test_connection",{message:"Test from client",timestamp:new Date().toISOString(),userId:null==t?void 0:t.id}),alert("Socket ".concat(d.connected?"is connected":"is disconnected",". Check console for details."))):alert("Socket is not initialized")},className:"mb-2",children:"Test Socket Connection"}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:["Socket Status: ",(null==d?void 0:d.connected)?"✅ Connected":"❌ Disconnected",(null==d?void 0:d.id)&&" (ID: ".concat(d.id,")")]})]})]}),(0,i.jsxs)(a.Zp,{className:"mb-8",children:[(0,i.jsxs)(a.aR,{children:[(0,i.jsx)(a.ZB,{children:"Trigger Activities"}),(0,i.jsx)(a.BT,{children:"Click any activity below to simulate earning points and receiving notifications."})]}),(0,i.jsx)(a.Wu,{children:(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:v.map(e=>(0,i.jsxs)(n.$,{variant:"outline",className:"h-auto p-4 flex flex-col items-start gap-2",onClick:()=>j(e),disabled:u===e.id,children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[(0,i.jsx)("div",{className:"p-2 rounded-full ".concat(e.color),children:e.icon}),(0,i.jsxs)("div",{className:"flex-1 text-left",children:[(0,i.jsx)("p",{className:"font-medium",children:e.name}),(0,i.jsxs)(l.E,{variant:"secondary",className:"text-xs",children:["+",e.points," points"]})]})]}),(0,i.jsx)("p",{className:"text-xs text-gray-500 text-left",children:e.description}),u===e.id&&(0,i.jsx)("div",{className:"text-xs text-blue-500",children:"Awarding points..."})]},e.id))})})]}),x.length>0&&(0,i.jsxs)(a.Zp,{children:[(0,i.jsxs)(a.aR,{children:[(0,i.jsx)(a.ZB,{children:"Recent Activity"}),(0,i.jsx)(a.BT,{children:"Results from your recent point-awarding activities."})]}),(0,i.jsx)(a.Wu,{children:(0,i.jsx)("div",{className:"space-y-3",children:x.map((e,t)=>(0,i.jsx)("div",{className:"p-3 rounded-lg border ".concat(e.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:e.activity}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.success?"+".concat(e.points," points awarded"):e.error}),e.success&&e.newTotal&&(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:["New total: ",e.newTotal," points"]})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("p",{className:"text-xs text-gray-500",children:e.timestamp}),(0,i.jsx)(l.E,{variant:e.success?"default":"destructive",className:"text-xs",children:e.success?"Success":"Failed"})]})]})},t))})})]}),(0,i.jsxs)(a.Zp,{className:"mt-8",children:[(0,i.jsx)(a.aR,{children:(0,i.jsx)(a.ZB,{children:"How to Test"})}),(0,i.jsx)(a.Wu,{children:(0,i.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm",children:[(0,i.jsx)("li",{children:"Make sure you're logged in and the notification bell is visible in the header"}),(0,i.jsx)("li",{children:"Click any activity button above to simulate earning points"}),(0,i.jsx)("li",{children:"Watch for the notification bell to show a red badge indicating new notifications"}),(0,i.jsx)("li",{children:"Click the notification bell to see your IQ points notification with details"}),(0,i.jsx)("li",{children:"Try different activities to see various point values and messages"}),(0,i.jsx)("li",{children:"Earn enough points to trigger a rank promotion for special celebration notifications"})]})})]})]})}):(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsx)(a.Zp,{className:"max-w-md mx-auto",children:(0,i.jsxs)(a.aR,{children:[(0,i.jsx)(a.ZB,{children:"Authentication Required"}),(0,i.jsx)(a.BT,{children:"Please log in to test the IQ points notification system."})]})})})}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>a,aR:()=>l,wL:()=>u});var i=s(95155),r=s(12115),n=s(59434);let a=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});a.displayName="Card";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...r})});l.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,n.cn)("p-6",s),...r})});d.displayName="CardContent";let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6",s),...r})});u.displayName="CardFooter"},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71366:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},71539:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},99708:(e,t,s)=>{"use strict";s.d(t,{DX:()=>l,Dc:()=>c,TL:()=>a});var i=s(12115),r=s(6101),n=s(95155);function a(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:s,...n}=e;if(i.isValidElement(s)){var a;let e,l,o=(a=s,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let s={...t};for(let i in t){let r=e[i],n=t[i];/^on[A-Z]/.test(i)?r&&n?s[i]=(...e)=>{let t=n(...e);return r(...e),t}:r&&(s[i]=r):"style"===i?s[i]={...r,...n}:"className"===i&&(s[i]=[r,n].filter(Boolean).join(" "))}return{...e,...s}}(n,s.props);return s.type!==i.Fragment&&(c.ref=t?(0,r.t)(t,o):o),i.cloneElement(s,c)}return i.Children.count(s)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=i.forwardRef((e,s)=>{let{children:r,...a}=e,l=i.Children.toArray(r),o=l.find(d);if(o){let e=o.props.children,r=l.map(t=>t!==o?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...a,ref:s,children:i.isValidElement(e)?i.cloneElement(e,void 0,r):null})}return(0,n.jsx)(t,{...a,ref:s,children:r})});return s.displayName=`${e}.Slot`,s}var l=a("Slot"),o=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function d(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,2757,5272,4298,7600,6261,8441,1684,7358],()=>t(38821)),_N_E=e.O()}]);