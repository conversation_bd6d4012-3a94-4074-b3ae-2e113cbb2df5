/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wp-proxy/posts/route";
exports.ids = ["app/api/wp-proxy/posts/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fposts%2Froute&page=%2Fapi%2Fwp-proxy%2Fposts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fposts%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fposts%2Froute&page=%2Fapi%2Fwp-proxy%2Fposts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fposts%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_posts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wp-proxy/posts/route.ts */ \"(rsc)/./src/app/api/wp-proxy/posts/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wp-proxy/posts/route\",\n        pathname: \"/api/wp-proxy/posts\",\n        filename: \"route\",\n        bundlePath: \"app/api/wp-proxy/posts/route\"\n    },\n    resolvedPagePath: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/api/wp-proxy/posts/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_posts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fposts%2Froute&page=%2Fapi%2Fwp-proxy%2Fposts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fposts%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wp-proxy/posts/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/wp-proxy/posts/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n\n/**\n * Proxy handler for WordPress posts\n * Handles fetching posts with various filters including categories\n */ async function GET(request) {\n    try {\n        // WordPress API URL from environment variable\n        const wpBaseUrl = \"http://tourismiq-headless.local\" || 0;\n        const apiUrl = `${wpBaseUrl}/wp-json`;\n        // Get query parameters\n        const searchParams = request.nextUrl.searchParams;\n        const perPage = searchParams.get('per_page') || '10';\n        const categories = searchParams.get('categories');\n        const embed = searchParams.get('_embed') ? '&_embed=1' : '';\n        const page = searchParams.get('page') || '1';\n        const orderby = searchParams.get('orderby') || 'date';\n        const order = searchParams.get('order') || 'desc';\n        const sticky = searchParams.get('sticky');\n        const slug = searchParams.get('slug');\n        const metaKey = searchParams.get('meta_key');\n        const excludeVendorPosts = searchParams.get('exclude_vendor_posts');\n        // Construct the WordPress API URL for posts with filters\n        let wpUrl = `${apiUrl}/wp/v2/posts?per_page=${perPage}&page=${page}&orderby=${orderby}&order=${order}${embed}`;\n        // Add meta_key if provided (for ordering by meta values)\n        if (metaKey) {\n            wpUrl += `&meta_key=${encodeURIComponent(metaKey)}`;\n        }\n        // Add category filter if provided\n        if (categories) {\n            wpUrl += `&categories=${categories}`;\n            // Let's also try to get the category details for debugging\n            try {\n                const categoryCheckUrl = `${apiUrl}/wp/v2/categories/${categories}`;\n                const categoryCheckResponse = await fetch(categoryCheckUrl, {\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Cookie: request.headers.get('cookie') || ''\n                    },\n                    credentials: 'include'\n                });\n                if (categoryCheckResponse.ok) {\n                    await categoryCheckResponse.json();\n                }\n            } catch  {\n            // Category check failed, continue without it\n            }\n        }\n        // Add sticky filter if provided\n        if (sticky) {\n            wpUrl += `&sticky=${sticky}`;\n        }\n        // Add slug filter if provided\n        if (slug) {\n            wpUrl += `&slug=${encodeURIComponent(slug)}`;\n        }\n        // Add vendor post exclusion filter if provided\n        if (excludeVendorPosts === 'true') {\n            // Use meta_query to exclude posts that have a post_vendor_id meta field set\n            const metaQuery = JSON.stringify([\n                {\n                    key: 'post_vendor_id',\n                    compare: 'NOT EXISTS'\n                }\n            ]);\n            wpUrl += `&meta_query=${encodeURIComponent(metaQuery)}`;\n        }\n        // Use environment variables for WordPress credentials (server-side only)\n        const username = process.env.WORDPRESS_USERNAME;\n        const password = process.env.WORDPRESS_APPLICATION_PASSWORD;\n        const headers = {\n            'Content-Type': 'application/json',\n            Cookie: request.headers.get('cookie') || ''\n        };\n        // Add authorization header if credentials are available\n        if (username && password) {\n            const auth = Buffer.from(`${username}:${password}`).toString('base64');\n            headers['Authorization'] = `Basic ${auth}`;\n        }\n        // Fetch posts from WordPress\n        let response = await fetch(wpUrl, {\n            headers,\n            credentials: 'include'\n        });\n        // If meta sorting fails, fall back to date sorting\n        if (!response.ok && metaKey) {\n            console.warn(`Meta sorting failed (${response.status}), falling back to date sorting`);\n            // Construct fallback URL without meta sorting\n            let fallbackUrl = `${apiUrl}/wp/v2/posts?per_page=${perPage}&page=${page}&orderby=date&order=desc${embed}`;\n            // Add category filter if provided\n            if (categories) {\n                fallbackUrl += `&categories=${categories}`;\n            }\n            // Add sticky filter if provided\n            if (sticky) {\n                fallbackUrl += `&sticky=${sticky}`;\n            }\n            // Add slug filter if provided\n            if (slug) {\n                fallbackUrl += `&slug=${encodeURIComponent(slug)}`;\n            }\n            // Add vendor post exclusion filter if provided\n            if (excludeVendorPosts === 'true') {\n                const metaQuery = JSON.stringify([\n                    {\n                        key: 'post_vendor_id',\n                        compare: 'NOT EXISTS'\n                    }\n                ]);\n                fallbackUrl += `&meta_query=${encodeURIComponent(metaQuery)}`;\n            }\n            response = await fetch(fallbackUrl, {\n                headers,\n                credentials: 'include'\n            });\n        }\n        if (!response.ok) {\n            console.error(`WordPress API error: ${response.status} ${response.statusText}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch posts from WordPress'\n            }, {\n                status: response.status\n            });\n        }\n        // Get the posts data\n        const posts = await response.json();\n        // Create response with posts data\n        const nextResponse = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(posts);\n        // Disable caching for posts to ensure fresh data\n        nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');\n        nextResponse.headers.set('Pragma', 'no-cache');\n        nextResponse.headers.set('Expires', '0');\n        // Forward WordPress pagination headers\n        const totalPages = response.headers.get('X-WP-TotalPages');\n        const total = response.headers.get('X-WP-Total');\n        if (totalPages) {\n            nextResponse.headers.set('X-WP-TotalPages', totalPages);\n        }\n        if (total) {\n            nextResponse.headers.set('X-WP-Total', total);\n        }\n        return nextResponse;\n    } catch (error) {\n        console.error('Error in posts proxy:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wp-proxy/posts/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fposts%2Froute&page=%2Fapi%2Fwp-proxy%2Fposts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fposts%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();