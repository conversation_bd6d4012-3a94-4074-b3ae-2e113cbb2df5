"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6261],{64614:(e,t,i)=>{i.d(t,{iZ:()=>n,w1:()=>a});var o=i(32960),s=i(93853);function a(){let{data:e,isLoading:t,error:i,refetch:a}=(0,o.I)({queryKey:s.lH.auth.status(),queryFn:async()=>{let e=await fetch("/api/wp-proxy/auth/status",{headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok)return{isAuthenticated:!1,user:null};let t=await e.json();return{isAuthenticated:t.isLoggedIn||t.isAuthenticated||!1,user:t.wpUser||t.user||null}},staleTime:3e5,gcTime:6e5,retry:!1,refetchOnWindowFocus:!1,refetchOnReconnect:!0});return{isLoggedIn:(null==e?void 0:e.isAuthenticated)||!1,isLoading:t,user:(null==e?void 0:e.user)||null,error:i,refetch:a}}function n(){let{isLoggedIn:e,isLoading:t,user:i,error:o}=a();return{user:i||null,isAuthenticated:e||!1,isLoading:t,error:o}}},77470:(e,t,i)=>{i.d(t,{E:()=>u,NotificationProvider:()=>d});var o=i(95155),s=i(12115),a=i(14298),n=i(27600);let l=(0,s.createContext)(void 0),r=e=>"tourismiq_notifications_".concat(e||"anonymous"),c=new Set,d=e=>{let{children:t}=e,[i,d]=(0,s.useState)([]),[u,f]=(0,s.useState)(null),[m,p]=(0,s.useState)(!1),{isAuthenticated:h,user:y}=(0,n.A)(),v=i.filter(e=>!e.read&&"message"!==e.type).length;(0,s.useEffect)(()=>{if(!h||!(null==y?void 0:y.id))return u&&(u.disconnect(),f(null),p(!1)),d([]),()=>{};let e=(0,a.io)("http://localhost:3000",{transports:["websocket"],autoConnect:!0,withCredentials:!0,extraHeaders:{"Cache-Control":"no-cache",Pragma:"no-cache",Expires:"0"}});return e.on("connect",()=>{p(!0),y&&y.id?e.emit("authenticate",{userId:y.id}):console.error("Socket authentication failed: No user ID available")}),e.on("disconnect",()=>{p(!1)}),e.on("notification",e=>{if("message"===e.type)return;let t="".concat(e.type,":").concat(e.referenceId,":").concat(e.senderId,":").concat(e.content);if(c.has(t))return;c.add(t),setTimeout(()=>{c.delete(t)},1e4);let i={...e,read:!1};d(e=>[i,...e]),"Notification"in window&&"granted"===Notification.permission&&new Notification("TourismIQ",{body:e.content})}),f(e),()=>{e.disconnect(),f(null),p(!1)}},[h,null==y?void 0:y.id]),(0,s.useEffect)(()=>{if(null==y?void 0:y.id){let e="tourismiq_notifications";localStorage.getItem(e)&&localStorage.removeItem(e);let t=r(y.id),i=localStorage.getItem(t);if(i)try{d(JSON.parse(i))}catch(e){console.error("Failed to parse stored notifications",e)}}else(null==y?void 0:y.id)||d([])},[null==y?void 0:y.id]),(0,s.useEffect)(()=>{if((null==y?void 0:y.id)&&i.length>0){let e=r(y.id);localStorage.setItem(e,JSON.stringify(i))}},[i,null==y?void 0:y.id]),(0,s.useEffect)(()=>{"Notification"in window&&"granted"!==Notification.permission&&Notification.requestPermission()},[]);let g=(0,s.useCallback)(async()=>{if(h&&(null==y?void 0:y.id))try{let e=await fetch("/api/wp-proxy/notifications",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)return void console.error("Failed to fetch notifications:",e.statusText);let t=await e.json();t.success&&t.notifications&&d(e=>{let i=new Set(e.map(e=>e.id)),o=t.notifications.filter(e=>!i.has(e.id)&&"message"!==e.type);return o.length>0?[...o,...e].sort((e,t)=>t.timestamp-e.timestamp):e})}catch(e){console.error("Error fetching notifications:",e)}},[h,null==y?void 0:y.id]);(0,s.useEffect)(()=>{h&&(null==y?void 0:y.id)&&g()},[h,null==y?void 0:y.id,g]);let w=async e=>{if(d(t=>{let i=t.filter(t=>t.id!==e);if(null==y?void 0:y.id){let e=r(y.id);i.length>0?localStorage.setItem(e,JSON.stringify(i)):localStorage.removeItem(e)}return i}),/^\d+$/.test(e))try{let t=await fetch("/api/wp-proxy/notifications?id=".concat(e),{method:"DELETE",credentials:"include",headers:{"Content-Type":"application/json"}});t.ok||console.error("Failed to delete notification from database:",t.statusText)}catch(e){console.error("Error deleting notification:",e)}},S=async()=>{if(h&&(null==y?void 0:y.id))try{let e=await fetch("/api/wp-proxy/notifications",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)return void console.error("Failed to refresh notifications:",e.statusText);let t=await e.json();if(t.success&&t.notifications&&(d(t.notifications),null==y?void 0:y.id)){let e=r(y.id);t.notifications.length>0?localStorage.setItem(e,JSON.stringify(t.notifications)):localStorage.removeItem(e)}}catch(e){console.error("Error refreshing notifications:",e)}},q=async(e,t,i,o,s)=>{let a=!1;if(u&&m)try{u.emit("send_notification",{recipientId:e,type:t,content:i,referenceId:o,referenceType:s,senderId:null==y?void 0:y.id}),a=!0}catch(e){console.error("Error sending notification via socket:",e)}try{let n=await fetch("/api/wp-proxy/notifications/send",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({recipient_id:e,type:t,content:i,reference_id:o,reference_type:s,sender_id:null==y?void 0:y.id})});if(!n.ok){let e=await n.text();if(console.error("Notification API error:",e),!a)throw Error("Failed to send notification via REST API")}}catch(e){if(!a)throw e}};return(0,o.jsx)(l.Provider,{value:{notifications:i,unreadCount:v,socket:u,markAsRead:e=>{d(t=>t.map(t=>t.id===e?{...t,read:!0}:t))},markAllAsRead:()=>{d(e=>e.map(e=>({...e,read:!0})))},removeNotification:w,updateNotification:(e,t)=>{d(i=>i.map(i=>i.id===e?{...i,...t}:i))},addNotification:e=>{let t={...e,id:Math.random().toString(36).substring(2,11),timestamp:Date.now(),read:!1};d(e=>[t,...e])},clearNotifications:()=>{if(d([]),null==y?void 0:y.id){let e=r(y.id);localStorage.removeItem(e)}},sendNotification:q,connected:m,fetchNotifications:g,refreshNotifications:S},children:t})},u=()=>{let e=(0,s.useContext)(l);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}},93853:(e,t,i)=>{i.d(t,{WG:()=>a,lH:()=>s,qQ:()=>o});let o=new(i(87017)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:3,refetchOnWindowFocus:!1,refetchOnReconnect:!1},mutations:{retry:1}}}),s={posts:{all:["posts"],lists:()=>[...s.posts.all,"list"],list:e=>[...s.posts.lists(),e],details:()=>[...s.posts.all,"detail"],detail:e=>[...s.posts.details(),e],userPosts:(e,t,i)=>[...s.posts.all,"user",e,{page:t,perPage:i}],byCategory:(e,t)=>[...s.posts.all,"category",e,t]},auth:{all:["auth"],status:()=>[...s.auth.all,"status"],profile:()=>[...s.auth.all,"profile"]},members:{all:["members"],lists:()=>[...s.members.all,"list"],list:e=>[...s.members.lists(),e],details:()=>[...s.members.all,"detail"],detail:e=>[...s.members.details(),e],profile:e=>[...s.members.all,"profile",e]},connections:{all:["connections"],status:e=>[...s.connections.all,"status",e],list:e=>[...s.connections.all,"list",e],pending:()=>[...s.connections.all,"pending"]},iqScore:{all:["iqScore"],user:e=>[...s.iqScore.all,"user",e],me:()=>[...s.iqScore.all,"me"],leaderboard:()=>[...s.iqScore.all,"leaderboard"],ranks:()=>[...s.iqScore.all,"ranks"]},forum:{all:["forum"],questions:e=>[...s.forum.all,"questions",e],question:e=>[...s.forum.all,"question",e],userQuestions:e=>[...s.forum.all,"user",e],comments:e=>[...s.forum.all,"comments",e]},messages:{all:["messages"],conversations:()=>[...s.messages.all,"conversations"],conversation:e=>[...s.messages.all,"conversation",e],unreadCount:()=>[...s.messages.all,"unreadCount"]},vendors:{all:["vendors"],lists:()=>[...s.vendors.all,"list"],list:e=>[...s.vendors.lists(),e],details:()=>[...s.vendors.all,"detail"],detail:e=>[...s.vendors.details(),e],posts:e=>[...s.vendors.all,"posts",e],categories:()=>[...s.vendors.all,"categories"]},categories:{all:["categories"],list:()=>[...s.categories.all,"list"]},bookmarks:{all:["bookmarks"],user:e=>[...s.bookmarks.all,"user",e]},notifications:{all:["notifications"],list:()=>[...s.notifications.all,"list"]}},a={allPosts:()=>o.invalidateQueries({queryKey:s.posts.all}),post:e=>o.invalidateQueries({queryKey:s.posts.detail(e)}),userPosts:e=>o.invalidateQueries({queryKey:[...s.posts.all,"user",e]}),auth:()=>o.invalidateQueries({queryKey:s.auth.all}),connections:()=>o.invalidateQueries({queryKey:s.connections.all}),iqScores:()=>o.invalidateQueries({queryKey:s.iqScore.all}),forum:()=>o.invalidateQueries({queryKey:s.forum.all}),messages:()=>o.invalidateQueries({queryKey:s.messages.all}),notifications:()=>o.invalidateQueries({queryKey:s.notifications.all})}}}]);