"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5272],{6784:(t,e,s)=>{s.d(e,{II:()=>l,v_:()=>o,wm:()=>c});var i=s(50920),r=s(21239),n=s(73504),a=s(52020);function u(t){return Math.min(1e3*2**t,3e4)}function o(t){return(t??"online")!=="online"||r.t.isOnline()}var h=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function c(t){return t instanceof h}function l(t){let e,s=!1,c=0,l=!1,d=(0,n.T)(),f=()=>i.m.isFocused()&&("always"===t.networkMode||r.t.isOnline())&&t.canRun(),p=()=>o(t.networkMode)&&t.canRun(),y=s=>{l||(l=!0,t.onSuccess?.(s),e?.(),d.resolve(s))},m=s=>{l||(l=!0,t.onError?.(s),e?.(),d.reject(s))},v=()=>new Promise(s=>{e=t=>{(l||f())&&s(t)},t.onPause?.()}).then(()=>{e=void 0,l||t.onContinue?.()}),b=()=>{let e;if(l)return;let i=0===c?t.initialPromise:void 0;try{e=i??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(y).catch(e=>{if(l)return;let i=t.retry??3*!a.S$,r=t.retryDelay??u,n="function"==typeof r?r(c,e):r,o=!0===i||"number"==typeof i&&c<i||"function"==typeof i&&i(c,e);if(s||!o)return void m(e);c++,t.onFail?.(c,e),(0,a.yy)(n).then(()=>f()?void 0:v()).then(()=>{s?m(e):b()})})};return{promise:d,cancel:e=>{l||(m(new h(e)),t.abort?.())},continue:()=>(e?.(),d),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:p,start:()=>(p()?b():v().then(b),d)}}},7165:(t,e,s)=>{s.d(e,{jG:()=>r});var i=t=>setTimeout(t,0),r=function(){let t=[],e=0,s=t=>{t()},r=t=>{t()},n=i,a=i=>{e?t.push(i):n(()=>{s(i)})},u=()=>{let e=t;t=[],e.length&&n(()=>{r(()=>{e.forEach(t=>{s(t)})})})};return{batch:t=>{let s;e++;try{s=t()}finally{--e||u()}return s},batchCalls:t=>(...e)=>{a(()=>{t(...e)})},schedule:a,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{r=t},setScheduler:t=>{n=t}}}()},21239:(t,e,s)=>{s.d(e,{t:()=>n});var i=s(25910),r=s(52020),n=new class extends i.Q{#t=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!r.S$&&window.addEventListener){let e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#t!==t&&(this.#t=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#t}}},25910:(t,e,s)=>{s.d(e,{Q:()=>i});var i=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},32960:(t,e,s)=>{s.d(e,{I:()=>E});var i=s(50920),r=s(7165),n=s(39853),a=s(25910),u=s(73504),o=s(52020),h=class extends a.Q{constructor(t,e){super(),this.options=e,this.#i=t,this.#r=null,this.#n=(0,u.T)(),this.options.experimental_prefetchInRender||this.#n.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#i;#a=void 0;#u=void 0;#o=void 0;#h;#c;#n;#r;#l;#d;#f;#p;#y;#m;#v=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#a.addObserver(this),c(this.#a,this.options)?this.#b():this.updateResult(),this.#g())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.#a,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.#a,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#C(),this.#O(),this.#a.removeObserver(this)}setOptions(t){let e=this.options,s=this.#a;if(this.options=this.#i.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,o.Eh)(this.options.enabled,this.#a))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#R(),this.#a.setOptions(this.options),e._defaulted&&!(0,o.f8)(this.options,e)&&this.#i.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#a,observer:this});let i=this.hasListeners();i&&d(this.#a,s,this.options,e)&&this.#b(),this.updateResult(),i&&(this.#a!==s||(0,o.Eh)(this.options.enabled,this.#a)!==(0,o.Eh)(e.enabled,this.#a)||(0,o.d2)(this.options.staleTime,this.#a)!==(0,o.d2)(e.staleTime,this.#a))&&this.#Q();let r=this.#w();i&&(this.#a!==s||(0,o.Eh)(this.options.enabled,this.#a)!==(0,o.Eh)(e.enabled,this.#a)||r!==this.#m)&&this.#S(r)}getOptimisticResult(t){var e,s;let i=this.#i.getQueryCache().build(this.#i,t),r=this.createResult(i,t);return e=this,s=r,(0,o.f8)(e.getCurrentResult(),s)||(this.#o=r,this.#c=this.options,this.#h=this.#a.state),r}getCurrentResult(){return this.#o}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),e?.(s),Reflect.get(t,s))})}trackProp(t){this.#v.add(t)}getCurrentQuery(){return this.#a}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#i.defaultQueryOptions(t),s=this.#i.getQueryCache().build(this.#i,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#b({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#o))}#b(t){this.#R();let e=this.#a.fetch(this.options,t);return t?.throwOnError||(e=e.catch(o.lQ)),e}#Q(){this.#C();let t=(0,o.d2)(this.options.staleTime,this.#a);if(o.S$||this.#o.isStale||!(0,o.gn)(t))return;let e=(0,o.j3)(this.#o.dataUpdatedAt,t);this.#p=setTimeout(()=>{this.#o.isStale||this.updateResult()},e+1)}#w(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#a):this.options.refetchInterval)??!1}#S(t){this.#O(),this.#m=t,!o.S$&&!1!==(0,o.Eh)(this.options.enabled,this.#a)&&(0,o.gn)(this.#m)&&0!==this.#m&&(this.#y=setInterval(()=>{(this.options.refetchIntervalInBackground||i.m.isFocused())&&this.#b()},this.#m))}#g(){this.#Q(),this.#S(this.#w())}#C(){this.#p&&(clearTimeout(this.#p),this.#p=void 0)}#O(){this.#y&&(clearInterval(this.#y),this.#y=void 0)}createResult(t,e){let s,i=this.#a,r=this.options,a=this.#o,h=this.#h,l=this.#c,p=t!==i?t.state:this.#u,{state:y}=t,m={...y},v=!1;if(e._optimisticResults){let s=this.hasListeners(),a=!s&&c(t,e),u=s&&d(t,i,e,r);(a||u)&&(m={...m,...(0,n.k)(y.data,t.options)}),"isRestoring"===e._optimisticResults&&(m.fetchStatus="idle")}let{error:b,errorUpdatedAt:g,status:C}=m;s=m.data;let O=!1;if(void 0!==e.placeholderData&&void 0===s&&"pending"===C){let t;a?.isPlaceholderData&&e.placeholderData===l?.placeholderData?(t=a.data,O=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#f?.state.data,this.#f):e.placeholderData,void 0!==t&&(C="success",s=(0,o.pl)(a?.data,t,e),v=!0)}if(e.select&&void 0!==s&&!O)if(a&&s===h?.data&&e.select===this.#l)s=this.#d;else try{this.#l=e.select,s=e.select(s),s=(0,o.pl)(a?.data,s,e),this.#d=s,this.#r=null}catch(t){this.#r=t}this.#r&&(b=this.#r,s=this.#d,g=Date.now(),C="error");let R="fetching"===m.fetchStatus,Q="pending"===C,w="error"===C,S=Q&&R,q=void 0!==s,E={status:C,fetchStatus:m.fetchStatus,isPending:Q,isSuccess:"success"===C,isError:w,isInitialLoading:S,isLoading:S,data:s,dataUpdatedAt:m.dataUpdatedAt,error:b,errorUpdatedAt:g,failureCount:m.fetchFailureCount,failureReason:m.fetchFailureReason,errorUpdateCount:m.errorUpdateCount,isFetched:m.dataUpdateCount>0||m.errorUpdateCount>0,isFetchedAfterMount:m.dataUpdateCount>p.dataUpdateCount||m.errorUpdateCount>p.errorUpdateCount,isFetching:R,isRefetching:R&&!Q,isLoadingError:w&&!q,isPaused:"paused"===m.fetchStatus,isPlaceholderData:v,isRefetchError:w&&q,isStale:f(t,e),refetch:this.refetch,promise:this.#n,isEnabled:!1!==(0,o.Eh)(e.enabled,t)};if(this.options.experimental_prefetchInRender){let e=t=>{"error"===E.status?t.reject(E.error):void 0!==E.data&&t.resolve(E.data)},s=()=>{e(this.#n=E.promise=(0,u.T)())},r=this.#n;switch(r.status){case"pending":t.queryHash===i.queryHash&&e(r);break;case"fulfilled":("error"===E.status||E.data!==r.value)&&s();break;case"rejected":("error"!==E.status||E.error!==r.reason)&&s()}}return E}updateResult(){let t=this.#o,e=this.createResult(this.#a,this.options);this.#h=this.#a.state,this.#c=this.options,void 0!==this.#h.data&&(this.#f=this.#a),(0,o.f8)(e,t)||(this.#o=e,this.#q({listeners:(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!this.#v.size)return!0;let i=new Set(s??this.#v);return this.options.throwOnError&&i.add("error"),Object.keys(this.#o).some(e=>this.#o[e]!==t[e]&&i.has(e))})()}))}#R(){let t=this.#i.getQueryCache().build(this.#i,this.options);if(t===this.#a)return;let e=this.#a;this.#a=t,this.#u=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#g()}#q(t){r.jG.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#o)}),this.#i.getQueryCache().notify({query:this.#a,type:"observerResultsUpdated"})})}};function c(t,e){return!1!==(0,o.Eh)(e.enabled,t)&&void 0===t.state.data&&("error"!==t.state.status||!1!==e.retryOnMount)||void 0!==t.state.data&&l(t,e,e.refetchOnMount)}function l(t,e,s){if(!1!==(0,o.Eh)(e.enabled,t)&&"static"!==(0,o.d2)(e.staleTime,t)){let i="function"==typeof s?s(t):s;return"always"===i||!1!==i&&f(t,e)}return!1}function d(t,e,s,i){return(t!==e||!1===(0,o.Eh)(i.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&f(t,s)}function f(t,e){return!1!==(0,o.Eh)(e.enabled,t)&&t.isStaleByTime((0,o.d2)(e.staleTime,t))}var p=s(12115),y=s(26715);s(95155);var m=p.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),v=()=>p.useContext(m),b=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&!e.isReset()&&(t.retryOnMount=!1)},g=t=>{p.useEffect(()=>{t.clearReset()},[t])},C=t=>{let{result:e,errorResetBoundary:s,throwOnError:i,query:r,suspense:n}=t;return e.isError&&!s.isReset()&&!e.isFetching&&r&&(n&&void 0===e.data||(0,o.GU)(i,[e.error,r]))},O=p.createContext(!1),R=()=>p.useContext(O);O.Provider;var Q=t=>{if(t.suspense){let e=t=>"static"===t?t:Math.max(t??1e3,1e3),s=t.staleTime;t.staleTime="function"==typeof s?(...t)=>e(s(...t)):e(s),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}},w=(t,e)=>t.isLoading&&t.isFetching&&!e,S=(t,e)=>t?.suspense&&e.isPending,q=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function E(t,e){return function(t,e,s){var i,n,a,u,h;let c=R(),l=v(),d=(0,y.jE)(s),f=d.defaultQueryOptions(t);null==(n=d.getDefaultOptions().queries)||null==(i=n._experimental_beforeQuery)||i.call(n,f),f._optimisticResults=c?"isRestoring":"optimistic",Q(f),b(f,l),g(l);let m=!d.getQueryCache().get(f.queryHash),[O]=p.useState(()=>new e(d,f)),E=O.getOptimisticResult(f),F=!c&&!1!==t.subscribed;if(p.useSyncExternalStore(p.useCallback(t=>{let e=F?O.subscribe(r.jG.batchCalls(t)):o.lQ;return O.updateResult(),e},[O,F]),()=>O.getCurrentResult(),()=>O.getCurrentResult()),p.useEffect(()=>{O.setOptions(f)},[f,O]),S(f,E))throw q(f,O,l);if(C({result:E,errorResetBoundary:l,throwOnError:f.throwOnError,query:d.getQueryCache().get(f.queryHash),suspense:f.suspense}))throw E.error;if(null==(u=d.getDefaultOptions().queries)||null==(a=u._experimental_afterQuery)||a.call(u,f,E),f.experimental_prefetchInRender&&!o.S$&&w(E,c)){let t=m?q(f,O,l):null==(h=d.getQueryCache().get(f.queryHash))?void 0:h.promise;null==t||t.catch(o.lQ).finally(()=>{O.updateResult()})}return f.notifyOnChangeProps?E:O.trackResult(E)}(t,h,e)}},34560:(t,e,s)=>{s.d(e,{$:()=>u,s:()=>a});var i=s(7165),r=s(57948),n=s(6784),a=class extends r.k{#E;#F;#T;constructor(t){super(),this.mutationId=t.mutationId,this.#F=t.mutationCache,this.#E=[],this.state=t.state||u(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#E.includes(t)||(this.#E.push(t),this.clearGcTimeout(),this.#F.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#E=this.#E.filter(e=>e!==t),this.scheduleGc(),this.#F.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#E.length||("pending"===this.state.status?this.scheduleGc():this.#F.remove(this))}continue(){return this.#T?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#P({type:"continue"})};this.#T=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#P({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#P({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#F.canRun(this)});let s="pending"===this.state.status,i=!this.#T.canStart();try{if(s)e();else{this.#P({type:"pending",variables:t,isPaused:i}),await this.#F.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#P({type:"pending",context:e,variables:t,isPaused:i})}let r=await this.#T.start();return await this.#F.config.onSuccess?.(r,t,this.state.context,this),await this.options.onSuccess?.(r,t,this.state.context),await this.#F.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,t,this.state.context),this.#P({type:"success",data:r}),r}catch(e){try{throw await this.#F.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#F.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#P({type:"error",error:e})}}finally{this.#F.runNext(this)}}#P(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),i.jG.batch(()=>{this.#E.forEach(e=>{e.onMutationUpdate(t)}),this.#F.notify({mutation:this,type:"updated",action:t})})}};function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},39853:(t,e,s)=>{s.d(e,{X:()=>u,k:()=>o});var i=s(52020),r=s(7165),n=s(6784),a=s(57948),u=class extends a.k{#I;#D;#j;#i;#T;#x;#M;constructor(t){super(),this.#M=!1,this.#x=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#i=t.client,this.#j=this.#i.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#I=function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#I,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#T?.promise}setOptions(t){this.options={...this.#x,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#j.remove(this)}setData(t,e){let s=(0,i.pl)(this.state.data,t,this.options);return this.#P({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#P({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#T?.promise;return this.#T?.cancel(t),e?e.then(i.lQ).catch(i.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#I)}isActive(){return this.observers.some(t=>!1!==(0,i.Eh)(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===i.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===(0,i.d2)(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!(0,i.j3)(this.state.dataUpdatedAt,t))}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#T?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#T?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#j.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#T&&(this.#M?this.#T.cancel({revert:!0}):this.#T.cancelRetry()),this.scheduleGc()),this.#j.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#P({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#T)return this.#T.continueRetry(),this.#T.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let s=new AbortController,r=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#M=!0,s.signal)})},a=()=>{let t=(0,i.ZM)(this.options,e),s=(()=>{let t={client:this.#i,queryKey:this.queryKey,meta:this.meta};return r(t),t})();return(this.#M=!1,this.options.persister)?this.options.persister(t,s,this):t(s)},u=(()=>{let t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#i,state:this.state,fetchFn:a};return r(t),t})();this.options.behavior?.onFetch(u,this),this.#D=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==u.fetchOptions?.meta)&&this.#P({type:"fetch",meta:u.fetchOptions?.meta});let o=t=>{(0,n.wm)(t)&&t.silent||this.#P({type:"error",error:t}),(0,n.wm)(t)||(this.#j.config.onError?.(t,this),this.#j.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#T=(0,n.II)({initialPromise:e?.initialPromise,fn:u.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0===t)return void o(Error(`${this.queryHash} data is undefined`));try{this.setData(t)}catch(t){o(t);return}this.#j.config.onSuccess?.(t,this),this.#j.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError:o,onFail:(t,e)=>{this.#P({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#P({type:"pause"})},onContinue:()=>{this.#P({type:"continue"})},retry:u.options.retry,retryDelay:u.options.retryDelay,networkMode:u.options.networkMode,canRun:()=>!0}),this.#T.start()}#P(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...o(e.data,this.options),fetchMeta:t.meta??null};case"success":return this.#D=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let s=t.error;if((0,n.wm)(s)&&s.revert&&this.#D)return{...this.#D,fetchStatus:"idle"};return{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),r.jG.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#j.notify({query:this,type:"updated",action:t})})}};function o(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,n.v_)(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}},50920:(t,e,s)=>{s.d(e,{m:()=>n});var i=s(25910),r=s(52020),n=new class extends i.Q{#A;#e;#s;constructor(){super(),this.#s=t=>{if(!r.S$&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#A!==t&&(this.#A=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#A?this.#A:globalThis.document?.visibilityState!=="hidden"}}},52020:(t,e,s)=>{s.d(e,{Cp:()=>p,EN:()=>f,Eh:()=>h,F$:()=>d,GU:()=>S,MK:()=>c,S$:()=>i,ZM:()=>w,ZZ:()=>R,Zw:()=>n,d2:()=>o,f8:()=>y,gn:()=>a,hT:()=>Q,j3:()=>u,lQ:()=>r,nJ:()=>l,pl:()=>C,y9:()=>O,yy:()=>g});var i="undefined"==typeof window||"Deno"in globalThis;function r(){}function n(t,e){return"function"==typeof t?t(e):t}function a(t){return"number"==typeof t&&t>=0&&t!==1/0}function u(t,e){return Math.max(t+(e||0)-Date.now(),0)}function o(t,e){return"function"==typeof t?t(e):t}function h(t,e){return"function"==typeof t?t(e):t}function c(t,e){let{type:s="all",exact:i,fetchStatus:r,predicate:n,queryKey:a,stale:u}=t;if(a){if(i){if(e.queryHash!==d(a,e.options))return!1}else if(!p(e.queryKey,a))return!1}if("all"!==s){let t=e.isActive();if("active"===s&&!t||"inactive"===s&&t)return!1}return("boolean"!=typeof u||e.isStale()===u)&&(!r||r===e.state.fetchStatus)&&(!n||!!n(e))}function l(t,e){let{exact:s,status:i,predicate:r,mutationKey:n}=t;if(n){if(!e.options.mutationKey)return!1;if(s){if(f(e.options.mutationKey)!==f(n))return!1}else if(!p(e.options.mutationKey,n))return!1}return(!i||e.state.status===i)&&(!r||!!r(e))}function d(t,e){return(e?.queryKeyHashFn||f)(t)}function f(t){return JSON.stringify(t,(t,e)=>v(e)?Object.keys(e).sort().reduce((t,s)=>(t[s]=e[s],t),{}):e)}function p(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(s=>p(t[s],e[s]))}function y(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let s in t)if(t[s]!==e[s])return!1;return!0}function m(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function v(t){if(!b(t))return!1;let e=t.constructor;if(void 0===e)return!0;let s=e.prototype;return!!b(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function b(t){return"[object Object]"===Object.prototype.toString.call(t)}function g(t){return new Promise(e=>{setTimeout(e,t)})}function C(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?function t(e,s){if(e===s)return e;let i=m(e)&&m(s);if(i||v(e)&&v(s)){let r=i?e:Object.keys(e),n=r.length,a=i?s:Object.keys(s),u=a.length,o=i?[]:{},h=new Set(r),c=0;for(let r=0;r<u;r++){let n=i?r:a[r];(!i&&h.has(n)||i)&&void 0===e[n]&&void 0===s[n]?(o[n]=void 0,c++):(o[n]=t(e[n],s[n]),o[n]===e[n]&&void 0!==e[n]&&c++)}return n===u&&c===n?e:o}return s}(t,e):e}function O(t,e,s=0){let i=[...t,e];return s&&i.length>s?i.slice(1):i}function R(t,e,s=0){let i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var Q=Symbol();function w(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==Q?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}function S(t,e){return"function"==typeof t?t(...e):!!t}},57948:(t,e,s)=>{s.d(e,{k:()=>r});var i=s(52020),r=class{#k;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,i.gn)(this.gcTime)&&(this.#k=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(i.S$?1/0:3e5))}clearGcTimeout(){this.#k&&(clearTimeout(this.#k),this.#k=void 0)}}},73504:(t,e,s)=>{function i(){let t,e,s=new Promise((s,i)=>{t=s,e=i});function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}s.d(e,{T:()=>i})},87017:(t,e,s)=>{s.d(e,{E:()=>y});var i=s(52020),r=s(39853),n=s(7165),a=s(25910),u=class extends a.Q{constructor(t={}){super(),this.config=t,this.#U=new Map}#U;build(t,e,s){let n=e.queryKey,a=e.queryHash??(0,i.F$)(n,e),u=this.get(a);return u||(u=new r.X({client:t,queryKey:n,queryHash:a,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(n)}),this.add(u)),u}add(t){this.#U.has(t.queryHash)||(this.#U.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){let e=this.#U.get(t.queryHash);e&&(t.destroy(),e===t&&this.#U.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){n.jG.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#U.get(t)}getAll(){return[...this.#U.values()]}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,i.MK)(e,t))}findAll(t={}){let e=this.getAll();return Object.keys(t).length>0?e.filter(e=>(0,i.MK)(t,e)):e}notify(t){n.jG.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){n.jG.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){n.jG.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},o=s(34560),h=class extends a.Q{constructor(t={}){super(),this.config=t,this.#G=new Set,this.#K=new Map,this.#L=0}#G;#K;#L;build(t,e,s){let i=new o.s({mutationCache:this,mutationId:++this.#L,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#G.add(t);let e=c(t);if("string"==typeof e){let s=this.#K.get(e);s?s.push(t):this.#K.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#G.delete(t)){let e=c(t);if("string"==typeof e){let s=this.#K.get(e);if(s)if(s.length>1){let e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&this.#K.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){let e=c(t);if("string"!=typeof e)return!0;{let s=this.#K.get(e),i=s?.find(t=>"pending"===t.state.status);return!i||i===t}}runNext(t){let e=c(t);if("string"!=typeof e)return Promise.resolve();{let s=this.#K.get(e)?.find(e=>e!==t&&e.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){n.jG.batch(()=>{this.#G.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#G.clear(),this.#K.clear()})}getAll(){return Array.from(this.#G)}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,i.nJ)(e,t))}findAll(t={}){return this.getAll().filter(e=>(0,i.nJ)(t,e))}notify(t){n.jG.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){let t=this.getAll().filter(t=>t.state.isPaused);return n.jG.batch(()=>Promise.all(t.map(t=>t.continue().catch(i.lQ))))}};function c(t){return t.options.scope?.id}var l=s(50920),d=s(21239);function f(t){return{onFetch:(e,s)=>{let r=e.options,n=e.fetchOptions?.meta?.fetchMore?.direction,a=e.state.data?.pages||[],u=e.state.data?.pageParams||[],o={pages:[],pageParams:[]},h=0,c=async()=>{let s=!1,c=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)})},l=(0,i.ZM)(e.options,e.fetchOptions),d=async(t,r,n)=>{if(s)return Promise.reject();if(null==r&&t.pages.length)return Promise.resolve(t);let a=(()=>{let t={client:e.client,queryKey:e.queryKey,pageParam:r,direction:n?"backward":"forward",meta:e.options.meta};return c(t),t})(),u=await l(a),{maxPages:o}=e.options,h=n?i.ZZ:i.y9;return{pages:h(t.pages,u,o),pageParams:h(t.pageParams,r,o)}};if(n&&a.length){let t="backward"===n,e={pages:a,pageParams:u},s=(t?function(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}:p)(r,e);o=await d(e,s,t)}else{let e=t??a.length;do{let t=0===h?u[0]??r.initialPageParam:p(r,o);if(h>0&&null==t)break;o=await d(o,t),h++}while(h<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(c,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=c}}}function p(t,{pages:e,pageParams:s}){let i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}var y=class{#_;#F;#x;#H;#$;#N;#Z;#W;constructor(t={}){this.#_=t.queryCache||new u,this.#F=t.mutationCache||new h,this.#x=t.defaultOptions||{},this.#H=new Map,this.#$=new Map,this.#N=0}mount(){this.#N++,1===this.#N&&(this.#Z=l.m.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#_.onFocus())}),this.#W=d.t.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#_.onOnline())}))}unmount(){this.#N--,0===this.#N&&(this.#Z?.(),this.#Z=void 0,this.#W?.(),this.#W=void 0)}isFetching(t){return this.#_.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#F.findAll({...t,status:"pending"}).length}getQueryData(t){let e=this.defaultQueryOptions({queryKey:t});return this.#_.get(e.queryHash)?.state.data}ensureQueryData(t){let e=this.defaultQueryOptions(t),s=this.#_.build(this,e),r=s.state.data;return void 0===r?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime((0,i.d2)(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(r))}getQueriesData(t){return this.#_.findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,s){let r=this.defaultQueryOptions({queryKey:t}),n=this.#_.get(r.queryHash),a=n?.state.data,u=(0,i.Zw)(e,a);if(void 0!==u)return this.#_.build(this,r).setData(u,{...s,manual:!0})}setQueriesData(t,e,s){return n.jG.batch(()=>this.#_.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,s)]))}getQueryState(t){let e=this.defaultQueryOptions({queryKey:t});return this.#_.get(e.queryHash)?.state}removeQueries(t){let e=this.#_;n.jG.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){let s=this.#_;return n.jG.batch(()=>(s.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){let s={revert:!0,...e};return Promise.all(n.jG.batch(()=>this.#_.findAll(t).map(t=>t.cancel(s)))).then(i.lQ).catch(i.lQ)}invalidateQueries(t,e={}){return n.jG.batch(()=>(this.#_.findAll(t).forEach(t=>{t.invalidate()}),t?.refetchType==="none")?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e))}refetchQueries(t,e={}){let s={...e,cancelRefetch:e.cancelRefetch??!0};return Promise.all(n.jG.batch(()=>this.#_.findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(i.lQ)),"paused"===t.state.fetchStatus?Promise.resolve():e}))).then(i.lQ)}fetchQuery(t){let e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);let s=this.#_.build(this,e);return s.isStaleByTime((0,i.d2)(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(i.lQ).catch(i.lQ)}fetchInfiniteQuery(t){return t.behavior=f(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(i.lQ).catch(i.lQ)}ensureInfiniteQueryData(t){return t.behavior=f(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return d.t.isOnline()?this.#F.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#_}getMutationCache(){return this.#F}getDefaultOptions(){return this.#x}setDefaultOptions(t){this.#x=t}setQueryDefaults(t,e){this.#H.set((0,i.EN)(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){let e=[...this.#H.values()],s={};return e.forEach(e=>{(0,i.Cp)(t,e.queryKey)&&Object.assign(s,e.defaultOptions)}),s}setMutationDefaults(t,e){this.#$.set((0,i.EN)(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){let e=[...this.#$.values()],s={};return e.forEach(e=>{(0,i.Cp)(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;let e={...this.#x.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=(0,i.F$)(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===i.hT&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#x.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#_.clear(),this.#F.clear()}}}}]);