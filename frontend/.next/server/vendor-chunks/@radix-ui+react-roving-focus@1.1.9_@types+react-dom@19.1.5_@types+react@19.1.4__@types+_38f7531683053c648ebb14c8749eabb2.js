"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-roving-focus@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+_38f7531683053c648ebb14c8749eabb2";
exports.ids = ["vendor-chunks/@radix-ui+react-roving-focus@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+_38f7531683053c648ebb14c8749eabb2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+_38f7531683053c648ebb14c8749eabb2/node_modules/@radix-ui/react-roving-focus/dist/index.mjs":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+_38f7531683053c648ebb14c8749eabb2/node_modules/@radix-ui/react-roving-focus/dist/index.mjs ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RovingFocusGroup: () => (/* binding */ RovingFocusGroup),\n/* harmony export */   RovingFocusGroupItem: () => (/* binding */ RovingFocusGroupItem),\n/* harmony export */   createRovingFocusGroupScope: () => (/* binding */ createRovingFocusGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.6_@types+react-dom@19.1.5_@types+react@19.1.4__@types+re_0a81deda95bc01001437e69e14e149d2/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.2_@types+react-dom@19.1.5_@types+react@19.1.4__@types+rea_7b933c94c4e5e4ade0d694d5b36d9593/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Item,Root,RovingFocusGroup,RovingFocusGroupItem,createRovingFocusGroupScope auto */ // src/roving-focus-group.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(GROUP_NAME, [\n    createCollectionScope\n]);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeRovingFocusGroup,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: props.__scopeRovingFocusGroup,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusGroupImpl, {\n                ...props,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, orientation, loop = false, dir, currentTabStopId: currentTabStopIdProp, defaultCurrentTabStopId, onCurrentTabStopIdChange, onEntryFocus, preventScrollOnEntryFocus = false, ...groupProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__.useDirection)(dir);\n    const [currentTabStopId, setCurrentTabStopId] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__.useControllableState)({\n        prop: currentTabStopIdProp,\n        defaultProp: defaultCurrentTabStopId ?? null,\n        onChange: onCurrentTabStopIdChange,\n        caller: GROUP_NAME\n    });\n    const [isTabbingBackOut, setIsTabbingBackOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const handleEntryFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__.useCallbackRef)(onEntryFocus);\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const isClickFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [focusableItemsCount, setFocusableItemsCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupImpl.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n                return ({\n                    \"RovingFocusGroupImpl.useEffect\": ()=>node.removeEventListener(ENTRY_FOCUS, handleEntryFocus)\n                })[\"RovingFocusGroupImpl.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupImpl.useEffect\"], [\n        handleEntryFocus\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusProvider, {\n        scope: __scopeRovingFocusGroup,\n        orientation,\n        dir: direction,\n        loop,\n        currentTabStopId,\n        onItemFocus: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": (tabStopId)=>setCurrentTabStopId(tabStopId)\n        }[\"RovingFocusGroupImpl.useCallback\"], [\n            setCurrentTabStopId\n        ]),\n        onItemShiftTab: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setIsTabbingBackOut(true)\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount + 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount - 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n            \"data-orientation\": orientation,\n            ...groupProps,\n            ref: composedRefs,\n            style: {\n                outline: \"none\",\n                ...props.style\n            },\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, ()=>{\n                isClickFocusRef.current = true;\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, (event)=>{\n                const isKeyboardFocus = !isClickFocusRef.current;\n                if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n                    const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n                    event.currentTarget.dispatchEvent(entryFocusEvent);\n                    if (!entryFocusEvent.defaultPrevented) {\n                        const items = getItems().filter((item)=>item.focusable);\n                        const activeItem = items.find((item)=>item.active);\n                        const currentItem = items.find((item)=>item.id === currentTabStopId);\n                        const candidateItems = [\n                            activeItem,\n                            currentItem,\n                            ...items\n                        ].filter(Boolean);\n                        const candidateNodes = candidateItems.map((item)=>item.ref.current);\n                        focusFirst(candidateNodes, preventScrollOnEntryFocus);\n                    }\n                }\n                isClickFocusRef.current = false;\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onBlur, ()=>setIsTabbingBackOut(false))\n        })\n    });\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, focusable = true, active = false, tabStopId, children, ...itemProps } = props;\n    const autoId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupItem.useEffect\": ()=>{\n            if (focusable) {\n                onFocusableItemAdd();\n                return ({\n                    \"RovingFocusGroupItem.useEffect\": ()=>onFocusableItemRemove()\n                })[\"RovingFocusGroupItem.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupItem.useEffect\"], [\n        focusable,\n        onFocusableItemAdd,\n        onFocusableItemRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.span, {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, (event)=>{\n                if (!focusable) event.preventDefault();\n                else context.onItemFocus(id);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, ()=>context.onItemFocus(id)),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (event.key === \"Tab\" && event.shiftKey) {\n                    context.onItemShiftTab();\n                    return;\n                }\n                if (event.target !== event.currentTarget) return;\n                const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n                if (focusIntent !== void 0) {\n                    if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item)=>item.focusable);\n                    let candidateNodes = items.map((item)=>item.ref.current);\n                    if (focusIntent === \"last\") candidateNodes.reverse();\n                    else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                        if (focusIntent === \"prev\") candidateNodes.reverse();\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                }\n            }),\n            children: typeof children === \"function\" ? children({\n                isCurrentTabStop,\n                hasTabStop: currentTabStopId != null\n            }) : children\n        })\n    });\n});\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n    ArrowLeft: \"prev\",\n    ArrowUp: \"prev\",\n    ArrowRight: \"next\",\n    ArrowDown: \"next\",\n    PageUp: \"first\",\n    Home: \"first\",\n    PageDown: \"last\",\n    End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n    if (dir !== \"rtl\") return key;\n    return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n    const key = getDirectionAwareKey(event.key, dir);\n    if (orientation === \"vertical\" && [\n        \"ArrowLeft\",\n        \"ArrowRight\"\n    ].includes(key)) return void 0;\n    if (orientation === \"horizontal\" && [\n        \"ArrowUp\",\n        \"ArrowDown\"\n    ].includes(key)) return void 0;\n    return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus({\n            preventScroll\n        });\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+_38f7531683053c648ebb14c8749eabb2/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\n");

/***/ })

};
;