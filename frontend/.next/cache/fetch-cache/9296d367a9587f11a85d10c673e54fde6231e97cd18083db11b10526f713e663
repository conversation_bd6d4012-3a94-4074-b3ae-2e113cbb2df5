{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "BYPASS", "cf-ray": "967805bd6caae75d-DEN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Wed, 30 Jul 2025 21:41:50 GMT", "server": "cloudflare", "set-cookie": "__cf_bm=izjRVF3OhTAjcxgcLEhXlrT_hNbuEe7cvWSA0PPV3Ww-1753911710-*******-kn3zrAMuc3g1VaqhKOOYvXfGA2hvznZeTrPlKSpMECBKcyqmlCm2b97167l3QFcbmC_NAKmWo7rMzZNRKAUct977R21LJdNdf_Dtl.D3IxE; path=/; expires=Wed, 30-Jul-25 22:11:50 GMT; domain=.mytourismiq.com; HttpOnly; Secure; SameSite=None", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-envoy-upstream-service-time": "253", "x-powered-by": "WP Engine Headless Platform"}, "body": "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", "status": 200, "url": "https://mytourismiq.com/api/wp-proxy/categories?per_page=100"}, "revalidate": 3600, "tags": []}