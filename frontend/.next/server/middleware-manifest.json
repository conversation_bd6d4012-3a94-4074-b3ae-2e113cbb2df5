{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images|fonts|svg|manifest.json|robots.txt|sw.js|workbox-.*\\.js).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images|fonts|svg|manifest.json|robots.txt|sw.js|workbox-.*\\.js).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "h0r8-eqKCik7PK9H6C-LU", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HfsZ6Perfz63WUekb+dK7FY2Xg1bXORubTm2r7hlmHU=", "__NEXT_PREVIEW_MODE_ID": "319661b66894e183276a30adecb144b4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "97ce42cadb362196114bfde6906e404a255162dc99ce3d210adf430c8e35ccd1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "98e5a904baad5d17feceff09e371a0791c1eba99b8ae9312efbbab327360fcba"}}}, "functions": {}, "sortedMiddleware": ["/"]}