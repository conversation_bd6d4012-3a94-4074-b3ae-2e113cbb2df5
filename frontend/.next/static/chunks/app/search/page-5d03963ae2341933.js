(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2959],{8933:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>u});var t=s(95155),r=s(11518),n=s.n(r),l=s(12115),i=s(35695),o=s(47924),c=s(26126),d=s(66695);let m={post:"\uD83D\uDCC4",user:"\uD83D\uDC64",vendor:"\uD83C\uDFE2",forum:"\uD83D\uDCAC",rfp:"\uD83D\uDCCB",job:"\uD83D\uDCBC"};function x(){let e=(0,i.useSearchParams)().get("q")||"",[a]=(0,l.useState)(e),[s,r]=(0,l.useState)({posts:[],users:[],vendors:[],forum:[],rfps:[],jobs:[],total:0}),[x,u]=(0,l.useState)(!1),[f,p]=(0,l.useState)(""),[h,g]=(0,l.useState)([]),y=(0,l.useCallback)(async e=>{if(console.log('[SEARCH FRONTEND] performSearch called - Query: "'.concat(e,'"')),!e.trim()){r({posts:[],users:[],vendors:[],forum:[],rfps:[],jobs:[],total:0}),g([]);return}u(!0),p("Starting search...");try{let o=["Searching posts and articles...","Looking through community members...","Finding industry vendors...","Checking forum discussions...","Scanning job opportunities...","Reviewing project RFPs...","Exploring case studies...","Searching news and updates...","Finding expert insights...","Checking event listings...","Looking through resources...","Scanning marketplace items..."],c=0;p(o[0]);let d=setInterval(()=>{c=(c+1)%o.length,p(o[c])},3e3),m=await fetch("/api/search?q=".concat(encodeURIComponent(e),"&limit=").concat(100),{credentials:"include"});if(clearInterval(d),p("Processing results..."),m.ok){var a,s,t,n,l,i;let e=await m.json();console.log("[SEARCH FRONTEND] API Response:",{postsCount:(null==(a=e.posts)?void 0:a.length)||0,usersCount:(null==(s=e.users)?void 0:s.length)||0,vendorsCount:(null==(t=e.vendors)?void 0:t.length)||0,forumCount:(null==(n=e.forum)?void 0:n.length)||0,rfpsCount:(null==(l=e.rfps)?void 0:l.length)||0,jobsCount:(null==(i=e.jobs)?void 0:i.length)||0,total:e.total}),r(e);let o=[...e.posts,...e.users,...e.vendors,...e.forum,...e.rfps,...e.jobs];p("Organizing results..."),g(o),p("Complete!"),setTimeout(()=>{u(!1)},400)}}catch(e){console.error("Search error:",e),p("Search failed"),setTimeout(()=>{u(!1)},1500)}},[]);return(0,l.useEffect)(()=>{e&&y(e)},[e,y]),(0,t.jsxs)("div",{className:"min-h-screen bg-[#eff1f4]",children:[(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsxs)("div",{className:"max-w-[1360px] mx-auto py-6 px-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Search Results"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:s.total>0?"".concat(s.total," results found"):"Search across all content"})]}),e&&(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsxs)("p",{className:"text-lg text-gray-700",children:["Search results for:"," ",(0,t.jsxs)("span",{className:"font-semibold",children:["“",e,"”"]})]})})]})}),(0,t.jsxs)("div",{className:"max-w-[1360px] mx-auto py-6 px-4",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["Showing ",h.length," results"]})}),x&&(0,t.jsxs)("div",{className:"jsx-6d0571aa141b98ae fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50",children:[(0,t.jsx)(n(),{id:"6d0571aa141b98ae",children:"@-webkit-keyframes fade-up{0%{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}100%{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-up{0%{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}100%{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-up{0%{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}100%{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-up{0%{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}100%{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}.animate-fade-up.jsx-6d0571aa141b98ae{-webkit-animation:fade-up.6s ease-out;-moz-animation:fade-up.6s ease-out;-o-animation:fade-up.6s ease-out;animation:fade-up.6s ease-out}"}),(0,t.jsxs)("div",{className:"jsx-6d0571aa141b98ae bg-white rounded-lg p-8 max-w-sm w-full mx-4 text-center shadow-2xl",children:[(0,t.jsxs)("div",{className:"jsx-6d0571aa141b98ae mb-6",children:[(0,t.jsxs)("div",{className:"jsx-6d0571aa141b98ae w-16 h-16 mx-auto mb-4 relative",children:[(0,t.jsx)("div",{className:"jsx-6d0571aa141b98ae absolute inset-0 rounded-full border-4 border-gray-200"}),(0,t.jsx)("div",{className:"jsx-6d0571aa141b98ae absolute inset-0 rounded-full border-4 border-[#5cc8ff] border-t-transparent animate-spin"})]}),(0,t.jsx)("h3",{className:"jsx-6d0571aa141b98ae text-lg font-semibold text-gray-900 mb-2",children:"Searching All Content"}),(0,t.jsx)("div",{className:"jsx-6d0571aa141b98ae min-h-[20px] overflow-hidden",children:(0,t.jsx)("p",{className:"jsx-6d0571aa141b98ae text-sm text-gray-600 animate-fade-up",children:f||"Starting search..."},f)})]}),(0,t.jsx)("div",{className:"jsx-6d0571aa141b98ae flex justify-center mt-4",children:(0,t.jsxs)("div",{className:"jsx-6d0571aa141b98ae flex space-x-1",children:[(0,t.jsx)("div",{className:"jsx-6d0571aa141b98ae w-2 h-2 bg-[#5cc8ff] rounded-full animate-bounce"}),(0,t.jsx)("div",{style:{animationDelay:"0.1s"},className:"jsx-6d0571aa141b98ae w-2 h-2 bg-[#5cc8ff] rounded-full animate-bounce"}),(0,t.jsx)("div",{style:{animationDelay:"0.2s"},className:"jsx-6d0571aa141b98ae w-2 h-2 bg-[#5cc8ff] rounded-full animate-bounce"})]})})]})]}),x?null:0===h.length&&a?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(o.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-6"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No results found"}),(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"Try adjusting your search terms or check a different category."})]}):0===h.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(o.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-6"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Start searching"}),(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"Enter a search term to find posts, people, vendors, and more."})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6",children:[h.map(e=>(0,t.jsx)(d.Zp,{className:"hover:shadow-md transition-shadow",children:(0,t.jsx)(d.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"flex justify-center",children:"user"===e.type&&e.avatar_url?(0,t.jsx)("img",{src:e.avatar_url,alt:e.name||"",className:"w-16 h-16 rounded-full object-cover"}):e.featured_media?(0,t.jsx)("img",{src:e.featured_media,alt:e.title||"",className:"w-16 h-16 rounded-lg object-cover"}):(0,t.jsx)("div",{className:"w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center text-3xl",children:m[e.type]||"\uD83D\uDCC4"})}),(0,t.jsxs)("div",{className:"text-center space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 flex-wrap",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 line-clamp-2",children:(0,t.jsx)("a",{href:e.link,className:"hover:text-blue-600 transition-colors",children:e.title||e.name||e.question})}),e.is_paid&&(0,t.jsx)(c.E,{className:"bg-yellow-100 text-yellow-800",children:"Paid"}),"NEW"===e.status&&(0,t.jsx)(c.E,{className:"bg-[#22c55e] text-white",children:"New"})]}),(e.excerpt||e.content||e.description)&&(0,t.jsx)("p",{className:"text-sm text-gray-600 line-clamp-3",children:e.excerpt||e.content||e.description}),(0,t.jsxs)("div",{className:"flex flex-wrap items-center justify-center gap-2 text-xs text-gray-500",children:[(0,t.jsx)("span",{className:"capitalize",children:e.type}),e.author&&(0,t.jsxs)("span",{children:["by ",e.author]}),e.company&&(0,t.jsx)("span",{children:e.company}),e.location&&(0,t.jsx)("span",{children:e.location}),e.date&&(0,t.jsx)("span",{children:new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})})]})]})]})})},"".concat(e.type,"-").concat(e.id))),h.length>0&&(0,t.jsxs)("div",{className:"col-span-full text-center py-8 text-gray-500",children:["Showing all ",h.length," search results"]})]})]})]})}function u(){return(0,t.jsx)(l.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-[#eff1f4] flex items-center justify-center",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-500",children:[(0,t.jsx)("div",{className:"animate-spin h-5 w-5 border-2 border-gray-300 border-t-blue-500 rounded-full"}),(0,t.jsx)("span",{children:"Loading search results..."})]})}),children:(0,t.jsx)(x,{})})}},26126:(e,a,s)=>{"use strict";s.d(a,{E:()=>i});var t=s(95155);s(12115);var r=s(74466),n=s(59434);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:a,variant:s,...r}=e;return(0,t.jsx)("div",{className:(0,n.cn)(l({variant:s}),a),...r})}},29967:(e,a,s)=>{Promise.resolve().then(s.bind(s,8933))},59434:(e,a,s)=>{"use strict";s.d(a,{cn:()=>n});var t=s(52596),r=s(39688);function n(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,r.QP)((0,t.$)(a))}},66695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>i,wL:()=>m});var t=s(95155),r=s(12115),n=s(59434);let l=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});l.displayName="Card";let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...r})});i.displayName="CardHeader";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("h3",{ref:a,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("p",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,n.cn)("p-6",s),...r})});d.displayName="CardContent";let m=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center p-6",s),...r})});m.displayName="CardFooter"}},e=>{var a=a=>e(e.s=a);e.O(0,[5003,7885,8441,1684,7358],()=>a(29967)),_N_E=e.O()}]);