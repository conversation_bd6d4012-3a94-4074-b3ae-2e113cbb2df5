{"kind": "FETCH", "data": {"headers": {"access-control-allow-headers": "Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type", "access-control-expose-headers": "X-WP-Total, X-WP-TotalPages, Link", "allow": "GET", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json; charset=UTF-8", "date": "Wed, 30 Jul 2025 21:41:51 GMT", "link": "<http://tourismiq-headless.local/wp-json/>; rel=\"https://api.w.org/\"", "referrer-policy": "strict-origin-when-cross-origin", "server": "nginx/1.26.1", "transfer-encoding": "chunked", "vary": "Accept-Encoding, Origin", "x-content-type-options": "nosniff", "x-frame-options": "SAMEORIGIN", "x-powered-by": "PHP/8.2.27", "x-robots-tag": "noindex", "x-wp-total": "546", "x-wp-totalpages": "28", "x-xss-protection": "1; mode=block"}, "body": "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", "status": 200, "url": "http://tourismiq-headless.local/wp-json/tourismiq/v1/jobs/search?per_page=20&page=1&orderby=date&order=desc"}, "revalidate": 31536000, "tags": []}