(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1238],{6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>a,t:()=>i});var s=t(12115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function i(...e){return r=>{let t=!1,s=e.map(e=>{let s=n(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():n(e[r],null)}}}}function a(...e){return s.useCallback(i(...e),e)}},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),a=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},c=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:d="",children:u,iconNode:f,...h}=e;return(0,s.createElement)("svg",{ref:r,...o,width:n,height:n,stroke:t,strokeWidth:a?24*Number(i)/Number(n):i,className:l("lucide",d),...!u&&!c(h)&&{"aria-hidden":"true"},...h},[...f.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),u=(e,r)=>{let t=(0,s.forwardRef)((t,i)=>{let{className:c,...o}=t;return(0,s.createElement)(d,{ref:i,iconNode:r,className:l("lucide-".concat(n(a(e))),"lucide-".concat(e),c),...o})});return t.displayName=a(e),t}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>c,r:()=>l});var s=t(95155);t(12115);var n=t(99708),i=t(74466),a=t(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:r,variant:t,size:i,asChild:c=!1,...o}=e,d=c?n.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,a.cn)(l({variant:t,size:i,className:r})),...o})}},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"notFound")&&t.d(r,{notFound:function(){return s.notFound}}),t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},40646:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(52596),n=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,s.$)(r))}},61456:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(95155),n=t(12115),i=t(35695),a=t(51154),l=t(85339),c=t(40646),o=t(30285),d=t(6874),u=t.n(d);function f(){let e=(0,i.useSearchParams)(),r=(0,i.useRouter)(),[t,d]=(0,n.useState)(!0),[f,h]=(0,n.useState)(null),x=e.get("session_id");return((0,n.useEffect)(()=>{if(!x){h("No session ID provided"),d(!1);return}(async()=>{try{let e=decodeURIComponent(x).replace(/[{}]/g,"").trim(),t=await fetch("/api/stripe/verify-session/".concat(e));if(!t.ok){let e=await t.text();throw console.error("Session verification failed:",t.status,e),Error("Failed to verify session: ".concat(t.status," ").concat(e))}let s=await t.json();if(s.success)d(!1),s.vendorSlug?setTimeout(()=>{r.push("/vendors/".concat(s.vendorSlug,"?upgraded=true"))},3e3):setTimeout(()=>{r.push("/vendors?upgraded=true")},3e3);else throw Error(s.message||"Session verification failed")}catch(e){console.error("Error handling success redirect:",e),h("Failed to process upgrade"),d(!1)}})()},[x,r]),t)?(0,s.jsx)("div",{className:"min-h-screen bg-[#eff1f4] flex items-center justify-center",children:(0,s.jsx)("div",{className:"max-w-md w-full mx-auto p-6",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(a.A,{className:"h-12 w-12 text-[#5cc8ff] animate-spin"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Processing Your Upgrade"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We're setting up your premium vendor account. This will only take a moment..."})]})})}):f?(0,s.jsx)("div",{className:"min-h-screen bg-[#eff1f4] flex items-center justify-center",children:(0,s.jsx)("div",{className:"max-w-md w-full mx-auto p-6",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(l.A,{className:"h-12 w-12 text-red-600"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Something went wrong"}),(0,s.jsx)("p",{className:"text-gray-600",children:f}),(0,s.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,s.jsx)(o.$,{asChild:!0,children:(0,s.jsx)(u(),{href:"/vendors",children:"Go to Vendors"})}),(0,s.jsx)(o.$,{variant:"outline",asChild:!0,children:(0,s.jsx)(u(),{href:"/vendor-upgrade",children:"Try Again"})})]})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-[#eff1f4] flex items-center justify-center",children:(0,s.jsx)("div",{className:"max-w-md w-full mx-auto p-6",children:(0,s.jsxs)("div",{className:"text-center space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"bg-green-100 rounded-full p-3",children:(0,s.jsx)(c.A,{className:"h-12 w-12 text-green-600"})})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Upgrade Successful!"}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:"Welcome to your premium vendor account"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 border shadow-sm",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What's next?"}),(0,s.jsxs)("ul",{className:"text-left space-y-2 text-gray-600",children:[(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),(0,s.jsx)("span",{children:"Access premium vendor features"})]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),(0,s.jsx)("span",{children:"Upload cover photos and resources"})]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),(0,s.jsx)("span",{children:"Enhanced profile visibility"})]}),(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),(0,s.jsx)("span",{children:"Priority support"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting you to the vendors directory..."}),(0,s.jsx)(o.$,{asChild:!0,className:"w-full",children:(0,s.jsx)(u(),{href:"/vendors?upgraded=true",children:"View Vendors Directory"})})]})]})})})}function h(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-[#eff1f4] flex items-center justify-center",children:(0,s.jsx)(a.A,{className:"h-8 w-8 animate-spin"})}),children:(0,s.jsx)(f,{})})}},81864:(e,r,t)=>{Promise.resolve().then(t.bind(t,61456))},85339:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,Dc:()=>o,TL:()=>a});var s=t(12115),n=t(6101),i=t(95155);function a(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...i}=e;if(s.isValidElement(t)){var a;let e,l,c=(a=t,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),o=function(e,r){let t={...r};for(let s in r){let n=e[s],i=r[s];/^on[A-Z]/.test(s)?n&&i?t[s]=(...e)=>{let r=i(...e);return n(...e),r}:n&&(t[s]=n):"style"===s?t[s]={...n,...i}:"className"===s&&(t[s]=[n,i].filter(Boolean).join(" "))}return{...e,...t}}(i,t.props);return t.type!==s.Fragment&&(o.ref=r?(0,n.t)(r,c):c),s.cloneElement(t,o)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:n,...a}=e,l=s.Children.toArray(n),c=l.find(d);if(c){let e=c.props.children,n=l.map(r=>r!==c?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,i.jsx)(r,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,i.jsx)(r,{...a,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var l=a("Slot"),c=Symbol("radix.slottable");function o(e){let r=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=c,r}function d(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}}},e=>{var r=r=>e(e.s=r);e.O(0,[5003,6874,8441,1684,7358],()=>r(81864)),_N_E=e.O()}]);