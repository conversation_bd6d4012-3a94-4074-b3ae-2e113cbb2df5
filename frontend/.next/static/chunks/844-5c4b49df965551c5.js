"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[844],{5040:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},17576:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},17580:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},48264:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("presentation",[["path",{d:"M2 3h20",key:"91anmk"}],["path",{d:"M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3",key:"2k9sn8"}],["path",{d:"m7 21 5-5 5 5",key:"bip4we"}]])},48698:(e,r,n)=>{n.d(r,{UC:()=>eQ,q7:()=>e0,JU:()=>e$,ZL:()=>eJ,bL:()=>eW,wv:()=>e1,Pb:()=>e2,G5:()=>e9,ZP:()=>e4,l9:()=>eY});var t=n(12115),o=n(85185),a=n(6101),l=n(46081),u=n(5845),i=n(63655),d=n(37328),s=n(94315),c=n(19178),p=n(92293),f=n(25519),h=n(61285),v=n(38795),m=n(34378),g=n(28905),w=n(89196),y=n(99708),x=n(39033),M=n(38168),C=n(93795),k=n(95155),b=["Enter"," "],j=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",...j],D={ltr:[...b,"ArrowRight"],rtl:[...b,"ArrowLeft"]},_={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[I,E,A]=(0,d.N)(P),[N,O]=(0,l.A)(P,[A,v.Bk,w.RG]),T=(0,v.Bk)(),L=(0,w.RG)(),[S,F]=N(P),[K,G]=N(P),V=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=T(r),[d,c]=t.useState(null),p=t.useRef(!1),f=(0,x.c)(l),h=(0,s.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,k.jsx)(v.bL,{...i,children:(0,k.jsx)(S,{scope:r,open:n,onOpenChange:f,content:d,onContentChange:c,children:(0,k.jsx)(K,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:h,modal:u,children:o})})})};V.displayName=P;var B=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=T(n);return(0,k.jsx)(v.Mz,{...o,...t,ref:r})});B.displayName="MenuAnchor";var U="MenuPortal",[H,q]=N(U,{forceMount:void 0}),z=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=F(U,r);return(0,k.jsx)(H,{scope:r,forceMount:n,children:(0,k.jsx)(g.C,{present:n||a.open,children:(0,k.jsx)(m.Z,{asChild:!0,container:o,children:t})})})};z.displayName=U;var X="MenuContent",[Z,W]=N(X),Y=t.forwardRef((e,r)=>{let n=q(X,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=F(X,e.__scopeMenu),l=G(X,e.__scopeMenu);return(0,k.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:t||a.open,children:(0,k.jsx)(I.Slot,{scope:e.__scopeMenu,children:l.modal?(0,k.jsx)(J,{...o,ref:r}):(0,k.jsx)(Q,{...o,ref:r})})})})}),J=t.forwardRef((e,r)=>{let n=F(X,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,M.Eq)(e)},[]),(0,k.jsx)(ee,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=F(X,e.__scopeMenu);return(0,k.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=(0,y.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:d,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:M,disableOutsideScroll:b,...D}=e,_=F(X,n),P=G(X,n),I=T(n),A=L(n),N=E(n),[O,S]=t.useState(null),K=t.useRef(null),V=(0,a.s)(r,K,_.onContentChange),B=t.useRef(0),U=t.useRef(""),H=t.useRef(0),q=t.useRef(null),z=t.useRef("right"),W=t.useRef(0),Y=b?C.A:t.Fragment,J=e=>{var r,n;let t=U.current+e,o=N().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,l=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,l),i=null==(n=o.find(e=>e.textValue===u))?void 0:n.ref.current;!function e(r){U.current=r,window.clearTimeout(B.current),""!==r&&(B.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,p.Oh)();let Q=t.useCallback(e=>{var r,n;return z.current===(null==(r=q.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,d=l.y,s=u.x,c=u.y;d>t!=c>t&&n<(s-i)*(t-d)/(c-d)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(n=q.current)?void 0:n.area)},[]);return(0,k.jsx)(Z,{scope:n,searchRef:U,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var r;Q(e)||(null==(r=K.current)||r.focus(),S(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:H,onPointerGraceIntentChange:t.useCallback(e=>{q.current=e},[]),children:(0,k.jsx)(Y,{...b?{as:$,allowPinchZoom:!0}:void 0,children:(0,k.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=K.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,k.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:M,children:(0,k.jsx)(w.bL,{asChild:!0,...A,dir:P.dir,orientation:"vertical",loop:l,currentTabStopId:O,onCurrentTabStopIdChange:S,onEntryFocus:(0,o.m)(h,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":e_(_.open),"data-radix-menu-content":"",dir:P.dir,...I,...D,ref:V,style:{outline:"none",...D.style},onKeyDown:(0,o.m)(D.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&J(e.key));let o=K.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),U.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{let r=e.target,n=W.current!==e.clientX;e.currentTarget.contains(r)&&n&&(z.current=e.clientX>W.current?"right":"left",W.current=e.clientX)}))})})})})})})});Y.displayName=X;var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,k.jsx)(i.sG.div,{role:"group",...t,ref:r})});er.displayName="MenuGroup";var en=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,k.jsx)(i.sG.div,{...t,ref:r})});en.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...u}=e,d=t.useRef(null),s=G(et,e.__scopeMenu),c=W(et,e.__scopeMenu),p=(0,a.s)(r,d),f=t.useRef(!1);return(0,k.jsx)(el,{...u,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=d.current;if(!n&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?f.current=!1:s.onClose()}}),onPointerDown:r=>{var n;null==(n=e.onPointerDown)||n.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;n||r&&" "===e.key||b.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var el=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:u,...d}=e,s=W(et,n),c=L(n),p=t.useRef(null),f=(0,a.s)(r,p),[h,v]=t.useState(!1),[m,g]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var r;g((null!=(r=e.textContent)?r:"").trim())}},[d.children]),(0,k.jsx)(I.ItemSlot,{scope:n,disabled:l,textValue:null!=u?u:m,children:(0,k.jsx)(w.q7,{asChild:!0,...c,focusable:!l,children:(0,k.jsx)(i.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...d,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),eu=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,k.jsx)(ev,{scope:e.__scopeMenu,checked:n,children:(0,k.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eP(n)?"mixed":n,...a,ref:r,"data-state":eI(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!eP(n)||!n),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[ed,es]=N(ei,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,x.c)(t);return(0,k.jsx)(ed,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,k.jsx)(er,{...o,ref:r})})});ec.displayName=ei;var ep="MenuRadioItem",ef=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=es(ep,e.__scopeMenu),l=n===a.value;return(0,k.jsx)(ev,{scope:e.__scopeMenu,checked:l,children:(0,k.jsx)(ea,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":eI(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var eh="MenuItemIndicator",[ev,em]=N(eh,{checked:!1}),eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=em(eh,n);return(0,k.jsx)(g.C,{present:t||eP(a.checked)||!0===a.checked,children:(0,k.jsx)(i.sG.span,{...o,ref:r,"data-state":eI(a.checked)})})});eg.displayName=eh;var ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,k.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ew.displayName="MenuSeparator";var ey=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=T(n);return(0,k.jsx)(v.i3,{...o,...t,ref:r})});ey.displayName="MenuArrow";var ex="MenuSub",[eM,eC]=N(ex),ek=e=>{let{__scopeMenu:r,children:n,open:o=!1,onOpenChange:a}=e,l=F(ex,r),u=T(r),[i,d]=t.useState(null),[s,c]=t.useState(null),p=(0,x.c)(a);return t.useEffect(()=>(!1===l.open&&p(!1),()=>p(!1)),[l.open,p]),(0,k.jsx)(v.bL,{...u,children:(0,k.jsx)(S,{scope:r,open:o,onOpenChange:p,content:s,onContentChange:c,children:(0,k.jsx)(eM,{scope:r,contentId:(0,h.B)(),triggerId:(0,h.B)(),trigger:i,onTriggerChange:d,children:n})})})};ek.displayName=ex;var eb="MenuSubTrigger",ej=t.forwardRef((e,r)=>{let n=F(eb,e.__scopeMenu),l=G(eb,e.__scopeMenu),u=eC(eb,e.__scopeMenu),i=W(eb,e.__scopeMenu),d=t.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,k.jsx)(B,{asChild:!0,...p,children:(0,k.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":e_(n.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var t;null==(t=e.onClick)||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eE(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||d.current||(i.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>{var r,t;f();let o=null==(r=n.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(t=n.content)?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&D[l.dir].includes(r.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),r.preventDefault()}})})})});ej.displayName=eb;var eR="MenuSubContent",eD=t.forwardRef((e,r)=>{let n=q(X,e.__scopeMenu),{forceMount:l=n.forceMount,...u}=e,i=F(X,e.__scopeMenu),d=G(X,e.__scopeMenu),s=eC(eR,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(r,c);return(0,k.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:l||i.open,children:(0,k.jsx)(I.Slot,{scope:e.__scopeMenu,children:(0,k.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;d.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=_[d.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null==(t=s.trigger)||t.focus(),e.preventDefault()}})})})})})});function e_(e){return e?"open":"closed"}function eP(e){return"indeterminate"===e}function eI(e){return eP(e)?"indeterminate":e?"checked":"unchecked"}function eE(e){return r=>"mouse"===r.pointerType?e(r):void 0}eD.displayName=eR;var eA="DropdownMenu",[eN,eO]=(0,l.A)(eA,[O]),eT=O(),[eL,eS]=eN(eA),eF=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:d=!0}=e,s=eT(r),c=t.useRef(null),[p,f]=(0,u.i)({prop:a,defaultProp:null!=l&&l,onChange:i,caller:eA});return(0,k.jsx)(eL,{scope:r,triggerId:(0,h.B)(),triggerRef:c,contentId:(0,h.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:d,children:(0,k.jsx)(V,{...s,open:p,onOpenChange:f,dir:o,modal:d,children:n})})};eF.displayName=eA;var eK="DropdownMenuTrigger",eG=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,u=eS(eK,n),d=eT(n);return(0,k.jsx)(B,{asChild:!0,...d,children:(0,k.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eG.displayName=eK;var eV=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eT(r);return(0,k.jsx)(z,{...t,...n})};eV.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eS(eB,n),u=eT(n),i=t.useRef(!1);return(0,k.jsx)(Y,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eU.displayName=eB,t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(er,{...o,...t,ref:r})}).displayName="DropdownMenuGroup";var eH=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(en,{...o,...t,ref:r})});eH.displayName="DropdownMenuLabel";var eq=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(ea,{...o,...t,ref:r})});eq.displayName="DropdownMenuItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(eu,{...o,...t,ref:r})}).displayName="DropdownMenuCheckboxItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(ec,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(ef,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(eg,{...o,...t,ref:r})}).displayName="DropdownMenuItemIndicator";var ez=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(ew,{...o,...t,ref:r})});ez.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(ey,{...o,...t,ref:r})}).displayName="DropdownMenuArrow";var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(ej,{...o,...t,ref:r})});eX.displayName="DropdownMenuSubTrigger";var eZ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,k.jsx)(eD,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eZ.displayName="DropdownMenuSubContent";var eW=eF,eY=eG,eJ=eV,eQ=eU,e$=eH,e0=eq,e1=ez,e2=e=>{let{__scopeDropdownMenu:r,children:n,open:t,onOpenChange:o,defaultOpen:a}=e,l=eT(r),[i,d]=(0,u.i)({prop:t,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,k.jsx)(ek,{...l,open:i,onOpenChange:d,children:n})},e4=eX,e9=eZ},51154:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},57434:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},87949:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])}}]);