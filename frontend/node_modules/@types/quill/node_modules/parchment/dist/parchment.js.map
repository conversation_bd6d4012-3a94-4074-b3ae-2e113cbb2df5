{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap 3c334e19bc1efdeb80ab", "webpack:///./src/registry.ts", "webpack:///./src/attributor/attributor.ts", "webpack:///./src/blot/abstract/container.ts", "webpack:///./src/blot/abstract/format.ts", "webpack:///./src/blot/abstract/leaf.ts", "webpack:///./src/blot/abstract/shadow.ts", "webpack:///./src/attributor/store.ts", "webpack:///./src/attributor/class.ts", "webpack:///./src/attributor/style.ts", "webpack:///./src/parchment.ts", "webpack:///./src/collection/linked-list.ts", "webpack:///./src/blot/scroll.ts", "webpack:///./src/blot/inline.ts", "webpack:///./src/blot/block.ts", "webpack:///./src/blot/embed.ts", "webpack:///./src/blot/text.ts"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;AAEA;AACA;;;;;;;;AC7DA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,8CAA8C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,mBAAmB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;;;;;;;ACnJA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA,iCAAiC,cAAc;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;ACvDA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,WAAW;AAC1C,gCAAgC,2BAA2B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,mBAAmB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,eAAe;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AClQA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;AC1EA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;AC1CA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,gBAAgB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,oBAAoB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;AC3JA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI;AACb;AACA;AACA,CAAC;AACD;;;;;;;;ACrEA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D;AAC1D;AACA;AACA;AACA,CAAC;AACD;;;;;;;;ACvDA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D;AAC1D;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;;;;;;;;ACvDA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;;;;;;;ACpCA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,uBAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,qBAAqB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,mBAAmB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;ACrIA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,gBAAgB;AACnD,iCAAiC,cAAc;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,mBAAmB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,cAAc;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;AChLA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;AC7EA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;ACpEA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;ACxCA;AACA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,mBAAmB;AACtD;AACA;AACA;AACA,+BAA+B,eAAe;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD", "file": "parchment.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Parchment\"] = factory();\n\telse\n\t\troot[\"Parchment\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 9);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 3c334e19bc1efdeb80ab", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ParchmentError = /** @class */ (function (_super) {\n    __extends(ParchmentError, _super);\n    function ParchmentError(message) {\n        var _this = this;\n        message = '[Parchment] ' + message;\n        _this = _super.call(this, message) || this;\n        _this.message = message;\n        _this.name = _this.constructor.name;\n        return _this;\n    }\n    return ParchmentError;\n}(Error));\nexports.ParchmentError = ParchmentError;\nvar attributes = {};\nvar classes = {};\nvar tags = {};\nvar types = {};\nexports.DATA_KEY = '__blot';\nvar Scope;\n(function (Scope) {\n    Scope[Scope[\"TYPE\"] = 3] = \"TYPE\";\n    Scope[Scope[\"LEVEL\"] = 12] = \"LEVEL\";\n    Scope[Scope[\"ATTRIBUTE\"] = 13] = \"ATTRIBUTE\";\n    Scope[Scope[\"BLOT\"] = 14] = \"BLOT\";\n    Scope[Scope[\"INLINE\"] = 7] = \"INLINE\";\n    Scope[Scope[\"BLOCK\"] = 11] = \"BLOCK\";\n    Scope[Scope[\"BLOCK_BLOT\"] = 10] = \"BLOCK_BLOT\";\n    Scope[Scope[\"INLINE_BLOT\"] = 6] = \"INLINE_BLOT\";\n    Scope[Scope[\"BLOCK_ATTRIBUTE\"] = 9] = \"BLOCK_ATTRIBUTE\";\n    Scope[Scope[\"INLINE_ATTRIBUTE\"] = 5] = \"INLINE_ATTRIBUTE\";\n    Scope[Scope[\"ANY\"] = 15] = \"ANY\";\n})(Scope = exports.Scope || (exports.Scope = {}));\nfunction create(input, value) {\n    var match = query(input);\n    if (match == null) {\n        throw new ParchmentError(\"Unable to create \" + input + \" blot\");\n    }\n    var BlotClass = match;\n    var node = \n    // @ts-ignore\n    input instanceof Node || input['nodeType'] === Node.TEXT_NODE ? input : BlotClass.create(value);\n    return new BlotClass(node, value);\n}\nexports.create = create;\nfunction find(node, bubble) {\n    if (bubble === void 0) { bubble = false; }\n    if (node == null)\n        return null;\n    // @ts-ignore\n    if (node[exports.DATA_KEY] != null)\n        return node[exports.DATA_KEY].blot;\n    if (bubble)\n        return find(node.parentNode, bubble);\n    return null;\n}\nexports.find = find;\nfunction query(query, scope) {\n    if (scope === void 0) { scope = Scope.ANY; }\n    var match;\n    if (typeof query === 'string') {\n        match = types[query] || attributes[query];\n        // @ts-ignore\n    }\n    else if (query instanceof Text || query['nodeType'] === Node.TEXT_NODE) {\n        match = types['text'];\n    }\n    else if (typeof query === 'number') {\n        if (query & Scope.LEVEL & Scope.BLOCK) {\n            match = types['block'];\n        }\n        else if (query & Scope.LEVEL & Scope.INLINE) {\n            match = types['inline'];\n        }\n    }\n    else if (query instanceof HTMLElement) {\n        var names = (query.getAttribute('class') || '').split(/\\s+/);\n        for (var i in names) {\n            match = classes[names[i]];\n            if (match)\n                break;\n        }\n        match = match || tags[query.tagName];\n    }\n    if (match == null)\n        return null;\n    // @ts-ignore\n    if (scope & Scope.LEVEL & match.scope && scope & Scope.TYPE & match.scope)\n        return match;\n    return null;\n}\nexports.query = query;\nfunction register() {\n    var Definitions = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        Definitions[_i] = arguments[_i];\n    }\n    if (Definitions.length > 1) {\n        return Definitions.map(function (d) {\n            return register(d);\n        });\n    }\n    var Definition = Definitions[0];\n    if (typeof Definition.blotName !== 'string' && typeof Definition.attrName !== 'string') {\n        throw new ParchmentError('Invalid definition');\n    }\n    else if (Definition.blotName === 'abstract') {\n        throw new ParchmentError('Cannot register abstract class');\n    }\n    types[Definition.blotName || Definition.attrName] = Definition;\n    if (typeof Definition.keyName === 'string') {\n        attributes[Definition.keyName] = Definition;\n    }\n    else {\n        if (Definition.className != null) {\n            classes[Definition.className] = Definition;\n        }\n        if (Definition.tagName != null) {\n            if (Array.isArray(Definition.tagName)) {\n                Definition.tagName = Definition.tagName.map(function (tagName) {\n                    return tagName.toUpperCase();\n                });\n            }\n            else {\n                Definition.tagName = Definition.tagName.toUpperCase();\n            }\n            var tagNames = Array.isArray(Definition.tagName) ? Definition.tagName : [Definition.tagName];\n            tagNames.forEach(function (tag) {\n                if (tags[tag] == null || Definition.className == null) {\n                    tags[tag] = Definition;\n                }\n            });\n        }\n    }\n    return Definition;\n}\nexports.register = register;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/registry.ts\n// module id = 0\n// module chunks = 0", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar Registry = require(\"../registry\");\nvar Attributor = /** @class */ (function () {\n    function Attributor(attrName, keyName, options) {\n        if (options === void 0) { options = {}; }\n        this.attrName = attrName;\n        this.keyName = keyName;\n        var attributeBit = Registry.Scope.TYPE & Registry.Scope.ATTRIBUTE;\n        if (options.scope != null) {\n            // Ignore type bits, force attribute bit\n            this.scope = (options.scope & Registry.Scope.LEVEL) | attributeBit;\n        }\n        else {\n            this.scope = Registry.Scope.ATTRIBUTE;\n        }\n        if (options.whitelist != null)\n            this.whitelist = options.whitelist;\n    }\n    Attributor.keys = function (node) {\n        return [].map.call(node.attributes, function (item) {\n            return item.name;\n        });\n    };\n    Attributor.prototype.add = function (node, value) {\n        if (!this.canAdd(node, value))\n            return false;\n        node.setAttribute(this.keyName, value);\n        return true;\n    };\n    Attributor.prototype.canAdd = function (node, value) {\n        var match = Registry.query(node, Registry.Scope.BLOT & (this.scope | Registry.Scope.TYPE));\n        if (match == null)\n            return false;\n        if (this.whitelist == null)\n            return true;\n        if (typeof value === 'string') {\n            return this.whitelist.indexOf(value.replace(/[\"']/g, '')) > -1;\n        }\n        else {\n            return this.whitelist.indexOf(value) > -1;\n        }\n    };\n    Attributor.prototype.remove = function (node) {\n        node.removeAttribute(this.keyName);\n    };\n    Attributor.prototype.value = function (node) {\n        var value = node.getAttribute(this.keyName);\n        if (this.canAdd(node, value) && value) {\n            return value;\n        }\n        return '';\n    };\n    return Attributor;\n}());\nexports.default = Attributor;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/attributor/attributor.ts\n// module id = 1\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar linked_list_1 = require(\"../../collection/linked-list\");\nvar shadow_1 = require(\"./shadow\");\nvar Registry = require(\"../../registry\");\nvar ContainerBlot = /** @class */ (function (_super) {\n    __extends(ContainerBlot, _super);\n    function ContainerBlot(domNode) {\n        var _this = _super.call(this, domNode) || this;\n        _this.build();\n        return _this;\n    }\n    ContainerBlot.prototype.appendChild = function (other) {\n        this.insertBefore(other);\n    };\n    ContainerBlot.prototype.attach = function () {\n        _super.prototype.attach.call(this);\n        this.children.forEach(function (child) {\n            child.attach();\n        });\n    };\n    ContainerBlot.prototype.build = function () {\n        var _this = this;\n        this.children = new linked_list_1.default();\n        // Need to be reversed for if DOM nodes already in order\n        [].slice\n            .call(this.domNode.childNodes)\n            .reverse()\n            .forEach(function (node) {\n            try {\n                var child = makeBlot(node);\n                _this.insertBefore(child, _this.children.head || undefined);\n            }\n            catch (err) {\n                if (err instanceof Registry.ParchmentError)\n                    return;\n                else\n                    throw err;\n            }\n        });\n    };\n    ContainerBlot.prototype.deleteAt = function (index, length) {\n        if (index === 0 && length === this.length()) {\n            return this.remove();\n        }\n        this.children.forEachAt(index, length, function (child, offset, length) {\n            child.deleteAt(offset, length);\n        });\n    };\n    ContainerBlot.prototype.descendant = function (criteria, index) {\n        var _a = this.children.find(index), child = _a[0], offset = _a[1];\n        if ((criteria.blotName == null && criteria(child)) ||\n            (criteria.blotName != null && child instanceof criteria)) {\n            return [child, offset];\n        }\n        else if (child instanceof ContainerBlot) {\n            return child.descendant(criteria, offset);\n        }\n        else {\n            return [null, -1];\n        }\n    };\n    ContainerBlot.prototype.descendants = function (criteria, index, length) {\n        if (index === void 0) { index = 0; }\n        if (length === void 0) { length = Number.MAX_VALUE; }\n        var descendants = [];\n        var lengthLeft = length;\n        this.children.forEachAt(index, length, function (child, index, length) {\n            if ((criteria.blotName == null && criteria(child)) ||\n                (criteria.blotName != null && child instanceof criteria)) {\n                descendants.push(child);\n            }\n            if (child instanceof ContainerBlot) {\n                descendants = descendants.concat(child.descendants(criteria, index, lengthLeft));\n            }\n            lengthLeft -= length;\n        });\n        return descendants;\n    };\n    ContainerBlot.prototype.detach = function () {\n        this.children.forEach(function (child) {\n            child.detach();\n        });\n        _super.prototype.detach.call(this);\n    };\n    ContainerBlot.prototype.formatAt = function (index, length, name, value) {\n        this.children.forEachAt(index, length, function (child, offset, length) {\n            child.formatAt(offset, length, name, value);\n        });\n    };\n    ContainerBlot.prototype.insertAt = function (index, value, def) {\n        var _a = this.children.find(index), child = _a[0], offset = _a[1];\n        if (child) {\n            child.insertAt(offset, value, def);\n        }\n        else {\n            var blot = def == null ? Registry.create('text', value) : Registry.create(value, def);\n            this.appendChild(blot);\n        }\n    };\n    ContainerBlot.prototype.insertBefore = function (childBlot, refBlot) {\n        if (this.statics.allowedChildren != null &&\n            !this.statics.allowedChildren.some(function (child) {\n                return childBlot instanceof child;\n            })) {\n            throw new Registry.ParchmentError(\"Cannot insert \" + childBlot.statics.blotName + \" into \" + this.statics.blotName);\n        }\n        childBlot.insertInto(this, refBlot);\n    };\n    ContainerBlot.prototype.length = function () {\n        return this.children.reduce(function (memo, child) {\n            return memo + child.length();\n        }, 0);\n    };\n    ContainerBlot.prototype.moveChildren = function (targetParent, refNode) {\n        this.children.forEach(function (child) {\n            targetParent.insertBefore(child, refNode);\n        });\n    };\n    ContainerBlot.prototype.optimize = function (context) {\n        _super.prototype.optimize.call(this, context);\n        if (this.children.length === 0) {\n            if (this.statics.defaultChild != null) {\n                var child = Registry.create(this.statics.defaultChild);\n                this.appendChild(child);\n                child.optimize(context);\n            }\n            else {\n                this.remove();\n            }\n        }\n    };\n    ContainerBlot.prototype.path = function (index, inclusive) {\n        if (inclusive === void 0) { inclusive = false; }\n        var _a = this.children.find(index, inclusive), child = _a[0], offset = _a[1];\n        var position = [[this, index]];\n        if (child instanceof ContainerBlot) {\n            return position.concat(child.path(offset, inclusive));\n        }\n        else if (child != null) {\n            position.push([child, offset]);\n        }\n        return position;\n    };\n    ContainerBlot.prototype.removeChild = function (child) {\n        this.children.remove(child);\n    };\n    ContainerBlot.prototype.replace = function (target) {\n        if (target instanceof ContainerBlot) {\n            target.moveChildren(this);\n        }\n        _super.prototype.replace.call(this, target);\n    };\n    ContainerBlot.prototype.split = function (index, force) {\n        if (force === void 0) { force = false; }\n        if (!force) {\n            if (index === 0)\n                return this;\n            if (index === this.length())\n                return this.next;\n        }\n        var after = this.clone();\n        this.parent.insertBefore(after, this.next);\n        this.children.forEachAt(index, this.length(), function (child, offset, length) {\n            child = child.split(offset, force);\n            after.appendChild(child);\n        });\n        return after;\n    };\n    ContainerBlot.prototype.unwrap = function () {\n        this.moveChildren(this.parent, this.next);\n        this.remove();\n    };\n    ContainerBlot.prototype.update = function (mutations, context) {\n        var _this = this;\n        var addedNodes = [];\n        var removedNodes = [];\n        mutations.forEach(function (mutation) {\n            if (mutation.target === _this.domNode && mutation.type === 'childList') {\n                addedNodes.push.apply(addedNodes, mutation.addedNodes);\n                removedNodes.push.apply(removedNodes, mutation.removedNodes);\n            }\n        });\n        removedNodes.forEach(function (node) {\n            // Check node has actually been removed\n            // One exception is Chrome does not immediately remove IFRAMEs\n            // from DOM but MutationRecord is correct in its reported removal\n            if (node.parentNode != null &&\n                // @ts-ignore\n                node.tagName !== 'IFRAME' &&\n                document.body.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n                return;\n            }\n            var blot = Registry.find(node);\n            if (blot == null)\n                return;\n            if (blot.domNode.parentNode == null || blot.domNode.parentNode === _this.domNode) {\n                blot.detach();\n            }\n        });\n        addedNodes\n            .filter(function (node) {\n            return node.parentNode == _this.domNode;\n        })\n            .sort(function (a, b) {\n            if (a === b)\n                return 0;\n            if (a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING) {\n                return 1;\n            }\n            return -1;\n        })\n            .forEach(function (node) {\n            var refBlot = null;\n            if (node.nextSibling != null) {\n                refBlot = Registry.find(node.nextSibling);\n            }\n            var blot = makeBlot(node);\n            if (blot.next != refBlot || blot.next == null) {\n                if (blot.parent != null) {\n                    blot.parent.removeChild(_this);\n                }\n                _this.insertBefore(blot, refBlot || undefined);\n            }\n        });\n    };\n    return ContainerBlot;\n}(shadow_1.default));\nfunction makeBlot(node) {\n    var blot = Registry.find(node);\n    if (blot == null) {\n        try {\n            blot = Registry.create(node);\n        }\n        catch (e) {\n            blot = Registry.create(Registry.Scope.INLINE);\n            [].slice.call(node.childNodes).forEach(function (child) {\n                // @ts-ignore\n                blot.domNode.appendChild(child);\n            });\n            if (node.parentNode) {\n                node.parentNode.replaceChild(blot.domNode, node);\n            }\n            blot.attach();\n        }\n    }\n    return blot;\n}\nexports.default = ContainerBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/abstract/container.ts\n// module id = 2\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar attributor_1 = require(\"../../attributor/attributor\");\nvar store_1 = require(\"../../attributor/store\");\nvar container_1 = require(\"./container\");\nvar Registry = require(\"../../registry\");\nvar FormatBlot = /** @class */ (function (_super) {\n    __extends(FormatBlot, _super);\n    function FormatBlot(domNode) {\n        var _this = _super.call(this, domNode) || this;\n        _this.attributes = new store_1.default(_this.domNode);\n        return _this;\n    }\n    FormatBlot.formats = function (domNode) {\n        if (typeof this.tagName === 'string') {\n            return true;\n        }\n        else if (Array.isArray(this.tagName)) {\n            return domNode.tagName.toLowerCase();\n        }\n        return undefined;\n    };\n    FormatBlot.prototype.format = function (name, value) {\n        var format = Registry.query(name);\n        if (format instanceof attributor_1.default) {\n            this.attributes.attribute(format, value);\n        }\n        else if (value) {\n            if (format != null && (name !== this.statics.blotName || this.formats()[name] !== value)) {\n                this.replaceWith(name, value);\n            }\n        }\n    };\n    FormatBlot.prototype.formats = function () {\n        var formats = this.attributes.values();\n        var format = this.statics.formats(this.domNode);\n        if (format != null) {\n            formats[this.statics.blotName] = format;\n        }\n        return formats;\n    };\n    FormatBlot.prototype.replaceWith = function (name, value) {\n        var replacement = _super.prototype.replaceWith.call(this, name, value);\n        this.attributes.copy(replacement);\n        return replacement;\n    };\n    FormatBlot.prototype.update = function (mutations, context) {\n        var _this = this;\n        _super.prototype.update.call(this, mutations, context);\n        if (mutations.some(function (mutation) {\n            return mutation.target === _this.domNode && mutation.type === 'attributes';\n        })) {\n            this.attributes.build();\n        }\n    };\n    FormatBlot.prototype.wrap = function (name, value) {\n        var wrapper = _super.prototype.wrap.call(this, name, value);\n        if (wrapper instanceof FormatBlot && wrapper.statics.scope === this.statics.scope) {\n            this.attributes.move(wrapper);\n        }\n        return wrapper;\n    };\n    return FormatBlot;\n}(container_1.default));\nexports.default = FormatBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/abstract/format.ts\n// module id = 3\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar shadow_1 = require(\"./shadow\");\nvar Registry = require(\"../../registry\");\nvar LeafBlot = /** @class */ (function (_super) {\n    __extends(LeafBlot, _super);\n    function LeafBlot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    LeafBlot.value = function (domNode) {\n        return true;\n    };\n    LeafBlot.prototype.index = function (node, offset) {\n        if (this.domNode === node ||\n            this.domNode.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n            return Math.min(offset, 1);\n        }\n        return -1;\n    };\n    LeafBlot.prototype.position = function (index, inclusive) {\n        var offset = [].indexOf.call(this.parent.domNode.childNodes, this.domNode);\n        if (index > 0)\n            offset += 1;\n        return [this.parent.domNode, offset];\n    };\n    LeafBlot.prototype.value = function () {\n        return _a = {}, _a[this.statics.blotName] = this.statics.value(this.domNode) || true, _a;\n        var _a;\n    };\n    LeafBlot.scope = Registry.Scope.INLINE_BLOT;\n    return LeafBlot;\n}(shadow_1.default));\nexports.default = LeafBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/abstract/leaf.ts\n// module id = 4\n// module chunks = 0", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar Registry = require(\"../../registry\");\nvar ShadowBlot = /** @class */ (function () {\n    function ShadowBlot(domNode) {\n        this.domNode = domNode;\n        // @ts-ignore\n        this.domNode[Registry.DATA_KEY] = { blot: this };\n    }\n    Object.defineProperty(ShadowBlot.prototype, \"statics\", {\n        // Hack for accessing inherited static methods\n        get: function () {\n            return this.constructor;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    ShadowBlot.create = function (value) {\n        if (this.tagName == null) {\n            throw new Registry.ParchmentError('Blot definition missing tagName');\n        }\n        var node;\n        if (Array.isArray(this.tagName)) {\n            if (typeof value === 'string') {\n                value = value.toUpperCase();\n                if (parseInt(value).toString() === value) {\n                    value = parseInt(value);\n                }\n            }\n            if (typeof value === 'number') {\n                node = document.createElement(this.tagName[value - 1]);\n            }\n            else if (this.tagName.indexOf(value) > -1) {\n                node = document.createElement(value);\n            }\n            else {\n                node = document.createElement(this.tagName[0]);\n            }\n        }\n        else {\n            node = document.createElement(this.tagName);\n        }\n        if (this.className) {\n            node.classList.add(this.className);\n        }\n        return node;\n    };\n    ShadowBlot.prototype.attach = function () {\n        if (this.parent != null) {\n            this.scroll = this.parent.scroll;\n        }\n    };\n    ShadowBlot.prototype.clone = function () {\n        var domNode = this.domNode.cloneNode(false);\n        return Registry.create(domNode);\n    };\n    ShadowBlot.prototype.detach = function () {\n        if (this.parent != null)\n            this.parent.removeChild(this);\n        // @ts-ignore\n        delete this.domNode[Registry.DATA_KEY];\n    };\n    ShadowBlot.prototype.deleteAt = function (index, length) {\n        var blot = this.isolate(index, length);\n        blot.remove();\n    };\n    ShadowBlot.prototype.formatAt = function (index, length, name, value) {\n        var blot = this.isolate(index, length);\n        if (Registry.query(name, Registry.Scope.BLOT) != null && value) {\n            blot.wrap(name, value);\n        }\n        else if (Registry.query(name, Registry.Scope.ATTRIBUTE) != null) {\n            var parent_1 = Registry.create(this.statics.scope);\n            blot.wrap(parent_1);\n            parent_1.format(name, value);\n        }\n    };\n    ShadowBlot.prototype.insertAt = function (index, value, def) {\n        var blot = def == null ? Registry.create('text', value) : Registry.create(value, def);\n        var ref = this.split(index);\n        this.parent.insertBefore(blot, ref);\n    };\n    ShadowBlot.prototype.insertInto = function (parentBlot, refBlot) {\n        if (refBlot === void 0) { refBlot = null; }\n        if (this.parent != null) {\n            this.parent.children.remove(this);\n        }\n        var refDomNode = null;\n        parentBlot.children.insertBefore(this, refBlot);\n        if (refBlot != null) {\n            refDomNode = refBlot.domNode;\n        }\n        if (this.domNode.parentNode != parentBlot.domNode ||\n            this.domNode.nextSibling != refDomNode) {\n            parentBlot.domNode.insertBefore(this.domNode, refDomNode);\n        }\n        this.parent = parentBlot;\n        this.attach();\n    };\n    ShadowBlot.prototype.isolate = function (index, length) {\n        var target = this.split(index);\n        target.split(length);\n        return target;\n    };\n    ShadowBlot.prototype.length = function () {\n        return 1;\n    };\n    ShadowBlot.prototype.offset = function (root) {\n        if (root === void 0) { root = this.parent; }\n        if (this.parent == null || this == root)\n            return 0;\n        return this.parent.children.offset(this) + this.parent.offset(root);\n    };\n    ShadowBlot.prototype.optimize = function (context) {\n        // TODO clean up once we use WeakMap\n        // @ts-ignore\n        if (this.domNode[Registry.DATA_KEY] != null) {\n            // @ts-ignore\n            delete this.domNode[Registry.DATA_KEY].mutations;\n        }\n    };\n    ShadowBlot.prototype.remove = function () {\n        if (this.domNode.parentNode != null) {\n            this.domNode.parentNode.removeChild(this.domNode);\n        }\n        this.detach();\n    };\n    ShadowBlot.prototype.replace = function (target) {\n        if (target.parent == null)\n            return;\n        target.parent.insertBefore(this, target.next);\n        target.remove();\n    };\n    ShadowBlot.prototype.replaceWith = function (name, value) {\n        var replacement = typeof name === 'string' ? Registry.create(name, value) : name;\n        replacement.replace(this);\n        return replacement;\n    };\n    ShadowBlot.prototype.split = function (index, force) {\n        return index === 0 ? this : this.next;\n    };\n    ShadowBlot.prototype.update = function (mutations, context) {\n        // Nothing to do by default\n    };\n    ShadowBlot.prototype.wrap = function (name, value) {\n        var wrapper = typeof name === 'string' ? Registry.create(name, value) : name;\n        if (this.parent != null) {\n            this.parent.insertBefore(wrapper, this.next);\n        }\n        wrapper.appendChild(this);\n        return wrapper;\n    };\n    ShadowBlot.blotName = 'abstract';\n    return ShadowBlot;\n}());\nexports.default = ShadowBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/abstract/shadow.ts\n// module id = 5\n// module chunks = 0", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar attributor_1 = require(\"./attributor\");\nvar class_1 = require(\"./class\");\nvar style_1 = require(\"./style\");\nvar Registry = require(\"../registry\");\nvar AttributorStore = /** @class */ (function () {\n    function AttributorStore(domNode) {\n        this.attributes = {};\n        this.domNode = domNode;\n        this.build();\n    }\n    AttributorStore.prototype.attribute = function (attribute, value) {\n        // verb\n        if (value) {\n            if (attribute.add(this.domNode, value)) {\n                if (attribute.value(this.domNode) != null) {\n                    this.attributes[attribute.attrName] = attribute;\n                }\n                else {\n                    delete this.attributes[attribute.attrName];\n                }\n            }\n        }\n        else {\n            attribute.remove(this.domNode);\n            delete this.attributes[attribute.attrName];\n        }\n    };\n    AttributorStore.prototype.build = function () {\n        var _this = this;\n        this.attributes = {};\n        var attributes = attributor_1.default.keys(this.domNode);\n        var classes = class_1.default.keys(this.domNode);\n        var styles = style_1.default.keys(this.domNode);\n        attributes\n            .concat(classes)\n            .concat(styles)\n            .forEach(function (name) {\n            var attr = Registry.query(name, Registry.Scope.ATTRIBUTE);\n            if (attr instanceof attributor_1.default) {\n                _this.attributes[attr.attrName] = attr;\n            }\n        });\n    };\n    AttributorStore.prototype.copy = function (target) {\n        var _this = this;\n        Object.keys(this.attributes).forEach(function (key) {\n            var value = _this.attributes[key].value(_this.domNode);\n            target.format(key, value);\n        });\n    };\n    AttributorStore.prototype.move = function (target) {\n        var _this = this;\n        this.copy(target);\n        Object.keys(this.attributes).forEach(function (key) {\n            _this.attributes[key].remove(_this.domNode);\n        });\n        this.attributes = {};\n    };\n    AttributorStore.prototype.values = function () {\n        var _this = this;\n        return Object.keys(this.attributes).reduce(function (attributes, name) {\n            attributes[name] = _this.attributes[name].value(_this.domNode);\n            return attributes;\n        }, {});\n    };\n    return AttributorStore;\n}());\nexports.default = AttributorStore;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/attributor/store.ts\n// module id = 6\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar attributor_1 = require(\"./attributor\");\nfunction match(node, prefix) {\n    var className = node.getAttribute('class') || '';\n    return className.split(/\\s+/).filter(function (name) {\n        return name.indexOf(prefix + \"-\") === 0;\n    });\n}\nvar ClassAttributor = /** @class */ (function (_super) {\n    __extends(ClassAttributor, _super);\n    function ClassAttributor() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ClassAttributor.keys = function (node) {\n        return (node.getAttribute('class') || '').split(/\\s+/).map(function (name) {\n            return name\n                .split('-')\n                .slice(0, -1)\n                .join('-');\n        });\n    };\n    ClassAttributor.prototype.add = function (node, value) {\n        if (!this.canAdd(node, value))\n            return false;\n        this.remove(node);\n        node.classList.add(this.keyName + \"-\" + value);\n        return true;\n    };\n    ClassAttributor.prototype.remove = function (node) {\n        var matches = match(node, this.keyName);\n        matches.forEach(function (name) {\n            node.classList.remove(name);\n        });\n        if (node.classList.length === 0) {\n            node.removeAttribute('class');\n        }\n    };\n    ClassAttributor.prototype.value = function (node) {\n        var result = match(node, this.keyName)[0] || '';\n        var value = result.slice(this.keyName.length + 1); // +1 for hyphen\n        return this.canAdd(node, value) ? value : '';\n    };\n    return ClassAttributor;\n}(attributor_1.default));\nexports.default = ClassAttributor;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/attributor/class.ts\n// module id = 7\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar attributor_1 = require(\"./attributor\");\nfunction camelize(name) {\n    var parts = name.split('-');\n    var rest = parts\n        .slice(1)\n        .map(function (part) {\n        return part[0].toUpperCase() + part.slice(1);\n    })\n        .join('');\n    return parts[0] + rest;\n}\nvar StyleAttributor = /** @class */ (function (_super) {\n    __extends(StyleAttributor, _super);\n    function StyleAttributor() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    StyleAttributor.keys = function (node) {\n        return (node.getAttribute('style') || '').split(';').map(function (value) {\n            var arr = value.split(':');\n            return arr[0].trim();\n        });\n    };\n    StyleAttributor.prototype.add = function (node, value) {\n        if (!this.canAdd(node, value))\n            return false;\n        // @ts-ignore\n        node.style[camelize(this.keyName)] = value;\n        return true;\n    };\n    StyleAttributor.prototype.remove = function (node) {\n        // @ts-ignore\n        node.style[camelize(this.keyName)] = '';\n        if (!node.getAttribute('style')) {\n            node.removeAttribute('style');\n        }\n    };\n    StyleAttributor.prototype.value = function (node) {\n        // @ts-ignore\n        var value = node.style[camelize(this.keyName)];\n        return this.canAdd(node, value) ? value : '';\n    };\n    return StyleAttributor;\n}(attributor_1.default));\nexports.default = StyleAttributor;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/attributor/style.ts\n// module id = 8\n// module chunks = 0", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar container_1 = require(\"./blot/abstract/container\");\nvar format_1 = require(\"./blot/abstract/format\");\nvar leaf_1 = require(\"./blot/abstract/leaf\");\nvar scroll_1 = require(\"./blot/scroll\");\nvar inline_1 = require(\"./blot/inline\");\nvar block_1 = require(\"./blot/block\");\nvar embed_1 = require(\"./blot/embed\");\nvar text_1 = require(\"./blot/text\");\nvar attributor_1 = require(\"./attributor/attributor\");\nvar class_1 = require(\"./attributor/class\");\nvar style_1 = require(\"./attributor/style\");\nvar store_1 = require(\"./attributor/store\");\nvar Registry = require(\"./registry\");\nvar Parchment = {\n    Scope: Registry.Scope,\n    create: Registry.create,\n    find: Registry.find,\n    query: Registry.query,\n    register: Registry.register,\n    Container: container_1.default,\n    Format: format_1.default,\n    Leaf: leaf_1.default,\n    Embed: embed_1.default,\n    Scroll: scroll_1.default,\n    Block: block_1.default,\n    Inline: inline_1.default,\n    Text: text_1.default,\n    Attributor: {\n        Attribute: attributor_1.default,\n        Class: class_1.default,\n        Style: style_1.default,\n        Store: store_1.default,\n    },\n};\nexports.default = Parchment;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/parchment.ts\n// module id = 10\n// module chunks = 0", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar LinkedList = /** @class */ (function () {\n    function LinkedList() {\n        this.head = this.tail = null;\n        this.length = 0;\n    }\n    LinkedList.prototype.append = function () {\n        var nodes = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            nodes[_i] = arguments[_i];\n        }\n        this.insertBefore(nodes[0], null);\n        if (nodes.length > 1) {\n            this.append.apply(this, nodes.slice(1));\n        }\n    };\n    LinkedList.prototype.contains = function (node) {\n        var cur, next = this.iterator();\n        while ((cur = next())) {\n            if (cur === node)\n                return true;\n        }\n        return false;\n    };\n    LinkedList.prototype.insertBefore = function (node, refNode) {\n        if (!node)\n            return;\n        node.next = refNode;\n        if (refNode != null) {\n            node.prev = refNode.prev;\n            if (refNode.prev != null) {\n                refNode.prev.next = node;\n            }\n            refNode.prev = node;\n            if (refNode === this.head) {\n                this.head = node;\n            }\n        }\n        else if (this.tail != null) {\n            this.tail.next = node;\n            node.prev = this.tail;\n            this.tail = node;\n        }\n        else {\n            node.prev = null;\n            this.head = this.tail = node;\n        }\n        this.length += 1;\n    };\n    LinkedList.prototype.offset = function (target) {\n        var index = 0, cur = this.head;\n        while (cur != null) {\n            if (cur === target)\n                return index;\n            index += cur.length();\n            cur = cur.next;\n        }\n        return -1;\n    };\n    LinkedList.prototype.remove = function (node) {\n        if (!this.contains(node))\n            return;\n        if (node.prev != null)\n            node.prev.next = node.next;\n        if (node.next != null)\n            node.next.prev = node.prev;\n        if (node === this.head)\n            this.head = node.next;\n        if (node === this.tail)\n            this.tail = node.prev;\n        this.length -= 1;\n    };\n    LinkedList.prototype.iterator = function (curNode) {\n        if (curNode === void 0) { curNode = this.head; }\n        // TODO use yield when we can\n        return function () {\n            var ret = curNode;\n            if (curNode != null)\n                curNode = curNode.next;\n            return ret;\n        };\n    };\n    LinkedList.prototype.find = function (index, inclusive) {\n        if (inclusive === void 0) { inclusive = false; }\n        var cur, next = this.iterator();\n        while ((cur = next())) {\n            var length_1 = cur.length();\n            if (index < length_1 ||\n                (inclusive && index === length_1 && (cur.next == null || cur.next.length() !== 0))) {\n                return [cur, index];\n            }\n            index -= length_1;\n        }\n        return [null, 0];\n    };\n    LinkedList.prototype.forEach = function (callback) {\n        var cur, next = this.iterator();\n        while ((cur = next())) {\n            callback(cur);\n        }\n    };\n    LinkedList.prototype.forEachAt = function (index, length, callback) {\n        if (length <= 0)\n            return;\n        var _a = this.find(index), startNode = _a[0], offset = _a[1];\n        var cur, curIndex = index - offset, next = this.iterator(startNode);\n        while ((cur = next()) && curIndex < index + length) {\n            var curLength = cur.length();\n            if (index > curIndex) {\n                callback(cur, index - curIndex, Math.min(length, curIndex + curLength - index));\n            }\n            else {\n                callback(cur, 0, Math.min(curLength, index + length - curIndex));\n            }\n            curIndex += curLength;\n        }\n    };\n    LinkedList.prototype.map = function (callback) {\n        return this.reduce(function (memo, cur) {\n            memo.push(callback(cur));\n            return memo;\n        }, []);\n    };\n    LinkedList.prototype.reduce = function (callback, memo) {\n        var cur, next = this.iterator();\n        while ((cur = next())) {\n            memo = callback(memo, cur);\n        }\n        return memo;\n    };\n    return LinkedList;\n}());\nexports.default = LinkedList;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/collection/linked-list.ts\n// module id = 11\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar container_1 = require(\"./abstract/container\");\nvar Registry = require(\"../registry\");\nvar OBSERVER_CONFIG = {\n    attributes: true,\n    characterData: true,\n    characterDataOldValue: true,\n    childList: true,\n    subtree: true,\n};\nvar MAX_OPTIMIZE_ITERATIONS = 100;\nvar ScrollBlot = /** @class */ (function (_super) {\n    __extends(ScrollBlot, _super);\n    function ScrollBlot(node) {\n        var _this = _super.call(this, node) || this;\n        _this.scroll = _this;\n        _this.observer = new MutationObserver(function (mutations) {\n            _this.update(mutations);\n        });\n        _this.observer.observe(_this.domNode, OBSERVER_CONFIG);\n        _this.attach();\n        return _this;\n    }\n    ScrollBlot.prototype.detach = function () {\n        _super.prototype.detach.call(this);\n        this.observer.disconnect();\n    };\n    ScrollBlot.prototype.deleteAt = function (index, length) {\n        this.update();\n        if (index === 0 && length === this.length()) {\n            this.children.forEach(function (child) {\n                child.remove();\n            });\n        }\n        else {\n            _super.prototype.deleteAt.call(this, index, length);\n        }\n    };\n    ScrollBlot.prototype.formatAt = function (index, length, name, value) {\n        this.update();\n        _super.prototype.formatAt.call(this, index, length, name, value);\n    };\n    ScrollBlot.prototype.insertAt = function (index, value, def) {\n        this.update();\n        _super.prototype.insertAt.call(this, index, value, def);\n    };\n    ScrollBlot.prototype.optimize = function (mutations, context) {\n        var _this = this;\n        if (mutations === void 0) { mutations = []; }\n        if (context === void 0) { context = {}; }\n        _super.prototype.optimize.call(this, context);\n        // We must modify mutations directly, cannot make copy and then modify\n        var records = [].slice.call(this.observer.takeRecords());\n        // Array.push currently seems to be implemented by a non-tail recursive function\n        // so we cannot just mutations.push.apply(mutations, this.observer.takeRecords());\n        while (records.length > 0)\n            mutations.push(records.pop());\n        // TODO use WeakMap\n        var mark = function (blot, markParent) {\n            if (markParent === void 0) { markParent = true; }\n            if (blot == null || blot === _this)\n                return;\n            if (blot.domNode.parentNode == null)\n                return;\n            // @ts-ignore\n            if (blot.domNode[Registry.DATA_KEY].mutations == null) {\n                // @ts-ignore\n                blot.domNode[Registry.DATA_KEY].mutations = [];\n            }\n            if (markParent)\n                mark(blot.parent);\n        };\n        var optimize = function (blot) {\n            // Post-order traversal\n            if (\n            // @ts-ignore\n            blot.domNode[Registry.DATA_KEY] == null ||\n                // @ts-ignore\n                blot.domNode[Registry.DATA_KEY].mutations == null) {\n                return;\n            }\n            if (blot instanceof container_1.default) {\n                blot.children.forEach(optimize);\n            }\n            blot.optimize(context);\n        };\n        var remaining = mutations;\n        for (var i = 0; remaining.length > 0; i += 1) {\n            if (i >= MAX_OPTIMIZE_ITERATIONS) {\n                throw new Error('[Parchment] Maximum optimize iterations reached');\n            }\n            remaining.forEach(function (mutation) {\n                var blot = Registry.find(mutation.target, true);\n                if (blot == null)\n                    return;\n                if (blot.domNode === mutation.target) {\n                    if (mutation.type === 'childList') {\n                        mark(Registry.find(mutation.previousSibling, false));\n                        [].forEach.call(mutation.addedNodes, function (node) {\n                            var child = Registry.find(node, false);\n                            mark(child, false);\n                            if (child instanceof container_1.default) {\n                                child.children.forEach(function (grandChild) {\n                                    mark(grandChild, false);\n                                });\n                            }\n                        });\n                    }\n                    else if (mutation.type === 'attributes') {\n                        mark(blot.prev);\n                    }\n                }\n                mark(blot);\n            });\n            this.children.forEach(optimize);\n            remaining = [].slice.call(this.observer.takeRecords());\n            records = remaining.slice();\n            while (records.length > 0)\n                mutations.push(records.pop());\n        }\n    };\n    ScrollBlot.prototype.update = function (mutations, context) {\n        var _this = this;\n        if (context === void 0) { context = {}; }\n        mutations = mutations || this.observer.takeRecords();\n        // TODO use WeakMap\n        mutations\n            .map(function (mutation) {\n            var blot = Registry.find(mutation.target, true);\n            if (blot == null)\n                return null;\n            // @ts-ignore\n            if (blot.domNode[Registry.DATA_KEY].mutations == null) {\n                // @ts-ignore\n                blot.domNode[Registry.DATA_KEY].mutations = [mutation];\n                return blot;\n            }\n            else {\n                // @ts-ignore\n                blot.domNode[Registry.DATA_KEY].mutations.push(mutation);\n                return null;\n            }\n        })\n            .forEach(function (blot) {\n            if (blot == null ||\n                blot === _this ||\n                //@ts-ignore\n                blot.domNode[Registry.DATA_KEY] == null)\n                return;\n            // @ts-ignore\n            blot.update(blot.domNode[Registry.DATA_KEY].mutations || [], context);\n        });\n        // @ts-ignore\n        if (this.domNode[Registry.DATA_KEY].mutations != null) {\n            // @ts-ignore\n            _super.prototype.update.call(this, this.domNode[Registry.DATA_KEY].mutations, context);\n        }\n        this.optimize(mutations, context);\n    };\n    ScrollBlot.blotName = 'scroll';\n    ScrollBlot.defaultChild = 'block';\n    ScrollBlot.scope = Registry.Scope.BLOCK_BLOT;\n    ScrollBlot.tagName = 'DIV';\n    return ScrollBlot;\n}(container_1.default));\nexports.default = ScrollBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/scroll.ts\n// module id = 12\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar format_1 = require(\"./abstract/format\");\nvar Registry = require(\"../registry\");\n// Shallow object comparison\nfunction isEqual(obj1, obj2) {\n    if (Object.keys(obj1).length !== Object.keys(obj2).length)\n        return false;\n    // @ts-ignore\n    for (var prop in obj1) {\n        // @ts-ignore\n        if (obj1[prop] !== obj2[prop])\n            return false;\n    }\n    return true;\n}\nvar InlineBlot = /** @class */ (function (_super) {\n    __extends(InlineBlot, _super);\n    function InlineBlot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    InlineBlot.formats = function (domNode) {\n        if (domNode.tagName === InlineBlot.tagName)\n            return undefined;\n        return _super.formats.call(this, domNode);\n    };\n    InlineBlot.prototype.format = function (name, value) {\n        var _this = this;\n        if (name === this.statics.blotName && !value) {\n            this.children.forEach(function (child) {\n                if (!(child instanceof format_1.default)) {\n                    child = child.wrap(InlineBlot.blotName, true);\n                }\n                _this.attributes.copy(child);\n            });\n            this.unwrap();\n        }\n        else {\n            _super.prototype.format.call(this, name, value);\n        }\n    };\n    InlineBlot.prototype.formatAt = function (index, length, name, value) {\n        if (this.formats()[name] != null || Registry.query(name, Registry.Scope.ATTRIBUTE)) {\n            var blot = this.isolate(index, length);\n            blot.format(name, value);\n        }\n        else {\n            _super.prototype.formatAt.call(this, index, length, name, value);\n        }\n    };\n    InlineBlot.prototype.optimize = function (context) {\n        _super.prototype.optimize.call(this, context);\n        var formats = this.formats();\n        if (Object.keys(formats).length === 0) {\n            return this.unwrap(); // unformatted span\n        }\n        var next = this.next;\n        if (next instanceof InlineBlot && next.prev === this && isEqual(formats, next.formats())) {\n            next.moveChildren(this);\n            next.remove();\n        }\n    };\n    InlineBlot.blotName = 'inline';\n    InlineBlot.scope = Registry.Scope.INLINE_BLOT;\n    InlineBlot.tagName = 'SPAN';\n    return InlineBlot;\n}(format_1.default));\nexports.default = InlineBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/inline.ts\n// module id = 13\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar format_1 = require(\"./abstract/format\");\nvar Registry = require(\"../registry\");\nvar BlockBlot = /** @class */ (function (_super) {\n    __extends(BlockBlot, _super);\n    function BlockBlot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    BlockBlot.formats = function (domNode) {\n        var tagName = Registry.query(BlockBlot.blotName).tagName;\n        if (domNode.tagName === tagName)\n            return undefined;\n        return _super.formats.call(this, domNode);\n    };\n    BlockBlot.prototype.format = function (name, value) {\n        if (Registry.query(name, Registry.Scope.BLOCK) == null) {\n            return;\n        }\n        else if (name === this.statics.blotName && !value) {\n            this.replaceWith(BlockBlot.blotName);\n        }\n        else {\n            _super.prototype.format.call(this, name, value);\n        }\n    };\n    BlockBlot.prototype.formatAt = function (index, length, name, value) {\n        if (Registry.query(name, Registry.Scope.BLOCK) != null) {\n            this.format(name, value);\n        }\n        else {\n            _super.prototype.formatAt.call(this, index, length, name, value);\n        }\n    };\n    BlockBlot.prototype.insertAt = function (index, value, def) {\n        if (def == null || Registry.query(value, Registry.Scope.INLINE) != null) {\n            // Insert text or inline\n            _super.prototype.insertAt.call(this, index, value, def);\n        }\n        else {\n            var after = this.split(index);\n            var blot = Registry.create(value, def);\n            after.parent.insertBefore(blot, after);\n        }\n    };\n    BlockBlot.prototype.update = function (mutations, context) {\n        if (navigator.userAgent.match(/Trident/)) {\n            this.build();\n        }\n        else {\n            _super.prototype.update.call(this, mutations, context);\n        }\n    };\n    BlockBlot.blotName = 'block';\n    BlockBlot.scope = Registry.Scope.BLOCK_BLOT;\n    BlockBlot.tagName = 'P';\n    return BlockBlot;\n}(format_1.default));\nexports.default = BlockBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/block.ts\n// module id = 14\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar leaf_1 = require(\"./abstract/leaf\");\nvar EmbedBlot = /** @class */ (function (_super) {\n    __extends(EmbedBlot, _super);\n    function EmbedBlot() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    EmbedBlot.formats = function (domNode) {\n        return undefined;\n    };\n    EmbedBlot.prototype.format = function (name, value) {\n        // super.formatAt wraps, which is what we want in general,\n        // but this allows subclasses to overwrite for formats\n        // that just apply to particular embeds\n        _super.prototype.formatAt.call(this, 0, this.length(), name, value);\n    };\n    EmbedBlot.prototype.formatAt = function (index, length, name, value) {\n        if (index === 0 && length === this.length()) {\n            this.format(name, value);\n        }\n        else {\n            _super.prototype.formatAt.call(this, index, length, name, value);\n        }\n    };\n    EmbedBlot.prototype.formats = function () {\n        return this.statics.formats(this.domNode);\n    };\n    return EmbedBlot;\n}(leaf_1.default));\nexports.default = EmbedBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/embed.ts\n// module id = 15\n// module chunks = 0", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar leaf_1 = require(\"./abstract/leaf\");\nvar Registry = require(\"../registry\");\nvar TextBlot = /** @class */ (function (_super) {\n    __extends(TextBlot, _super);\n    function TextBlot(node) {\n        var _this = _super.call(this, node) || this;\n        _this.text = _this.statics.value(_this.domNode);\n        return _this;\n    }\n    TextBlot.create = function (value) {\n        return document.createTextNode(value);\n    };\n    TextBlot.value = function (domNode) {\n        var text = domNode.data;\n        // @ts-ignore\n        if (text['normalize'])\n            text = text['normalize']();\n        return text;\n    };\n    TextBlot.prototype.deleteAt = function (index, length) {\n        this.domNode.data = this.text = this.text.slice(0, index) + this.text.slice(index + length);\n    };\n    TextBlot.prototype.index = function (node, offset) {\n        if (this.domNode === node) {\n            return offset;\n        }\n        return -1;\n    };\n    TextBlot.prototype.insertAt = function (index, value, def) {\n        if (def == null) {\n            this.text = this.text.slice(0, index) + value + this.text.slice(index);\n            this.domNode.data = this.text;\n        }\n        else {\n            _super.prototype.insertAt.call(this, index, value, def);\n        }\n    };\n    TextBlot.prototype.length = function () {\n        return this.text.length;\n    };\n    TextBlot.prototype.optimize = function (context) {\n        _super.prototype.optimize.call(this, context);\n        this.text = this.statics.value(this.domNode);\n        if (this.text.length === 0) {\n            this.remove();\n        }\n        else if (this.next instanceof TextBlot && this.next.prev === this) {\n            this.insertAt(this.length(), this.next.value());\n            this.next.remove();\n        }\n    };\n    TextBlot.prototype.position = function (index, inclusive) {\n        if (inclusive === void 0) { inclusive = false; }\n        return [this.domNode, index];\n    };\n    TextBlot.prototype.split = function (index, force) {\n        if (force === void 0) { force = false; }\n        if (!force) {\n            if (index === 0)\n                return this;\n            if (index === this.length())\n                return this.next;\n        }\n        var after = Registry.create(this.domNode.splitText(index));\n        this.parent.insertBefore(after, this.next);\n        this.text = this.statics.value(this.domNode);\n        return after;\n    };\n    TextBlot.prototype.update = function (mutations, context) {\n        var _this = this;\n        if (mutations.some(function (mutation) {\n            return mutation.type === 'characterData' && mutation.target === _this.domNode;\n        })) {\n            this.text = this.statics.value(this.domNode);\n        }\n    };\n    TextBlot.prototype.value = function () {\n        return this.text;\n    };\n    TextBlot.blotName = 'text';\n    TextBlot.scope = Registry.Scope.INLINE_BLOT;\n    return TextBlot;\n}(leaf_1.default));\nexports.default = TextBlot;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/blot/text.ts\n// module id = 16\n// module chunks = 0"], "sourceRoot": ""}