(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2281],{17313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>c,j7:()=>n,tU:()=>o});var r=a(95155),t=a(12115),i=a(60704),l=a(59434);let o=i.bL,n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.B8,{ref:s,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...t})});n.displayName=i.B8.displayName;let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.l9,{ref:s,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...t})});d.displayName=i.l9.displayName;let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.UC,{ref:s,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...t})});c.displayName=i.UC.displayName},30285:(e,s,a)=>{"use strict";a.d(s,{$:()=>n,r:()=>o});var r=a(95155);a(12115);var t=a(99708),i=a(74466),l=a(59434);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function n(e){let{className:s,variant:a,size:i,asChild:n=!1,...d}=e,c=n?t.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,l.cn)(o({variant:a,size:i,className:s})),...d})}},40504:(e,s,a)=>{Promise.resolve().then(a.bind(a,72608))},59434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>i});var r=a(52596),t=a(39688);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.QP)((0,r.$)(s))}},62523:(e,s,a)=>{"use strict";a.d(s,{p:()=>i});var r=a(95155);a(12115);var t=a(59434);function i(e){let{className:s,type:a,...i}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},66695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>o,wL:()=>m});var r=a(95155),t=a(12115),i=a(59434);let l=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});l.displayName="Card";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...t})});o.displayName="CardHeader";let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});n.displayName="CardTitle";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",a),...t})});d.displayName="CardDescription";let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6",a),...t})});c.displayName="CardContent";let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6",a),...t})});m.displayName="CardFooter"},72608:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N,dynamic:()=>y});var r=a(95155),t=a(12115),i=a(27600),l=a(30285),o=a(62523),n=a(85057),d=a(88539),c=a(62177),m=a(63560),u=a(70927),p=a(56671),f=a(4516),x=a(51154),h=a(4229),g=a(66695),v=a(17313),b=a(95666);let j=u.Ik({firstName:u.Yj().min(2,{message:"First name must be at least 2 characters."}),lastName:u.Yj().min(2,{message:"Last name must be at least 2 characters."}),email:u.Yj().email({message:"Please enter a valid email address."}),bio:u.Yj().max(500,{message:"Bio must not exceed 500 characters."}).optional(),jobTitle:u.Yj().optional(),company:u.Yj().optional(),location:u.Yj().optional(),phone:u.Yj().optional(),website:u.Yj().optional(),twitter:u.Yj().optional(),linkedin:u.Yj().optional(),facebook:u.Yj().optional(),instagram:u.Yj().optional()}),w=u.Ik({currentPassword:u.Yj().min(1,{message:"Current password is required."}),newPassword:u.Yj().min(8,{message:"New password must be at least 8 characters."}),confirmPassword:u.Yj().min(1,{message:"Please confirm your new password."})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords don't match.",path:["confirmPassword"]});function N(){let{user:e,updateProfile:s,refreshAuth:a}=(0,i.A)(),[u,N]=(0,t.useState)(!0),[y,k]=(0,t.useState)(!1),[P,C]=(0,t.useState)(!1);(0,t.useEffect)(()=>{(async()=>{try{N(!0)}catch(e){console.error("Error fetching user profile:",e),p.o.error("Failed to load profile data")}finally{N(!1)}})()},[e]);let T=(0,c.mN)({resolver:(0,m.u)(j),defaultValues:{firstName:"",lastName:"",email:"",bio:"",jobTitle:"",company:"",location:"",phone:"",website:"",twitter:"",linkedin:"",facebook:"",instagram:""},reValidateMode:"onChange"}),S=(0,c.mN)({resolver:(0,m.u)(w),defaultValues:{currentPassword:"",newPassword:"",confirmPassword:""}});(0,t.useEffect)(()=>{if(e){var s,a,r,t,i,l,o,n,d,c,m,u,p,f;let x={firstName:e.firstName||"",lastName:e.lastName||"",email:e.email||"",bio:e.bio||(null==(s=e.acf)?void 0:s.bio)||"",jobTitle:e.jobTitle||(null==(a=e.acf)?void 0:a.job_title)||"",company:e.company||(null==(r=e.acf)?void 0:r.company)||"",location:e.location||(null==(t=e.acf)?void 0:t.location)||"",phone:e.phone||(null==(i=e.acf)?void 0:i.phone)||"",website:(null==(l=e.acf)?void 0:l.website)||"",twitter:(null==(o=e.socialLinks)?void 0:o.twitter)||(null==(n=e.acf)?void 0:n.twitter)||"",linkedin:(null==(d=e.socialLinks)?void 0:d.linkedin)||(null==(c=e.acf)?void 0:c.linkedin)||"",facebook:(null==(m=e.socialLinks)?void 0:m.facebook)||(null==(u=e.acf)?void 0:u.facebook)||"",instagram:(null==(p=e.socialLinks)?void 0:p.instagram)||(null==(f=e.acf)?void 0:f.instagram)||""};T.reset(x)}},[e,T]);let z=async r=>{try{N(!0),k(!0);let t={firstName:r.firstName||"",lastName:r.lastName||"",bio:r.bio||"",jobTitle:r.jobTitle||"",company:r.company||"",location:r.location||"",phone:r.phone||"",website:r.website||"",roles:(null==e?void 0:e.roles)||[],socialLinks:{twitter:r.twitter||"",linkedin:r.linkedin||"",facebook:r.facebook||"",instagram:r.instagram||""},acf:{...(null==e?void 0:e.acf)||{},bio:r.bio||"",job_title:r.jobTitle||"",company:r.company||"",location:r.location||"",phone:r.phone||"",website:r.website||"",twitter:r.twitter||"",linkedin:r.linkedin||"",facebook:r.facebook||"",instagram:r.instagram||""},username:(null==e?void 0:e.username)||"",name:"".concat(r.firstName," ").concat(r.lastName).trim()||(null==e?void 0:e.name)||"",email:r.email||(null==e?void 0:e.email)||""};await s(t),await a(),p.o.success("Profile updated successfully!")}catch(e){console.error("Error updating profile:",e),p.o.error(e instanceof Error?e.message:"Failed to update profile. Please try again.")}finally{N(!1),setTimeout(()=>{k(!1)},500)}},F=async e=>{try{C(!0);let s=await fetch("/api/user/password",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({currentPassword:e.currentPassword,newPassword:e.newPassword})});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.error||"Failed to update password")}S.reset(),p.o.success("Password updated successfully!")}catch(e){console.error("Error updating password:",e),p.o.error(e instanceof Error?e.message:"Failed to update password. Please try again.")}finally{C(!1)}};return(0,r.jsxs)("div",{className:"py-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-8",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Settings"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your account settings and preferences"})]})}),(0,r.jsxs)(v.tU,{defaultValue:"profile",className:"space-y-6",children:[(0,r.jsxs)(v.j7,{className:"grid w-full max-w-[400px] grid-cols-2 bg-white border border-gray-200 p-1 px-[5px] h-12 rounded-full",children:[(0,r.jsx)(v.Xi,{value:"profile",className:"data-[state=active]:bg-[#5cc8ff] data-[state=active]:text-[#3d405b] data-[state=active]:font-bold py-2 rounded-full",children:"Profile"}),(0,r.jsx)(v.Xi,{value:"account",className:"data-[state=active]:bg-[#5cc8ff] data-[state=active]:text-[#3d405b] data-[state=active]:font-bold py-2 rounded-full",children:"Account"})]}),(0,r.jsx)(v.av,{value:"profile",className:"space-y-6",children:(0,r.jsxs)(g.Zp,{children:[(0,r.jsxs)(g.aR,{children:[(0,r.jsx)(g.ZB,{children:"Profile"}),(0,r.jsx)(g.BT,{children:"This is how others will see you on the platform."})]}),(0,r.jsxs)("form",{onSubmit:T.handleSubmit(z),children:[(0,r.jsxs)(g.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"firstName",children:"First Name"}),(0,r.jsx)(o.p,{id:"firstName",placeholder:"John",className:"rounded-full",...T.register("firstName")}),T.formState.errors.firstName&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:T.formState.errors.firstName.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"lastName",children:"Last Name"}),(0,r.jsx)(o.p,{id:"lastName",placeholder:"Doe",className:"rounded-full",...T.register("lastName")}),T.formState.errors.lastName&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:T.formState.errors.lastName.message})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",className:"rounded-full",...T.register("email")}),T.formState.errors.email&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:T.formState.errors.email.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"jobTitle",children:"Job Title"}),(0,r.jsx)(o.p,{id:"jobTitle",placeholder:"e.g. Tourism Specialist",className:"rounded-full",...T.register("jobTitle")})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"company",children:"Company"}),(0,r.jsx)(o.p,{id:"company",placeholder:"e.g. ABC Tourism Board",className:"rounded-full",...T.register("company")})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"location",children:"Location"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(f.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(o.p,{id:"location",placeholder:"City, Country",className:"pl-10 rounded-full",...T.register("location")})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"phone",children:"Phone"}),(0,r.jsx)(o.p,{id:"phone",placeholder:"e.g. +****************",className:"rounded-full",...T.register("phone")})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"website",children:"Website"}),(0,r.jsx)(o.p,{id:"website",type:"url",placeholder:"https://www.example.com",className:"rounded-full",...T.register("website")})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{children:"Social Links"}),(0,r.jsxs)("div",{className:"grid gap-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(b.Tg,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(o.p,{placeholder:"Twitter username",className:"pl-10 rounded-full",...T.register("twitter")})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(b.af,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(o.p,{placeholder:"LinkedIn profile URL",className:"pl-10 rounded-full",...T.register("linkedin")})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(b.Gy,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(o.p,{placeholder:"Facebook profile URL",className:"pl-10 rounded-full",...T.register("facebook")})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(b.I4,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(o.p,{placeholder:"Instagram username",className:"pl-10 rounded-full",...T.register("instagram")})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{htmlFor:"bio",children:"Bio"}),(0,r.jsx)(d.T,{id:"bio",placeholder:"Tell us a little bit about yourself",className:"min-h-[100px] rounded-[20px]",...T.register("bio")}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This will be displayed on your public profile."}),T.formState.errors.bio&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:T.formState.errors.bio.message})]})]}),(0,r.jsx)(g.wL,{className:"px-6 py-4 pb-[34px]",children:(0,r.jsxs)(l.$,{type:"submit",disabled:y||u,className:"relative",children:[y&&(0,r.jsx)("span",{className:"absolute inset-0 flex items-center justify-center bg-primary rounded-full",children:(0,r.jsx)(x.A,{className:"h-5 w-5 animate-spin text-primary-foreground"})}),(0,r.jsxs)("span",{className:y?"opacity-0":"flex items-center",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Save Changes"]})]})})]})]})}),(0,r.jsx)(v.av,{value:"account",className:"space-y-6",children:(0,r.jsxs)(g.Zp,{children:[(0,r.jsxs)(g.aR,{children:[(0,r.jsx)(g.ZB,{children:"Account"}),(0,r.jsx)(g.BT,{children:"Update your account settings. Manage your password and security preferences."})]}),(0,r.jsxs)("form",{onSubmit:S.handleSubmit(F),children:[(0,r.jsx)(g.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.J,{children:"Change Password"}),(0,r.jsxs)("div",{className:"grid gap-3",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.p,{type:"password",placeholder:"Current Password",className:"rounded-full",...S.register("currentPassword")}),S.formState.errors.currentPassword&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:S.formState.errors.currentPassword.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.p,{type:"password",placeholder:"New Password",className:"rounded-full",...S.register("newPassword")}),S.formState.errors.newPassword&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:S.formState.errors.newPassword.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.p,{type:"password",placeholder:"Confirm New Password",className:"rounded-full",...S.register("confirmPassword")}),S.formState.errors.confirmPassword&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:S.formState.errors.confirmPassword.message})]})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use a strong, unique password that you don't use elsewhere."})]})}),(0,r.jsx)(g.wL,{className:"px-6 py-4 pb-[34px]",children:(0,r.jsxs)(l.$,{type:"submit",disabled:P,className:"relative",children:[P&&(0,r.jsx)("span",{className:"absolute inset-0 flex items-center justify-center bg-primary rounded-full",children:(0,r.jsx)(x.A,{className:"h-5 w-5 animate-spin text-primary-foreground"})}),(0,r.jsx)("span",{className:P?"opacity-0":"flex items-center",children:"Update Password"})]})})]})]})})]})]})}let y="force-dynamic"},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>l});var r=a(95155);a(12115);var t=a(40968),i=a(59434);function l(e){let{className:s,...a}=e;return(0,r.jsx)(t.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},88539:(e,s,a)=>{"use strict";a.d(s,{T:()=>i});var r=a(95155);a(12115);var t=a(59434);function i(e){let{className:s,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,t.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...a})}},95666:(e,s,a)=>{"use strict";a.d(s,{Gy:()=>t,I4:()=>o,Tg:()=>i,af:()=>l,dq:()=>n});var r=a(95155);function t(e){let{className:s="h-5 w-5"}=e;return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}function i(e){let{className:s="h-5 w-5"}=e;return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})}function l(e){let{className:s="h-5 w-5"}=e;return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})}function o(e){let{className:s="h-5 w-5"}=e;return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}function n(e){let{className:s="h-5 w-5"}=e;return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})})}a(12115)}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,2757,8034,1750,5604,3259,7600,8441,1684,7358],()=>s(40504)),_N_E=e.O()}]);