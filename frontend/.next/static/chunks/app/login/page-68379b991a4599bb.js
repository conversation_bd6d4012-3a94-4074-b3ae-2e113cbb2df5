(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>i,t:()=>n});var s=t(12115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function i(...e){return s.useCallback(n(...e),e)}},9690:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(95155),a=t(12115),n=t(35695),i=t(6874),l=t.n(i),o=t(62177),d=t(63560),c=t(70927),u=t(51154),m=t(62523),f=t(30285),p=t(27600);let x=c.Ik({username:c.Yj().min(1,{message:"Username is required"}),password:c.Yj().min(1,{message:"Password is required"})});function h(){let[e,r]=(0,a.useState)(null),[t,i]=(0,a.useState)(null),c=(0,n.useRouter)(),h=(0,n.useSearchParams)(),g=h.get("callbackUrl")||"/",b=h.get("reset"),{login:v,isLoading:y}=(0,p.A)();(0,a.useEffect)(()=>{if("true"===b){i("Password reset email sent! Check your email for the reset link.");let e=new URL(window.location.href);e.searchParams.delete("reset"),c.replace(e.pathname+e.search,{scroll:!1})}},[b,c]);let{register:w,handleSubmit:j,formState:{errors:N}}=(0,o.mN)({resolver:(0,d.u)(x)}),k=async e=>{try{r(null);let t=await v(e.username,e.password);(null==t?void 0:t.success)?c.push(g):(null==t?void 0:t.error)&&r(t.error)}catch(e){r(e instanceof Error?e.message:"Login failed. Please try again.")}},C=g&&"/"!==g?"After login, you will be redirected to ".concat(g):null;return(0,s.jsx)("div",{className:"container mx-auto pt-12 pb-8",children:(0,s.jsxs)("div",{className:"w-full max-w-md mx-auto space-y-6 p-8 border bg-white rounded-lg shadow-sm",children:[(0,s.jsxs)("div",{className:"space-y-2 text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Welcome back"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Enter your credentials to access your account"}),C&&(0,s.jsx)("p",{className:"text-sm text-blue-500 mt-2",children:C})]}),e&&(0,s.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 text-red-700 rounded-md text-sm",children:e}),t&&(0,s.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 text-green-700 rounded-md text-sm",children:t}),(0,s.jsxs)("form",{onSubmit:j(k),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"username",className:"text-sm font-medium",children:"Username"}),(0,s.jsx)(m.p,{id:"username",placeholder:"Username",...w("username"),className:N.username?"border-red-500":""}),N.username&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:N.username.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("label",{htmlFor:"password",className:"text-sm font-medium",children:"Password"}),(0,s.jsx)(l(),{href:"/forgot-password",className:"text-sm text-primary",children:"Forgot password?"})]}),(0,s.jsx)(m.p,{id:"password",type:"password",placeholder:"••••••••",...w("password"),className:N.password?"border-red-500":""}),N.password&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:N.password.message})]}),(0,s.jsx)(f.$,{type:"submit",className:"w-full bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-extrabold border-2 border-[#5cc8ff]",disabled:y,children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Logging in..."]}):"Log in"})]}),(0,s.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Don't have an account?"," ",(0,s.jsx)(l(),{href:"/register",className:"text-primary",children:"Sign up"})]})]})})}function g(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"container mx-auto pt-12 pb-8",children:(0,s.jsx)("div",{className:"w-full max-w-md mx-auto space-y-6 p-8 border bg-white rounded-lg shadow-sm",children:(0,s.jsxs)("div",{className:"space-y-2 text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Welcome back"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Loading..."})]})})}),children:(0,s.jsx)(h,{})})}},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},o=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:c="",children:u,iconNode:m,...f}=e;return(0,s.createElement)("svg",{ref:r,...d,width:a,height:a,stroke:t,strokeWidth:i?24*Number(n)/Number(a):n,className:l("lucide",c),...!u&&!o(f)&&{"aria-hidden":"true"},...f},[...m.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),u=(e,r)=>{let t=(0,s.forwardRef)((t,n)=>{let{className:o,...d}=t;return(0,s.createElement)(c,{ref:n,iconNode:r,className:l("lucide-".concat(a(i(e))),"lucide-".concat(e),o),...d})});return t.displayName=i(e),t}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o,r:()=>l});var s=t(95155);t(12115);var a=t(99708),n=t(74466),i=t(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:n,asChild:o=!1,...d}=e,c=o?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:n,className:r})),...d})}},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(52596),a=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(95155);t(12115);var a=t(59434);function n(e){let{className:r,type:t,...n}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},66862:(e,r,t)=>{Promise.resolve().then(t.bind(t,9690))},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,Dc:()=>d,TL:()=>i});var s=t(12115),a=t(6101),n=t(95155);function i(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var i;let e,l,o=(i=t,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{let r=n(...e);return a(...e),r}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(d.ref=r?(0,a.t)(r,o):o),s.cloneElement(t,d)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:a,...i}=e,l=s.Children.toArray(a),o=l.find(c);if(o){let e=o.props.children,a=l.map(r=>r!==o?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...i,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(r,{...i,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=o,r}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var r=r=>e(e.s=r);e.O(0,[5003,6874,2757,1750,7600,8441,1684,7358],()=>r(66862)),_N_E=e.O()}]);