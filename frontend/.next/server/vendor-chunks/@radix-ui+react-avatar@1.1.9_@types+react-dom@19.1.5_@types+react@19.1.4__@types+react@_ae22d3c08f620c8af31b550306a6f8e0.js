"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-avatar@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_ae22d3c08f620c8af31b550306a6f8e0";
exports.ids = ["vendor-chunks/@radix-ui+react-avatar@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_ae22d3c08f620c8af31b550306a6f8e0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_ae22d3c08f620c8af31b550306a6f8e0/node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-avatar@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_ae22d3c08f620c8af31b550306a6f8e0/node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.2_@types+react-dom@19.1.5_@types+react@19.1.4__@types+rea_7b933c94c4e5e4ade0d694d5b36d9593/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-is-hydrated */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-is-hydrated@0.1.0_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // src/avatar.tsx\n\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)({\n        \"AvatarImage.useCallbackRef[handleLoadingStatusChange]\": (status)=>{\n            onLoadingStatusChange(status);\n            context.onImageLoadingStatusChange(status);\n        }\n    }[\"AvatarImage.useCallbackRef[handleLoadingStatusChange]\"]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"AvatarImage.useLayoutEffect\": ()=>{\n            if (imageLoadingStatus !== \"idle\") {\n                handleLoadingStatusChange(imageLoadingStatus);\n            }\n        }\n    }[\"AvatarImage.useLayoutEffect\"], [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"AvatarFallback.useEffect\": ()=>{\n            if (delayMs !== void 0) {\n                const timerId = window.setTimeout({\n                    \"AvatarFallback.useEffect.timerId\": ()=>setCanRender(true)\n                }[\"AvatarFallback.useEffect.timerId\"], delayMs);\n                return ({\n                    \"AvatarFallback.useEffect\": ()=>window.clearTimeout(timerId)\n                })[\"AvatarFallback.useEffect\"];\n            }\n        }\n    }[\"AvatarFallback.useEffect\"], [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction resolveLoadingStatus(image, src) {\n    if (!image) {\n        return \"idle\";\n    }\n    if (!src) {\n        return \"error\";\n    }\n    if (image.src !== src) {\n        image.src = src;\n    }\n    return image.complete && image.naturalWidth > 0 ? \"loaded\" : \"loading\";\n}\nfunction useImageLoadingStatus(src, { referrerPolicy, crossOrigin }) {\n    const isHydrated = (0,_radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__.useIsHydrated)();\n    const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const image = (()=>{\n        if (!isHydrated) return null;\n        if (!imageRef.current) {\n            imageRef.current = new window.Image();\n        }\n        return imageRef.current;\n    })();\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useImageLoadingStatus.useState\": ()=>resolveLoadingStatus(image, src)\n    }[\"useImageLoadingStatus.useState\"]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n            setLoadingStatus(resolveLoadingStatus(image, src));\n        }\n    }[\"useImageLoadingStatus.useLayoutEffect\"], [\n        image,\n        src\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n            const updateStatus = {\n                \"useImageLoadingStatus.useLayoutEffect.updateStatus\": (status)=>({\n                        \"useImageLoadingStatus.useLayoutEffect.updateStatus\": ()=>{\n                            setLoadingStatus(status);\n                        }\n                    })[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"]\n            }[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"];\n            if (!image) return;\n            const handleLoad = updateStatus(\"loaded\");\n            const handleError = updateStatus(\"error\");\n            image.addEventListener(\"load\", handleLoad);\n            image.addEventListener(\"error\", handleError);\n            if (referrerPolicy) {\n                image.referrerPolicy = referrerPolicy;\n            }\n            if (typeof crossOrigin === \"string\") {\n                image.crossOrigin = crossOrigin;\n            }\n            return ({\n                \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n                    image.removeEventListener(\"load\", handleLoad);\n                    image.removeEventListener(\"error\", handleError);\n                }\n            })[\"useImageLoadingStatus.useLayoutEffect\"];\n        }\n    }[\"useImageLoadingStatus.useLayoutEffect\"], [\n        image,\n        crossOrigin,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_ae22d3c08f620c8af31b550306a6f8e0/node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ })

};
;