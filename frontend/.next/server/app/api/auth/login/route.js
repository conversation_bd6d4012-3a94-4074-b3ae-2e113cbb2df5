/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/login/route.ts */ \"(rsc)/./src/app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/api/auth/login/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const { username, password } = await request.json();\n        if (!username || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Username and password are required'\n            }, {\n                status: 400\n            });\n        }\n        const WORDPRESS_API_URL = \"http://mytourismiq.wpenginepowered.com/\";\n        if (!WORDPRESS_API_URL) {\n            console.error('NEXT_PUBLIC_WORDPRESS_API_URL is not defined');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // New WordPress endpoint for cookie-based login\n        const cookieLoginEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/login`;\n        const wpResponse = await fetch(cookieLoginEndpoint, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                // Forward original cookies if any are relevant, though for login, usually not critical\n                // However, some WP security plugins might expect this for CSRF or other checks.\n                ...request.headers.get('cookie') ? {\n                    Cookie: request.headers.get('cookie')\n                } : {}\n            },\n            body: JSON.stringify({\n                username,\n                password\n            })\n        });\n        const responseData = await wpResponse.json();\n        if (!wpResponse.ok || !responseData.success) {\n            console.error('WordPress cookie login failed:', responseData);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: responseData.message || 'Login failed',\n                code: responseData.code || 'wordpress_login_error'\n            }, {\n                status: wpResponse.status\n            });\n        }\n        // WordPress has set its httpOnly auth cookies.\n        // We need to relay any Set-Cookie headers from WordPress to the client.\n        const headers = new Headers();\n        // Iterate over Set-Cookie headers from WordPress response and append them\n        // For Node.js runtime, response.headers.raw()['set-cookie'] would be an array.\n        // For standard Fetch API, multiple Set-Cookie headers should be accessible by iterating.\n        // However, direct iteration over headers for a specific key is not straightforward with standard Headers API.\n        // A common workaround is to check if the environment provides a way to get raw headers or if a library handles it.\n        // For Next.js, let's try a more direct pass-through or specific handling if available.\n        // The `Headers` object in the Fetch API spec does not combine multiple headers of the same name\n        // when using `get()`. `getAll()` is the standards-compliant way but not universally available on Request.Headers.\n        // For Response.Headers, `forEach` can be used.\n        wpResponse.headers.forEach((value, key)=>{\n            if (key.toLowerCase() === 'set-cookie') {\n                headers.append('Set-Cookie', value);\n            }\n        });\n        if (!headers.has('Set-Cookie')) {\n            console.warn(\"WordPress login endpoint did not return Set-Cookie header or it wasn't relayed.\");\n        }\n        // Do NOT set auth_token or is_logged_in cookies here anymore.\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Login successful',\n            user: responseData.user\n        }, {\n            status: 200,\n            headers: headers\n        });\n    } catch (error) {\n        console.error('Login API route error:', error);\n        if (error instanceof SyntaxError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid request body'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'An unexpected error occurred during login'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();