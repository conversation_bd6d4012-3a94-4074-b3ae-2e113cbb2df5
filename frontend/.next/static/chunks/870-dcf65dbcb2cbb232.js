"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[870],{5196:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},38795:(t,e,n)=>{n.d(e,{Mz:()=>t4,i3:()=>ee,UC:()=>et,bL:()=>t8,Bk:()=>tG});var r=n(12115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=t=>({x:t,y:t}),c={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function d(t,e){return"function"==typeof t?t(e):t}function p(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function g(t){return"y"===t?"height":"width"}let y=new Set(["top","bottom"]);function w(t){return y.has(p(t))?"y":"x"}function x(t){return t.replace(/start|end/g,t=>u[t])}let v=["left","right"],b=["right","left"],A=["top","bottom"],R=["bottom","top"];function S(t){return t.replace(/left|right|bottom|top/g,t=>c[t])}function L(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function T(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function E(t,e,n){let r,{reference:i,floating:o}=t,l=w(e),a=m(w(e)),f=g(a),s=p(e),c="y"===l,u=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[f]/2-o[f]/2;switch(s){case"top":r={x:u,y:i.y-o.height};break;case"bottom":r={x:u,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[a]-=y*(n&&c?-1:1);break;case"end":r[a]+=y*(n&&c?-1:1)}return r}let C=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(e)),s=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:c,y:u}=E(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:x}=await m({x:c,y:u,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:t,floating:e}});c=null!=g?g:c,u=null!=y?y:u,p={...p,[o]:{...p[o],...w}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(s=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),{x:c,y:u}=E(s,d,f)),n=-1)}return{x:c,y:u,placement:d,strategy:i,middlewareData:p}};async function O(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=t,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:u="floating",altBoundary:p=!1,padding:h=0}=d(e,t),m=L(h),g=a[p?"floating"===u?"reference":"floating":u],y=T(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:f})),w="floating"===u?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),v=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=T(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:f}):w);return{top:(y.top-b.top+m.top)/v.y,bottom:(b.bottom-y.bottom+m.bottom)/v.y,left:(y.left-b.left+m.left)/v.x,right:(b.right-y.right+m.right)/v.x}}function P(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function k(t){return i.some(e=>t[e]>=0)}let H=new Set(["left","top"]);async function D(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===w(n),s=H.has(l)?-1:1,c=o&&f?-1:1,u=d(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-1*y:y),f?{x:g*c,y:m*s}:{x:m*s,y:g*c}}function F(){return"undefined"!=typeof window}function M(t){return W(t)?(t.nodeName||"").toLowerCase():"#document"}function N(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function j(t){var e;return null==(e=(W(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function W(t){return!!F()&&(t instanceof Node||t instanceof N(t).Node)}function z(t){return!!F()&&(t instanceof Element||t instanceof N(t).Element)}function B(t){return!!F()&&(t instanceof HTMLElement||t instanceof N(t).HTMLElement)}function V(t){return!!F()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof N(t).ShadowRoot)}let _=new Set(["inline","contents"]);function I(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=tt(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!_.has(i)}let Y=new Set(["table","td","th"]),X=[":popover-open",":modal"];function G(t){return X.some(e=>{try{return t.matches(e)}catch(t){return!1}})}let $=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],U=["paint","layout","strict","content"];function J(t){let e=K(),n=z(t)?tt(t):t;return $.some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||q.some(t=>(n.willChange||"").includes(t))||U.some(t=>(n.contain||"").includes(t))}function K(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function Z(t){return Q.has(M(t))}function tt(t){return N(t).getComputedStyle(t)}function te(t){return z(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function tn(t){if("html"===M(t))return t;let e=t.assignedSlot||t.parentNode||V(t)&&t.host||j(t);return V(e)?e.host:e}function tr(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=tn(e);return Z(n)?e.ownerDocument?e.ownerDocument.body:e.body:B(n)&&I(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=N(i);if(o){let t=ti(l);return e.concat(l,l.visualViewport||[],I(i)?i:[],t&&n?tr(t):[])}return e.concat(i,tr(i,[],n))}function ti(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function to(t){let e=tt(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=B(t),o=i?t.offsetWidth:n,l=i?t.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function tl(t){return z(t)?t:t.contextElement}function ta(t){let e=tl(t);if(!B(e))return s(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=to(e),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let tf=s(0);function ts(t){let e=N(t);return K()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tf}function tc(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=tl(t),a=s(1);e&&(r?z(r)&&(a=ta(r)):a=ta(t));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===N(l))&&i)?ts(l):s(0),c=(o.left+f.x)/a.x,u=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let t=N(l),e=r&&z(r)?N(r):r,n=t,i=ti(n);for(;i&&r&&e!==n;){let t=ta(i),e=i.getBoundingClientRect(),r=tt(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,u*=t.y,d*=t.x,p*=t.y,c+=o,u+=l,i=ti(n=N(i))}}return T({width:d,height:p,x:c,y:u})}function tu(t,e){let n=te(t).scrollLeft;return e?e.left+n:tc(j(t)).left+n}function td(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:tu(t,r)),y:r.top+e.scrollTop}}let tp=new Set(["absolute","fixed"]);function th(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=N(t),r=j(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let t=K();(!t||t&&"fixed"===e)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(t,n);else if("document"===e)r=function(t){let e=j(t),n=te(t),r=t.ownerDocument.body,i=l(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=l(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+tu(t),f=-n.scrollTop;return"rtl"===tt(r).direction&&(a+=l(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(j(t));else if(z(e))r=function(t,e){let n=tc(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=B(t)?ta(t):s(1),l=t.clientWidth*o.x,a=t.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(e,n);else{let n=ts(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return T(r)}function tm(t){return"static"===tt(t).position}function tg(t,e){if(!B(t)||"fixed"===tt(t).position)return null;if(e)return e(t);let n=t.offsetParent;return j(t)===n&&(n=n.ownerDocument.body),n}function ty(t,e){var n;let r=N(t);if(G(t))return r;if(!B(t)){let e=tn(t);for(;e&&!Z(e);){if(z(e)&&!tm(e))return e;e=tn(e)}return r}let i=tg(t,e);for(;i&&(n=i,Y.has(M(n)))&&tm(i);)i=tg(i,e);return i&&Z(i)&&tm(i)&&!J(i)?r:i||function(t){let e=tn(t);for(;B(e)&&!Z(e);){if(J(e))return e;if(G(e))break;e=tn(e)}return null}(t)||r}let tw=async function(t){let e=this.getOffsetParent||ty,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=B(e),i=j(e),o="fixed"===n,l=tc(t,!0,o,e),a={scrollLeft:0,scrollTop:0},f=s(0);if(r||!r&&!o)if(("body"!==M(e)||I(i))&&(a=te(e)),r){let t=tc(e,!0,o,e);f.x=t.x+e.clientLeft,f.y=t.y+e.clientTop}else i&&(f.x=tu(i));o&&!r&&i&&(f.x=tu(i));let c=!i||r||o?s(0):td(i,a);return{x:l.left+a.scrollLeft-f.x-c.x,y:l.top+a.scrollTop-f.y-c.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},tx={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=j(r),a=!!e&&G(e.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},c=s(1),u=s(0),d=B(r);if((d||!d&&!o)&&(("body"!==M(r)||I(l))&&(f=te(r)),B(r))){let t=tc(r);c=ta(r),u.x=t.x+r.clientLeft,u.y=t.y+r.clientTop}let p=!l||d||o?s(0):td(l,f,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-f.scrollLeft*c.x+u.x+p.x,y:n.y*c.y-f.scrollTop*c.y+u.y+p.y}},getDocumentElement:j,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:i}=t,a=[..."clippingAncestors"===n?G(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=tr(t,[],!1).filter(t=>z(t)&&"body"!==M(t)),i=null,o="fixed"===tt(t).position,l=o?tn(t):t;for(;z(l)&&!Z(l);){let e=tt(l),n=J(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&tp.has(i.position)||I(l)&&!n&&function t(e,n){let r=tn(e);return!(r===n||!z(r)||Z(r))&&("fixed"===tt(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=tn(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],f=a[0],s=a.reduce((t,n)=>{let r=th(e,n,i);return t.top=l(r.top,t.top),t.right=o(r.right,t.right),t.bottom=o(r.bottom,t.bottom),t.left=l(r.left,t.left),t},th(e,f,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ty,getElementRects:tw,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=to(t);return{width:e,height:n}},getScale:ta,isElement:z,isRTL:function(t){return"rtl"===tt(t).direction}};function tv(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}let tb=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:i,rects:a,platform:f,elements:s,middlewareData:c}=e,{element:u,padding:p=0}=d(t,e)||{};if(null==u)return{};let y=L(p),x={x:n,y:r},v=m(w(i)),b=g(v),A=await f.getDimensions(u),R="y"===v,S=R?"clientHeight":"clientWidth",T=a.reference[b]+a.reference[v]-x[v]-a.floating[b],E=x[v]-a.reference[v],C=await (null==f.getOffsetParent?void 0:f.getOffsetParent(u)),O=C?C[S]:0;O&&await (null==f.isElement?void 0:f.isElement(C))||(O=s.floating[S]||a.floating[b]);let P=O/2-A[b]/2-1,k=o(y[R?"top":"left"],P),H=o(y[R?"bottom":"right"],P),D=O-A[b]-H,F=O/2-A[b]/2+(T/2-E/2),M=l(k,o(F,D)),N=!c.arrow&&null!=h(i)&&F!==M&&a.reference[b]/2-(F<k?k:H)-A[b]/2<0,j=N?F<k?F-k:F-D:0;return{[v]:x[v]+j,data:{[v]:M,centerOffset:F-M-j,...N&&{alignmentOffset:j}},reset:N}}}),tA=(t,e,n)=>{let r=new Map,i={platform:tx,...n},o={...i.platform,_c:r};return C(t,e,{...i,platform:o})};var tR=n(47650),tS="undefined"!=typeof document?r.useLayoutEffect:function(){};function tL(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!tL(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!tL(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function tT(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function tE(t,e){let n=tT(t);return Math.round(e*n)/n}function tC(t){let e=r.useRef(t);return tS(()=>{e.current=t}),e}let tO=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:r}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?tb({element:n.current,padding:r}).fn(e):{}:n?tb({element:n,padding:r}).fn(e):{}}}),tP=(t,e)=>({...function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=e,f=await D(e,t);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(t),options:[t,e]}),tk=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:i}=e,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=d(t,e),u={x:n,y:r},h=await O(e,c),g=w(p(i)),y=m(g),x=u[y],v=u[g];if(a){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=x+h[t],r=x-h[e];x=l(n,o(x,r))}if(f){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",n=v+h[t],r=v-h[e];v=l(n,o(v,r))}let b=s.fn({...e,[y]:x,[g]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[g]:f}}}}}}(t),options:[t,e]}),tH=(t,e)=>({...function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(t,e),c={x:n,y:r},u=w(i),h=m(u),g=c[h],y=c[u],x=d(a,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){let t="y"===h?"height":"width",e=o.reference[h]-o.floating[t]+v.mainAxis,n=o.reference[h]+o.reference[t]-v.mainAxis;g<e?g=e:g>n&&(g=n)}if(s){var b,A;let t="y"===h?"width":"height",e=H.has(p(i)),n=o.reference[u]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[u])||0)+(e?0:v.crossAxis),r=o.reference[u]+o.reference[t]+(e?0:(null==(A=l.offset)?void 0:A[u])||0)-(e?v.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:g,[u]:y}}}}(t),options:[t,e]}),tD=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:c,platform:u,elements:y}=e,{mainAxis:L=!0,crossAxis:T=!0,fallbackPlacements:E,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:k=!0,...H}=d(t,e);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let D=p(a),F=w(c),M=p(c)===c,N=await (null==u.isRTL?void 0:u.isRTL(y.floating)),j=E||(M||!k?[S(c)]:function(t){let e=S(t);return[x(t),e,x(e)]}(c)),W="none"!==P;!E&&W&&j.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){switch(t){case"top":case"bottom":if(n)return e?b:v;return e?v:b;case"left":case"right":return e?A:R;default:return[]}}(p(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(x)))),o}(c,k,P,N));let z=[c,...j],B=await O(e,H),V=[],_=(null==(r=f.flip)?void 0:r.overflows)||[];if(L&&V.push(B[D]),T){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=m(w(t)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=S(l)),[l,S(l)]}(a,s,N);V.push(B[t[0]],B[t[1]])}if(_=[..._,{placement:a,overflows:V}],!V.every(t=>t<=0)){let t=((null==(i=f.flip)?void 0:i.index)||0)+1,e=z[t];if(e&&("alignment"!==T||F===w(e)||_.every(t=>w(t.placement)!==F||t.overflows[0]>0)))return{data:{index:t,overflows:_},reset:{placement:e}};let n=null==(o=_.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let t=null==(l=_.filter(t=>{if(W){let e=w(t.placement);return e===F||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(t),options:[t,e]}),tF=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;let i,a,{placement:f,rects:s,platform:c,elements:u}=e,{apply:m=()=>{},...g}=d(t,e),y=await O(e,g),x=p(f),v=h(f),b="y"===w(f),{width:A,height:R}=s.floating;"top"===x||"bottom"===x?(i=x,a=v===(await (null==c.isRTL?void 0:c.isRTL(u.floating))?"start":"end")?"left":"right"):(a=x,i="end"===v?"top":"bottom");let S=R-y.top-y.bottom,L=A-y.left-y.right,T=o(R-y[i],S),E=o(A-y[a],L),C=!e.middlewareData.shift,P=T,k=E;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(k=L),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(P=S),C&&!v){let t=l(y.left,0),e=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);b?k=A-2*(0!==t||0!==e?t+e:l(y.left,y.right)):P=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await m({...e,availableWidth:k,availableHeight:P});let H=await c.getDimensions(u.floating);return A!==H.width||R!==H.height?{reset:{rects:!0}}:{}}}}(t),options:[t,e]}),tM=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=d(t,e);switch(r){case"referenceHidden":{let t=P(await O(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:k(t)}}}case"escaped":{let t=P(await O(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:k(t)}}}default:return{}}}}}(t),options:[t,e]}),tN=(t,e)=>({...tO(t),options:[t,e]});var tj=n(63655),tW=n(95155),tz=r.forwardRef((t,e)=>{let{children:n,width:r=10,height:i=5,...o}=t;return(0,tW.jsx)(tj.sG.svg,{...o,ref:e,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:(0,tW.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tz.displayName="Arrow";var tB=n(6101),tV=n(46081),t_=n(39033),tI=n(52712),tY="Popper",[tX,tG]=(0,tV.A)(tY),[t$,tq]=tX(tY),tU=t=>{let{__scopePopper:e,children:n}=t,[i,o]=r.useState(null);return(0,tW.jsx)(t$,{scope:e,anchor:i,onAnchorChange:o,children:n})};tU.displayName=tY;var tJ="PopperAnchor",tK=r.forwardRef((t,e)=>{let{__scopePopper:n,virtualRef:i,...o}=t,l=tq(tJ,n),a=r.useRef(null),f=(0,tB.s)(e,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,tW.jsx)(tj.sG.div,{...o,ref:f})});tK.displayName=tJ;var tQ="PopperContent",[tZ,t0]=tX(tQ),t1=r.forwardRef((t,e)=>{var n,i,a,s,c,u,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:v=!0,collisionBoundary:b=[],collisionPadding:A=0,sticky:R="partial",hideWhenDetached:S=!1,updatePositionStrategy:L="optimized",onPlaced:T,...E}=t,C=tq(tQ,h),[O,P]=r.useState(null),k=(0,tB.s)(e,t=>P(t)),[H,D]=r.useState(null),F=function(t){let[e,n]=r.useState(void 0);return(0,tI.N)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}(H),M=null!=(d=null==F?void 0:F.width)?d:0,N=null!=(p=null==F?void 0:F.height)?p:0,W="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},z=Array.isArray(b)?b:[b],B=z.length>0,V={padding:W,boundary:z.filter(t9),altBoundary:B},{refs:_,floatingStyles:I,placement:Y,isPositioned:X,middlewareData:G}=function(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:c}=t,[u,d]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);tL(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(t=>{t!==R.current&&(R.current=t,g(t))},[]),v=r.useCallback(t=>{t!==S.current&&(S.current=t,w(t))},[]),b=l||m,A=a||y,R=r.useRef(null),S=r.useRef(null),L=r.useRef(u),T=null!=s,E=tC(s),C=tC(o),O=tC(c),P=r.useCallback(()=>{if(!R.current||!S.current)return;let t={placement:e,strategy:n,middleware:p};C.current&&(t.platform=C.current),tA(R.current,S.current,t).then(t=>{let e={...t,isPositioned:!1!==O.current};k.current&&!tL(L.current,e)&&(L.current=e,tR.flushSync(()=>{d(e)}))})},[p,e,n,C,O]);tS(()=>{!1===c&&L.current.isPositioned&&(L.current.isPositioned=!1,d(t=>({...t,isPositioned:!1})))},[c]);let k=r.useRef(!1);tS(()=>(k.current=!0,()=>{k.current=!1}),[]),tS(()=>{if(b&&(R.current=b),A&&(S.current=A),b&&A){if(E.current)return E.current(b,A,P);P()}},[b,A,P,E,T]);let H=r.useMemo(()=>({reference:R,floating:S,setReference:x,setFloating:v}),[x,v]),D=r.useMemo(()=>({reference:b,floating:A}),[b,A]),F=r.useMemo(()=>{let t={position:n,left:0,top:0};if(!D.floating)return t;let e=tE(D.floating,u.x),r=tE(D.floating,u.y);return f?{...t,transform:"translate("+e+"px, "+r+"px)",...tT(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,f,D.floating,u.x,u.y]);return r.useMemo(()=>({...u,update:P,refs:H,elements:D,floatingStyles:F}),[u,P,H,D,F])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t,e,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=tl(t),h=a||s?[...p?tr(p):[],...tr(e)]:[];h.forEach(t=>{a&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)});let m=p&&u?function(t,e){let n,r=null,i=j(t);function a(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function s(c,u){void 0===c&&(c=!1),void 0===u&&(u=1),a();let d=t.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||e(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),x={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,u))||1},v=!0;function b(e){let r=e[0].intersectionRatio;if(r!==u){if(!v)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||tv(d,t.getBoundingClientRect())||s(),v=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(t){r=new IntersectionObserver(b,x)}r.observe(t)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===p&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),p&&!d&&y.observe(p),y.observe(e));let w=d?tc(t):null;return d&&function e(){let r=tc(t);w&&!tv(w,r)&&n(),w=r,i=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{a&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...e,{animationFrame:"always"===L})},elements:{reference:C.anchor},middleware:[tP({mainAxis:g+N,alignmentAxis:w}),v&&tk({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?tH():void 0,...V}),v&&tD({...V}),tF({...V,apply:t=>{let{elements:e,rects:n,availableWidth:r,availableHeight:i}=t,{width:o,height:l}=n.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),H&&tN({element:H,padding:x}),t6({arrowWidth:M,arrowHeight:N}),S&&tM({strategy:"referenceHidden",...V})]}),[$,q]=t7(Y),U=(0,t_.c)(T);(0,tI.N)(()=>{X&&(null==U||U())},[X,U]);let J=null==(n=G.arrow)?void 0:n.x,K=null==(i=G.arrow)?void 0:i.y,Q=(null==(a=G.arrow)?void 0:a.centerOffset)!==0,[Z,tt]=r.useState();return(0,tI.N)(()=>{O&&tt(window.getComputedStyle(O).zIndex)},[O]),(0,tW.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:X?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Z,"--radix-popper-transform-origin":[null==(s=G.transformOrigin)?void 0:s.x,null==(c=G.transformOrigin)?void 0:c.y].join(" "),...(null==(u=G.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:(0,tW.jsx)(tZ,{scope:h,placedSide:$,onArrowChange:D,arrowX:J,arrowY:K,shouldHideArrow:Q,children:(0,tW.jsx)(tj.sG.div,{"data-side":$,"data-align":q,...E,ref:k,style:{...E.style,animation:X?void 0:"none"}})})})});t1.displayName=tQ;var t2="PopperArrow",t5={top:"bottom",right:"left",bottom:"top",left:"right"},t3=r.forwardRef(function(t,e){let{__scopePopper:n,...r}=t,i=t0(t2,n),o=t5[i.placedSide];return(0,tW.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,tW.jsx)(tz,{...r,ref:e,style:{...r.style,display:"block"}})})});function t9(t){return null!==t}t3.displayName=t2;var t6=t=>({name:"transformOrigin",options:t,fn(e){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:s}=e,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,u=c?0:t.arrowWidth,d=c?0:t.arrowHeight,[p,h]=t7(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(o=null==(r=s.arrow)?void 0:r.x)?o:0)+u/2,y=(null!=(l=null==(i=s.arrow)?void 0:i.y)?l:0)+d/2,w="",x="";return"bottom"===p?(w=c?m:"".concat(g,"px"),x="".concat(-d,"px")):"top"===p?(w=c?m:"".concat(g,"px"),x="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),x=c?m:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),x=c?m:"".concat(y,"px")),{data:{x:w,y:x}}}});function t7(t){let[e,n="center"]=t.split("-");return[e,n]}var t8=tU,t4=tK,et=t1,ee=t3}}]);