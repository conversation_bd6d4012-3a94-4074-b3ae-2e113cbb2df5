{"pages": {"/api/wp-proxy/cache/invalidations/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/cache/invalidations/route.js"], "/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/not-found": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/not-found.js"], "/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/page.js"], "/api/wp-proxy/posts/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/posts/route.js"], "/api/wp-proxy/categories/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/categories/route.js"], "/api/wp-proxy/auth/status/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/auth/status/route.js"], "/api/wp-proxy/iq-score/[userId]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/iq-score/[userId]/route.js"], "/api/wp-proxy/posts/batch-upvotes/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/posts/batch-upvotes/route.js"], "/login/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/login/page.js"], "/api/auth/login/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/auth/login/route.js"], "/api/wp-proxy/members/custom/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/members/custom/route.js"], "/api/wp-proxy/connections/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/connections/route.js"], "/api/wp-proxy/notifications/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/notifications/route.js"], "/api/wp-proxy/connections/status/[otherUserId]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/connections/status/[otherUserId]/route.js"], "/api/wp-proxy/messages/unread-count/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/messages/unread-count/route.js"], "/api/wp-proxy/user/assigned-vendors/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/user/assigned-vendors/route.js"], "/_not-found/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/_not-found/page.js"], "/api/wp-proxy/iq-score/leaderboard/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/wp-proxy/iq-score/leaderboard/route.js"]}}