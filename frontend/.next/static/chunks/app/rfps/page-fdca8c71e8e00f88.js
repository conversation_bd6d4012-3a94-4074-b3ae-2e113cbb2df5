(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2934],{48372:(e,s,a)=>{Promise.resolve().then(a.bind(a,75620))},75620:(e,s,a)=>{"use strict";a.d(s,{RfpsHub:()=>R});var t=a(95155),r=a(12115),l=a(66932),n=a(69074),c=a(85213),i=a(95488),d=a(54653),o=a(15968),x=a(77281);async function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,t]=e;null!=t&&""!==t&&s.append(a,t.toString())});let a=await fetch("".concat(x.tE,"/wp-proxy/rfps?").concat(s.toString()),{headers:{"Content-Type":"application/json"},credentials:"include",cache:"no-store"});if(!a.ok)throw Error("Failed to search RFPs: ".concat(a.status," ").concat(a.statusText));return a.json()}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;s.append("per_page",(e.per_page||20).toString()),s.append("page",(e.page||1).toString()),e._embed&&s.append("_embed","true"),e.status&&s.append("status",e.status);let a="".concat(x.tE,"/wp-proxy/rfps?").concat(s.toString()),t=await fetch(a,{headers:{"Content-Type":"application/json"},credentials:"include"});if(!t.ok)throw Error("Failed to fetch RFPs: ".concat(t.status));return t.json()}async function p(){let e=await m({per_page:100}),s=[];try{let e=await fetch("/api/wp-proxy/rfps/categories?per_page=100",{headers:{"Content-Type":"application/json"},credentials:"include"});e.ok&&(s=(await e.json()).map(e=>({value:e.slug,label:e.name})))}catch(e){console.error("Failed to fetch RFP categories:",e),s=[]}let a=[];try{let e=await fetch("/api/wp-proxy/rfps/states?per_page=100",{headers:{"Content-Type":"application/json"},credentials:"include"});e.ok&&(a=(await e.json()).map(e=>e.name).sort())}catch(e){console.error("Failed to fetch RFP states:",e),a=[]}let t=[];try{let e=await fetch("/api/wp-proxy/rfps/countries?per_page=100",{headers:{"Content-Type":"application/json"},credentials:"include"});e.ok&&(t=(await e.json()).map(e=>e.name).sort())}catch(e){console.error("Failed to fetch RFP countries:",e),t=[]}return{categories:s,statuses:[{value:"NEW",label:"New"},{value:"EXPIRED",label:"Expired"}],countries:t,states:a,organizations:Array.from(new Set(e.map(e=>{var s;return null==(s=e.rfp_meta)?void 0:s.rfp_organization}).filter(Boolean))).sort()}}function g(e){if(!e)return"";let s=new Date(e),a=new Date,t=Math.ceil((s.getTime()-a.getTime())/864e5);return 0===t?"Today":1===t?"Tomorrow":t>0&&t<=7?"".concat(t," days left"):s.toLocaleDateString()}function u(e){return({NEW:"New",EXPIRED:"Expired",new:"New",expired:"Expired"})[e]||e}var b=a(23227),j=a(4516),f=a(14186),v=a(33786),N=a(26126),y=a(30285),w=a(66695);function k(e){let{rfp:s,showFullDescription:a=!1}=e,{title:r,content:l,excerpt:c,date:i,rfp_meta:d}=s,{rfp_status:o="",rfp_organization:x="",rfp_source_url:h="",rfp_deadline:m=""}=d||{},p=s.rfp_categories&&s.rfp_categories.length>0?s.rfp_categories[0]:"",k=[s.rfp_states&&s.rfp_states.length>0?s.rfp_states[0]:"",s.rfp_countries&&s.rfp_countries.length>0?s.rfp_countries[0]:""].filter(Boolean).join(", "),C=new Date(i).toLocaleDateString(),_=g(m),E=(null==o?void 0:o.toUpperCase())==="EXPIRED"||"Expired"===_,S=_.includes("day")&&!E,A=()=>{let e=[r.rendered.replace(/<[^>]*>/g,""),x,k,"RFP"].filter(Boolean).join(" "),s="https://www.google.com/search?q=".concat(encodeURIComponent(e));window.open(s,"_blank")};return(0,t.jsxs)(w.Zp,{className:"group relative flex flex-col h-full transition-all duration-200 hover:shadow-md cursor-pointer ".concat(E?"opacity-75":""," ").concat(a?"rounded-b-lg":"rounded-lg"),onClick:A,role:"button",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),A())},"aria-label":"Search Google for RFP: ".concat(r.rendered.replace(/<[^>]*>/g,"")),children:[(0,t.jsx)(w.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"text-xl font-semibold text-gray-900 line-clamp-2 block",dangerouslySetInnerHTML:{__html:r.rendered}}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-sm text-gray-600",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 flex-shrink-0"}),(0,t.jsx)("span",{className:"font-medium",children:x})]}),k&&(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-1 text-sm text-gray-500",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 flex-shrink-0"}),(0,t.jsx)("span",{children:k})]}),p&&(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)(N.E,{variant:"outline",className:"".concat({marketing:"bg-purple-100 text-purple-800 border-purple-200",consulting:"bg-blue-100 text-blue-800 border-blue-200",technology:"bg-indigo-100 text-indigo-800 border-indigo-200",events:"bg-pink-100 text-pink-800 border-pink-200",design:"bg-orange-100 text-orange-800 border-orange-200",strategy:"bg-cyan-100 text-cyan-800 border-cyan-200",research:"bg-slate-100 text-slate-800 border-slate-200",development:"bg-emerald-100 text-emerald-800 border-emerald-200"}[p]||"bg-gray-100 text-gray-800 border-gray-200"," text-sm"),children:p})})]}),(0,t.jsx)("div",{className:"flex flex-col gap-2 items-end",children:o&&(0,t.jsx)(N.E,{className:"".concat((e=>{switch(e.toUpperCase()){case"NEW":return"bg-[#22c55e] text-white border-[#22c55e]";case"EXPIRED":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(o)," text-sm"),children:u(o)})})]})}),(0,t.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none z-20",children:[(0,t.jsx)("div",{className:"absolute inset-0 border-2 border-[#5cc8ff] rounded-lg"}),(0,t.jsx)("div",{className:"absolute inset-0 rounded-lg",style:{background:"linear-gradient(135deg, transparent 0%, rgba(92, 200, 255, 0.12) 20%, rgba(92, 200, 255, 0.23) 40%, rgba(92, 200, 255, 0.35) 60%, rgba(92, 200, 255, 0.47) 80%, rgba(92, 200, 255, 0.59) 100%)"}}),(0,t.jsx)("div",{className:"absolute bottom-3 right-3 pointer-events-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-full px-3 py-1.5 flex items-center gap-1.5",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-[#5cc8ff]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})}),(0,t.jsx)("span",{className:"text-xs text-gray-700 font-medium",children:"Search for this RFP on Google"})]})})]}),(0,t.jsx)(w.Wu,{className:"py-3 flex-1",children:(0,t.jsx)("div",{className:"text-sm text-gray-700 ".concat(a?"":"line-clamp-3"),dangerouslySetInnerHTML:{__html:a?l.rendered:c.rendered}})}),(0,t.jsx)(w.wL,{className:"pt-3 border-t",style:{backgroundColor:"rgba(92, 200, 255, 0.05)"},children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(n.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:["Posted ",C]})]}),m&&(0,t.jsxs)("div",{className:"flex items-center gap-1 ".concat(S?"text-orange-600 font-medium":""),children:[(0,t.jsx)(f.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:_})]})]}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:h&&!E&&(0,t.jsx)(y.$,{size:"sm",asChild:!0,children:(0,t.jsxs)("a",{href:h,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1",children:["View RFP",(0,t.jsx)(v.A,{className:"h-3 w-3"})]})})})]})})]})}function C(e){let{rfp:s}=e,{title:a,date:r,rfp_meta:l}=s,{rfp_status:c="",rfp_organization:i="",rfp_source_url:d="",rfp_deadline:o=""}=l||{},x=s.rfp_categories&&s.rfp_categories.length>0?s.rfp_categories[0]:"",h=[s.rfp_states&&s.rfp_states.length>0?s.rfp_states[0]:"",s.rfp_countries&&s.rfp_countries.length>0?s.rfp_countries[0]:""].filter(Boolean).join(", "),m=new Date(r).toLocaleDateString(),p=g(o),w=(null==c?void 0:c.toUpperCase())==="EXPIRED",k=p.includes("day"),C=()=>{let e=[a.rendered.replace(/<[^>]*>/g,""),i,h,"RFP"].filter(Boolean).join(" "),s="https://www.google.com/search?q=".concat(encodeURIComponent(e));window.open(s,"_blank")};return(0,t.jsxs)("div",{className:"group relative flex flex-col w-full p-4 bg-white border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer ".concat(w?"opacity-75":""),onClick:C,role:"button",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),C())},"aria-label":"Search Google for RFP: ".concat(a.rendered.replace(/<[^>]*>/g,"")),children:[(null==c?void 0:c.toUpperCase())==="NEW"&&(0,t.jsx)("div",{className:"absolute top-3 right-3 z-10",children:(0,t.jsx)(N.E,{className:"bg-[#22c55e] text-white text-sm py-0.5 px-2 h-5 border-[#22c55e]",children:"New"})}),(0,t.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none z-20",children:[(0,t.jsx)("div",{className:"absolute inset-0 border-2 border-[#5cc8ff] rounded-lg"}),(0,t.jsx)("div",{className:"absolute inset-0 rounded-lg",style:{background:"linear-gradient(135deg, transparent 0%, rgba(92, 200, 255, 0.12) 20%, rgba(92, 200, 255, 0.23) 40%, rgba(92, 200, 255, 0.35) 60%, rgba(92, 200, 255, 0.47) 80%, rgba(92, 200, 255, 0.59) 100%)"}}),(0,t.jsx)("div",{className:"absolute bottom-3 right-3 pointer-events-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-full px-3 py-1.5 flex items-center gap-1.5",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-[#5cc8ff]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})}),(0,t.jsx)("span",{className:"text-xs text-gray-700 font-medium",children:"Search for this RFP on Google"})]})})]}),(0,t.jsxs)("div",{className:"flex items-start gap-4 flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"min-w-0 pl-1 flex-shrink-0",style:{width:"66.67%",maxWidth:"66.67%"},children:(0,t.jsx)("div",{className:"text-base font-semibold text-gray-900 break-words mb-3",dangerouslySetInnerHTML:{__html:a.rendered}})}),x&&(0,t.jsx)("div",{className:"flex-shrink-0 hidden sm:block",children:(0,t.jsx)(N.E,{variant:"outline",className:"".concat({marketing:"bg-purple-100 text-purple-800 border-purple-200",consulting:"bg-blue-100 text-blue-800 border-blue-200",technology:"bg-indigo-100 text-indigo-800 border-indigo-200",events:"bg-pink-100 text-pink-800 border-pink-200",design:"bg-orange-100 text-orange-800 border-orange-200",strategy:"bg-cyan-100 text-cyan-800 border-cyan-200",research:"bg-slate-100 text-slate-800 border-slate-200",development:"bg-emerald-100 text-emerald-800 border-emerald-200"}[x]||"bg-gray-100 text-gray-800 border-gray-200"," text-sm"),children:x})})]}),(0,t.jsxs)("div",{className:"flex items-start gap-4 flex-1 min-w-0 pl-1",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-2 text-xs text-gray-500 flex-1",children:[i&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(b.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"break-words",children:i})]}),h&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(j.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"break-words",children:h})]}),c&&"NEW"!==c.toUpperCase()&&(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)(N.E,{className:"".concat((e=>{switch(e.toUpperCase()){case"NEW":return"bg-[#22c55e] text-white border-[#22c55e]";case"EXPIRED":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(c)," text-sm py-0 px-1 h-4"),variant:"outline",children:u(c)})})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-2 text-xs text-gray-500 flex-shrink-0 pr-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(n.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsxs)("span",{className:"whitespace-nowrap",children:["Posted ",m]})]}),o&&p&&(0,t.jsxs)("div",{className:"flex items-center gap-1 ".concat(k?"text-orange-600 font-medium":""),children:[(0,t.jsx)(f.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"whitespace-nowrap",children:p})]})]})]}),d&&!w&&(0,t.jsx)("div",{className:"flex justify-end mt-3",children:(0,t.jsx)(y.$,{size:"sm",asChild:!0,children:(0,t.jsxs)("a",{href:d,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1",children:["View RFP",(0,t.jsx)(v.A,{className:"h-3 w-3"})]})})})]})}var _=a(59409),E=a(68856),S=a(10130),A=a(59434);function R(e){let{initialRfps:s=[],initialTotal:a=0}=e,[x,m]=(0,r.useState)(s),[g,u]=(0,r.useState)(a),[b,j]=(0,r.useState)(!1),[f,v]=(0,r.useState)(null),[R,P]=(0,r.useState)(""),[F,z]=(0,r.useState)({per_page:2e3,orderby:"date",order:"desc"}),[D,T]=(0,r.useState)({categories:[],statuses:[],countries:[],states:[],organizations:[]}),[I,L]=(0,r.useState)(0),[U,W]=(0,r.useState)("compact"),[q,V]=(0,r.useState)(!1),B=(0,r.useCallback)(e=>{P(e)},[]);(0,r.useEffect)(()=>{(async()=>{try{let e=await p();T(e)}catch(e){console.error("Failed to load filter options:",e)}})()},[]),(0,r.useEffect)(()=>{let e=Object.entries(F).filter(e=>{let[s,a]=e;return"per_page"!==s&&"orderby"!==s&&"order"!==s&&"page"!==s&&null!=a&&""!==a}).length;R?L(e+1):L(e)},[F,R]);let O=(0,r.useCallback)(async()=>{j(!0),v(null);try{let e={...F,page:1,...R&&{search:R}},s=await h(e);m(s.rfps),u(s.total)}catch(e){v(e instanceof Error?e.message:"Failed to load RFPs"),console.error("Error searching RFPs:",e)}finally{j(!1)}},[F,R]);(0,r.useEffect)(()=>{O()},[O]);let $=()=>{P(""),z({per_page:2e3,orderby:"status_then_date",order:"desc"})},M=(e,s)=>{z(a=>({...a,[e]:s||void 0}))},X=(e,s)=>{z(a=>({...a,orderby:e,order:s}))};return(0,t.jsxs)("div",{className:"min-h-screen bg-[#eff1f4]",children:[(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("div",{className:"max-w-[1360px] mx-auto py-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"RFPs Hub"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Discover Request for Proposal opportunities in tourism and travel"})]}),(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[g," RFP",1!==g?"s":""," found"]})})]})})}),(0,t.jsxs)("div",{className:"max-w-[1300px] mx-auto py-6",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(S.A,{onSearch:B,placeholder:"Search by title, organization...",defaultValue:R,inputClassName:"bg-white rounded-full h-12 border border-gray-200 px-6 text-base"})}),(0,t.jsxs)(y.$,{variant:"outline",onClick:()=>V(!q),className:"rounded-full bg-white border-2 border-[#5cc8ff] text-[#3d405b] h-12 px-6 font-semibold flex items-center gap-2 shadow-none",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 text-[#3d405b]"}),"Advanced Search",I>0&&(0,t.jsx)(N.E,{variant:"secondary",className:"text-xs ml-1",children:I})]})]})}),q&&(0,t.jsxs)(w.Zp,{className:"mb-6",children:[(0,t.jsx)(w.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(w.ZB,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"h-5 w-5"}),"Advanced Filters"]}),I>0&&(0,t.jsx)(y.$,{variant:"ghost",size:"sm",onClick:$,className:"text-xs h-auto p-1",children:"Clear All"})]})}),(0,t.jsxs)(w.Wu,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Status"}),(0,t.jsxs)(_.l6,{value:F.status||"",onValueChange:e=>M("status","all"===e?void 0:e),children:[(0,t.jsx)(_.bq,{className:"w-full",children:(0,t.jsx)(_.yv,{placeholder:"All"})}),(0,t.jsxs)(_.gC,{children:[(0,t.jsx)(_.eb,{value:"all",children:"All Statuses"}),D.statuses.map(e=>(0,t.jsx)(_.eb,{value:e.value,children:e.label},e.value))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Category"}),(0,t.jsxs)(_.l6,{value:F.category||"",onValueChange:e=>M("category","all"===e?void 0:e),children:[(0,t.jsx)(_.bq,{className:"w-full",children:(0,t.jsx)(_.yv,{placeholder:"All"})}),(0,t.jsxs)(_.gC,{children:[(0,t.jsx)(_.eb,{value:"all",children:"All Categories"}),D.categories.map(e=>(0,t.jsx)(_.eb,{value:e.value,children:e.label},e.value))]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Country"}),(0,t.jsxs)(_.l6,{value:F.country||"",onValueChange:e=>M("country","all"===e?void 0:e),children:[(0,t.jsx)(_.bq,{className:"w-full",children:(0,t.jsx)(_.yv,{placeholder:"All countries"})}),(0,t.jsxs)(_.gC,{children:[(0,t.jsx)(_.eb,{value:"all",children:"All Countries"}),D.countries.map(e=>(0,t.jsx)(_.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"State/Province"}),(0,t.jsxs)(_.l6,{value:F.state||"",onValueChange:e=>M("state","all"===e?void 0:e),children:[(0,t.jsx)(_.bq,{className:"w-full",children:(0,t.jsx)(_.yv,{placeholder:"All states"})}),(0,t.jsxs)(_.gC,{className:"max-h-[200px] overflow-y-auto",children:[(0,t.jsx)(_.eb,{value:"all",children:"All States"}),D.states.map(e=>(0,t.jsx)(_.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Organization"}),(0,t.jsxs)(_.l6,{value:F.organization||"",onValueChange:e=>M("organization","all"===e?void 0:e),children:[(0,t.jsx)(_.bq,{className:"w-full",children:(0,t.jsx)(_.yv,{placeholder:"All organizations"})}),(0,t.jsxs)(_.gC,{children:[(0,t.jsx)(_.eb,{value:"all",children:"All Organizations"}),D.organizations.map(e=>(0,t.jsx)(_.eb,{value:e,children:e},e))]})]})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,t.jsxs)(_.l6,{value:"".concat(F.orderby,"-").concat(F.order),onValueChange:e=>{let[s,a]=e.split("-");X(s||"date",a||"desc")},children:[(0,t.jsx)(_.bq,{className:"w-[180px] bg-white rounded-full border h-12 px-6 font-semibold",children:(0,t.jsx)(_.yv,{})}),(0,t.jsxs)(_.gC,{children:[(0,t.jsx)(_.eb,{value:"date-desc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Newest First"})]})}),(0,t.jsx)(_.eb,{value:"date-asc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Oldest First"})]})}),(0,t.jsx)(_.eb,{value:"status_then_date-desc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Status"})]})}),(0,t.jsx)(_.eb,{value:"title-asc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Title A-Z"})]})}),(0,t.jsx)(_.eb,{value:"title-desc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Title Z-A"})]})})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center border rounded-lg overflow-hidden",children:[(0,t.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>W("card"),className:(0,A.cn)("rounded-none h-8 px-3 bg-white transition-colors","card"===U&&"bg-[#5cc8ff] !shadow-none","card"===U?"hover:bg-[#5cc8ff]":"hover:bg-[rgba(92,200,255,0.15)]"),children:(0,t.jsx)(d.A,{className:"h-4 w-4 text-[#3d405b]"})}),(0,t.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>W("compact"),className:(0,A.cn)("rounded-none h-8 px-3 bg-white transition-colors","compact"===U&&"bg-[#5cc8ff] !shadow-none","compact"===U?"hover:bg-[#5cc8ff]":"hover:bg-[rgba(92,200,255,0.15)]"),children:(0,t.jsx)(o.A,{className:"h-4 w-4 text-[#3d405b]"})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[f&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:f}),b?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-lg text-gray-600 mb-4",children:"Loading RFPs..."}),(0,t.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"})]}),Array.from({length:3}).map((e,s)=>(0,t.jsxs)(w.Zp,{className:"opacity-50",children:[(0,t.jsxs)(w.aR,{children:[(0,t.jsx)(E.E,{className:"h-6 w-3/4"}),(0,t.jsx)(E.E,{className:"h-4 w-1/2"})]}),(0,t.jsxs)(w.Wu,{children:[(0,t.jsx)(E.E,{className:"h-4 w-full mb-2"}),(0,t.jsx)(E.E,{className:"h-4 w-3/4"})]})]},s))]}):0===x.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-500 text-lg mb-2",children:"No RFPs found"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search criteria or filters"}),I>0&&(0,t.jsx)(y.$,{variant:"outline",onClick:$,className:"mt-4",children:"Clear all filters"})]}):(0,t.jsxs)("div",{className:"card"===U?"grid grid-cols-1 md:grid-cols-2 gap-6":"space-y-2",children:[x.map(e=>"card"===U?(0,t.jsx)(k,{rfp:e},e.id):(0,t.jsx)(C,{rfp:e},e.id)),x.length>0&&(0,t.jsxs)("div",{className:"text-center py-6 text-gray-500 text-sm",children:["Showing all ",x.length," RFP",1!==x.length?"s":""]})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,409,8034,870,7884,875,8441,1684,7358],()=>s(48372)),_N_E=e.O()}]);