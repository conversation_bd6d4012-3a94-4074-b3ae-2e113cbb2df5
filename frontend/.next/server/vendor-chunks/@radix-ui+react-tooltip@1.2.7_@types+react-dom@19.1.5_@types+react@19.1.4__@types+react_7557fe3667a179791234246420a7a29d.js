"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-tooltip@1.2.7_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react_7557fe3667a179791234246420a7a29d";
exports.ids = ["vendor-chunks/@radix-ui+react-tooltip@1.2.7_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react_7557fe3667a179791234246420a7a29d"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2.7_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react_7557fe3667a179791234246420a7a29d/node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-tooltip@1.2.7_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react_7557fe3667a179791234246420a7a29d/node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@19.1.5_@types+react@19.1.4__@_422595167670725011b69572eff325ac/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_0cb242d0add104272a545734161c59b1/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_9fef6ae75aba8aa40787e025c22cfd92/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.4__@types+reac_0d0cd24fc19ea4534d390a6089f37aea/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@19.1.5_@types+react@19.1.4__@types+rea_5995b29907b4034fd01e32785b01e59f/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@19.1.5_@types+react@19.1.4__@typ_0bbf573224db5af97d6828cf1a21795c/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // src/tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const isOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipProvider.useEffect\": ()=>{\n            const skipDelayTimer = skipDelayTimerRef.current;\n            return ({\n                \"TooltipProvider.useEffect\": ()=>window.clearTimeout(skipDelayTimer)\n            })[\"TooltipProvider.useEffect\"];\n        }\n    }[\"TooltipProvider.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayedRef,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                isOpenDelayedRef.current = false;\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                skipDelayTimerRef.current = window.setTimeout({\n                    \"TooltipProvider.useCallback\": ()=>isOpenDelayedRef.current = true\n                }[\"TooltipProvider.useCallback\"], skipDelayDuration);\n            }\n        }[\"TooltipProvider.useCallback\"], [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": (inTransit)=>{\n                isPointerInTransitRef.current = inTransit;\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: {\n            \"Tooltip.useControllableState\": (open2)=>{\n                if (open2) {\n                    providerContext.onOpen();\n                    document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n                } else {\n                    providerContext.onClose();\n                }\n                onOpenChange?.(open2);\n            }\n        }[\"Tooltip.useControllableState\"],\n        caller: TOOLTIP_NAME\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Tooltip.useMemo[stateAttribute]\": ()=>{\n            return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n        }\n    }[\"Tooltip.useMemo[stateAttribute]\"], [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            wasOpenDelayedRef.current = false;\n            setOpen(true);\n        }\n    }[\"Tooltip.useCallback[handleOpen]\"], [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleClose]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            setOpen(false);\n        }\n    }[\"Tooltip.useCallback[handleClose]\"], [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = window.setTimeout({\n                \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n                    wasOpenDelayedRef.current = true;\n                    setOpen(true);\n                    openTimerRef.current = 0;\n                }\n            }[\"Tooltip.useCallback[handleDelayedOpen]\"], delayDuration);\n        }\n    }[\"Tooltip.useCallback[handleDelayedOpen]\"], [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Tooltip.useEffect\": ()=>{\n            return ({\n                \"Tooltip.useEffect\": ()=>{\n                    if (openTimerRef.current) {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            })[\"Tooltip.useEffect\"];\n        }\n    }[\"Tooltip.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n                    else handleOpen();\n                }\n            }[\"Tooltip.useCallback\"], [\n                providerContext.isOpenDelayedRef,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (disableHoverableContent) {\n                        handleClose();\n                    } else {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            }[\"Tooltip.useCallback\"], [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipTrigger.useCallback[handlePointerUp]\": ()=>isPointerDownRef.current = false\n    }[\"TooltipTrigger.useCallback[handlePointerUp]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipTrigger.useEffect\": ()=>{\n            return ({\n                \"TooltipTrigger.useEffect\": ()=>document.removeEventListener(\"pointerup\", handlePointerUp)\n            })[\"TooltipTrigger.useEffect\"];\n        }\n    }[\"TooltipTrigger.useEffect\"], [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                if (context.open) {\n                    context.onClose();\n                }\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\": ()=>{\n            setPointerGraceArea(null);\n            onPointerInTransitChange(false);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleCreateGraceArea]\": (event, hoverTarget)=>{\n            const currentTarget = event.currentTarget;\n            const exitPoint = {\n                x: event.clientX,\n                y: event.clientY\n            };\n            const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n            const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n            const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n            const graceArea = getHull([\n                ...paddedExitPoints,\n                ...hoverTargetPoints\n            ]);\n            setPointerGraceArea(graceArea);\n            onPointerInTransitChange(true);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleCreateGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            return ({\n                \"TooltipContentHoverable.useEffect\": ()=>handleRemoveGraceArea()\n            })[\"TooltipContentHoverable.useEffect\"];\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (trigger && content) {\n                const handleTriggerLeave = {\n                    \"TooltipContentHoverable.useEffect.handleTriggerLeave\": (event)=>handleCreateGraceArea(event, content)\n                }[\"TooltipContentHoverable.useEffect.handleTriggerLeave\"];\n                const handleContentLeave = {\n                    \"TooltipContentHoverable.useEffect.handleContentLeave\": (event)=>handleCreateGraceArea(event, trigger)\n                }[\"TooltipContentHoverable.useEffect.handleContentLeave\"];\n                trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n                content.addEventListener(\"pointerleave\", handleContentLeave);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>{\n                        trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                        content.removeEventListener(\"pointerleave\", handleContentLeave);\n                    }\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (pointerGraceArea) {\n                const handleTrackPointerGrace = {\n                    \"TooltipContentHoverable.useEffect.handleTrackPointerGrace\": (event)=>{\n                        const target = event.target;\n                        const pointerPosition = {\n                            x: event.clientX,\n                            y: event.clientY\n                        };\n                        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                        if (hasEnteredTarget) {\n                            handleRemoveGraceArea();\n                        } else if (isPointerOutsideGraceArea) {\n                            handleRemoveGraceArea();\n                            onClose();\n                        }\n                    }\n                }[\"TooltipContentHoverable.useEffect.handleTrackPointerGrace\"];\n                document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace)\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar Slottable = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.createSlottable)(\"TooltipContent\");\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            document.addEventListener(TOOLTIP_OPEN, onClose);\n            return ({\n                \"TooltipContentImpl.useEffect\": ()=>document.removeEventListener(TOOLTIP_OPEN, onClose)\n            })[\"TooltipContentImpl.useEffect\"];\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            if (context.trigger) {\n                const handleScroll = {\n                    \"TooltipContentImpl.useEffect.handleScroll\": (event)=>{\n                        const target = event.target;\n                        if (target?.contains(context.trigger)) onClose();\n                    }\n                }[\"TooltipContentImpl.useEffect.handleScroll\"];\n                window.addEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n                return ({\n                    \"TooltipContentImpl.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                            capture: true\n                        })\n                })[\"TooltipContentImpl.useEffect\"];\n            }\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2.7_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react_7557fe3667a179791234246420a7a29d/node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ })

};
;