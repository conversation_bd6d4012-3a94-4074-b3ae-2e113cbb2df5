"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3750],{5041:(t,e,n)=>{n.d(e,{n:()=>c});var r=n(12115),s=n(34560),i=n(7165),o=n(25910),a=n(52020),u=class extends o.Q{#t;#e=void 0;#n;#r;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#s()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,a.f8)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#n,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,a.EN)(e.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#n?.state.status==="pending"&&this.#n.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#n?.removeObserver(this)}onMutationUpdate(t){this.#s(),this.#i(t)}getCurrentResult(){return this.#e}reset(){this.#n?.removeObserver(this),this.#n=void 0,this.#s(),this.#i()}mutate(t,e){return this.#r=e,this.#n?.removeObserver(this),this.#n=this.#t.getMutationCache().build(this.#t,this.options),this.#n.addObserver(this),this.#n.execute(t)}#s(){let t=this.#n?.state??(0,s.$)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#i(t){i.jG.batch(()=>{if(this.#r&&this.hasListeners()){let e=this.#e.variables,n=this.#e.context;t?.type==="success"?(this.#r.onSuccess?.(t.data,e,n),this.#r.onSettled?.(t.data,null,e,n)):t?.type==="error"&&(this.#r.onError?.(t.error,e,n),this.#r.onSettled?.(void 0,t.error,e,n))}this.listeners.forEach(t=>{t(this.#e)})})}},l=n(26715);function c(t,e){let n=(0,l.jE)(e),[s]=r.useState(()=>new u(n,t));r.useEffect(()=>{s.setOptions(t)},[s,t]);let o=r.useSyncExternalStore(r.useCallback(t=>s.subscribe(i.jG.batchCalls(t)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),c=r.useCallback((t,e)=>{s.mutate(t,e).catch(a.lQ)},[s]);if(o.error&&(0,a.GU)(s.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:c,mutateAsync:o.mutate}}},12318:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(19946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},15452:(t,e,n)=>{n.d(e,{G$:()=>V,Hs:()=>R,UC:()=>tn,VY:()=>ts,ZL:()=>tt,bL:()=>z,bm:()=>ti,hE:()=>tr,hJ:()=>te,l9:()=>X});var r=n(12115),s=n(85185),i=n(6101),o=n(46081),a=n(61285),u=n(5845),l=n(19178),c=n(25519),d=n(34378),p=n(28905),h=n(63655),f=n(92293),m=n(93795),g=n(38168),y=n(99708),v=n(95155),b="Dialog",[x,R]=(0,o.A)(b),[O,C]=x(b),D=t=>{let{__scopeDialog:e,children:n,open:s,defaultOpen:i,onOpenChange:o,modal:l=!0}=t,c=r.useRef(null),d=r.useRef(null),[p,h]=(0,u.i)({prop:s,defaultProp:null!=i&&i,onChange:o,caller:b});return(0,v.jsx)(O,{scope:e,triggerRef:c,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:p,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(t=>!t),[h]),modal:l,children:n})};D.displayName=b;var j="DialogTrigger",M=r.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,o=C(j,n),a=(0,i.s)(e,o.triggerRef);return(0,v.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Z(o.open),...r,ref:a,onClick:(0,s.m)(t.onClick,o.onOpenToggle)})});M.displayName=j;var E="DialogPortal",[w,k]=x(E,{forceMount:void 0}),I=t=>{let{__scopeDialog:e,forceMount:n,children:s,container:i}=t,o=C(E,e);return(0,v.jsx)(w,{scope:e,forceMount:n,children:r.Children.map(s,t=>(0,v.jsx)(p.C,{present:n||o.open,children:(0,v.jsx)(d.Z,{asChild:!0,container:i,children:t})}))})};I.displayName=E;var N="DialogOverlay",_=r.forwardRef((t,e)=>{let n=k(N,t.__scopeDialog),{forceMount:r=n.forceMount,...s}=t,i=C(N,t.__scopeDialog);return i.modal?(0,v.jsx)(p.C,{present:r||i.open,children:(0,v.jsx)(F,{...s,ref:e})}):null});_.displayName=N;var A=(0,y.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,s=C(N,n);return(0,v.jsx)(m.A,{as:A,allowPinchZoom:!0,shards:[s.contentRef],children:(0,v.jsx)(h.sG.div,{"data-state":Z(s.open),...r,ref:e,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",S=r.forwardRef((t,e)=>{let n=k(P,t.__scopeDialog),{forceMount:r=n.forceMount,...s}=t,i=C(P,t.__scopeDialog);return(0,v.jsx)(p.C,{present:r||i.open,children:i.modal?(0,v.jsx)(G,{...s,ref:e}):(0,v.jsx)(q,{...s,ref:e})})});S.displayName=P;var G=r.forwardRef((t,e)=>{let n=C(P,t.__scopeDialog),o=r.useRef(null),a=(0,i.s)(e,n.contentRef,o);return r.useEffect(()=>{let t=o.current;if(t)return(0,g.Eq)(t)},[]),(0,v.jsx)(T,{...t,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(t.onCloseAutoFocus,t=>{var e;t.preventDefault(),null==(e=n.triggerRef.current)||e.focus()}),onPointerDownOutside:(0,s.m)(t.onPointerDownOutside,t=>{let e=t.detail.originalEvent,n=0===e.button&&!0===e.ctrlKey;(2===e.button||n)&&t.preventDefault()}),onFocusOutside:(0,s.m)(t.onFocusOutside,t=>t.preventDefault())})}),q=r.forwardRef((t,e)=>{let n=C(P,t.__scopeDialog),s=r.useRef(!1),i=r.useRef(!1);return(0,v.jsx)(T,{...t,ref:e,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{var r,o;null==(r=t.onCloseAutoFocus)||r.call(t,e),e.defaultPrevented||(s.current||null==(o=n.triggerRef.current)||o.focus(),e.preventDefault()),s.current=!1,i.current=!1},onInteractOutside:e=>{var r,o;null==(r=t.onInteractOutside)||r.call(t,e),e.defaultPrevented||(s.current=!0,"pointerdown"===e.detail.originalEvent.type&&(i.current=!0));let a=e.target;(null==(o=n.triggerRef.current)?void 0:o.contains(a))&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&i.current&&e.preventDefault()}})}),T=r.forwardRef((t,e)=>{let{__scopeDialog:n,trapFocus:s,onOpenAutoFocus:o,onCloseAutoFocus:a,...u}=t,d=C(P,n),p=r.useRef(null),h=(0,i.s)(e,p);return(0,f.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,v.jsx)(l.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":Z(d.open),...u,ref:h,onDismiss:()=>d.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:d.titleId}),(0,v.jsx)(Y,{contentRef:p,descriptionId:d.descriptionId})]})]})}),U="DialogTitle",B=r.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,s=C(U,n);return(0,v.jsx)(h.sG.h2,{id:s.titleId,...r,ref:e})});B.displayName=U;var K="DialogDescription",L=r.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,s=C(K,n);return(0,v.jsx)(h.sG.p,{id:s.descriptionId,...r,ref:e})});L.displayName=K;var H="DialogClose",W=r.forwardRef((t,e)=>{let{__scopeDialog:n,...r}=t,i=C(H,n);return(0,v.jsx)(h.sG.button,{type:"button",...r,ref:e,onClick:(0,s.m)(t.onClick,()=>i.onOpenChange(!1))})});function Z(t){return t?"open":"closed"}W.displayName=H;var Q="DialogTitleWarning",[V,$]=(0,o.q)(Q,{contentName:P,titleName:U,docsSlug:"dialog"}),J=t=>{let{titleId:e}=t,n=$(Q),s="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(s))},[s,e]),null},Y=t=>{let{contentRef:e,descriptionId:n}=t,s=$("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(s.contentName,"}.");return r.useEffect(()=>{var t;let r=null==(t=e.current)?void 0:t.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,e,n]),null},z=D,X=M,tt=I,te=_,tn=S,tr=B,ts=L,ti=W},75494:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(19946).A)("user-minus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])}}]);