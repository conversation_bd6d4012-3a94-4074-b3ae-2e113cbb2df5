(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3759],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>n,t:()=>i});var s=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,s=e.map(e=>{let s=l(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():l(e[t],null)}}}}function n(...e){return s.useCallback(i(...e),e)}},26715:(e,t,r)=>{"use strict";r.d(t,{Ht:()=>a,jE:()=>n});var s=r(12115),l=r(95155),i=s.createContext(void 0),n=e=>{let t=s.useContext(i);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},a=e=>{let{client:t,children:r}=e;return s.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,l.jsx)(i.Provider,{value:t,children:r})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>a});var s=r(95155);r(12115);var l=r(99708),i=r(74466),n=r(59434);let a=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:i,asChild:o=!1,...u}=e,d=o?l.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(a({variant:r,size:i,className:t})),...u})}},47765:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(95155),l=r(30285),i=r(66695),n=r(64614),a=r(26715),o=r(93853);function u(){let{isLoggedIn:e,isLoading:t,user:r,error:u}=(0,n.w1)(),d=(0,a.jE)();return(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Authentication Debug"}),(0,s.jsxs)(i.Zp,{className:"mb-6",children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Current Auth Status"}),(0,s.jsx)(i.BT,{children:"Debug information for authentication state"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Is Logged In:"})," ",e?"✅ Yes":"❌ No"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Is Loading:"})," ",t?"⏳ Yes":"✅ No"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"User ID:"})," ",(null==r?void 0:r.id)||"None"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Username:"})," ",(null==r?void 0:r.username)||"None"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Display Name:"})," ",(null==r?void 0:r.display_name)||"None"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Email:"})," ",(null==r?void 0:r.email)||"None"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Error:"})," ",(null==u?void 0:u.message)||"None"]})]})})]}),(0,s.jsxs)(i.Zp,{className:"mb-6",children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Debug Actions"}),(0,s.jsx)(i.BT,{children:"Tools to force refresh authentication state"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"space-x-4",children:[(0,s.jsx)(l.$,{onClick:()=>{d.invalidateQueries({queryKey:o.lH.auth.status()})},variant:"outline",children:"Force Refresh Auth"}),(0,s.jsx)(l.$,{onClick:()=>{d.removeQueries({queryKey:o.lH.auth.status()})},variant:"outline",children:"Clear Auth Cache"})]})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Instructions"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("ol",{className:"list-decimal list-inside space-y-2",children:[(0,s.jsx)("li",{children:"Check if you see correct authentication status above"}),(0,s.jsx)("li",{children:'If not logged in but you should be, click "Force Refresh Auth"'}),(0,s.jsx)("li",{children:'If still not working, click "Clear Auth Cache" then "Force Refresh Auth"'}),(0,s.jsx)("li",{children:"Check browser cookies for wordpress_logged_in_* cookie"})]})})]})]})})}},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(52596),l=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.QP)((0,s.$)(t))}},64614:(e,t,r)=>{"use strict";r.d(t,{iZ:()=>n,w1:()=>i});var s=r(32960),l=r(93853);function i(){let{data:e,isLoading:t,error:r,refetch:i}=(0,s.I)({queryKey:l.lH.auth.status(),queryFn:async()=>{let e=await fetch("/api/wp-proxy/auth/status",{headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok)return{isAuthenticated:!1,user:null};let t=await e.json();return{isAuthenticated:t.isLoggedIn||t.isAuthenticated||!1,user:t.wpUser||t.user||null}},staleTime:3e5,gcTime:6e5,retry:!1,refetchOnWindowFocus:!1,refetchOnReconnect:!0});return{isLoggedIn:(null==e?void 0:e.isAuthenticated)||!1,isLoading:t,user:(null==e?void 0:e.user)||null,error:r,refetch:i}}function n(){let{isLoggedIn:e,isLoading:t,user:r,error:s}=i();return{user:r||null,isAuthenticated:e||!1,isLoading:t,error:s}}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>u,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>a,wL:()=>c});var s=r(95155),l=r(12115),i=r(59434);let n=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...l})});n.displayName="Card";let a=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...l})});a.displayName="CardHeader";let o=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...l})});o.displayName="CardTitle";let u=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...l})});u.displayName="CardDescription";let d=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6",r),...l})});d.displayName="CardContent";let c=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6",r),...l})});c.displayName="CardFooter"},88767:(e,t,r)=>{Promise.resolve().then(r.bind(r,47765))},93853:(e,t,r)=>{"use strict";r.d(t,{WG:()=>i,lH:()=>l,qQ:()=>s});let s=new(r(87017)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:3,refetchOnWindowFocus:!1,refetchOnReconnect:!1},mutations:{retry:1}}}),l={posts:{all:["posts"],lists:()=>[...l.posts.all,"list"],list:e=>[...l.posts.lists(),e],details:()=>[...l.posts.all,"detail"],detail:e=>[...l.posts.details(),e],userPosts:(e,t,r)=>[...l.posts.all,"user",e,{page:t,perPage:r}],byCategory:(e,t)=>[...l.posts.all,"category",e,t]},auth:{all:["auth"],status:()=>[...l.auth.all,"status"],profile:()=>[...l.auth.all,"profile"]},members:{all:["members"],lists:()=>[...l.members.all,"list"],list:e=>[...l.members.lists(),e],details:()=>[...l.members.all,"detail"],detail:e=>[...l.members.details(),e],profile:e=>[...l.members.all,"profile",e]},connections:{all:["connections"],status:e=>[...l.connections.all,"status",e],list:e=>[...l.connections.all,"list",e],pending:()=>[...l.connections.all,"pending"]},iqScore:{all:["iqScore"],user:e=>[...l.iqScore.all,"user",e],me:()=>[...l.iqScore.all,"me"],leaderboard:()=>[...l.iqScore.all,"leaderboard"],ranks:()=>[...l.iqScore.all,"ranks"]},forum:{all:["forum"],questions:e=>[...l.forum.all,"questions",e],question:e=>[...l.forum.all,"question",e],userQuestions:e=>[...l.forum.all,"user",e],comments:e=>[...l.forum.all,"comments",e]},messages:{all:["messages"],conversations:()=>[...l.messages.all,"conversations"],conversation:e=>[...l.messages.all,"conversation",e],unreadCount:()=>[...l.messages.all,"unreadCount"]},vendors:{all:["vendors"],lists:()=>[...l.vendors.all,"list"],list:e=>[...l.vendors.lists(),e],details:()=>[...l.vendors.all,"detail"],detail:e=>[...l.vendors.details(),e],posts:e=>[...l.vendors.all,"posts",e],categories:()=>[...l.vendors.all,"categories"]},categories:{all:["categories"],list:()=>[...l.categories.all,"list"]},bookmarks:{all:["bookmarks"],user:e=>[...l.bookmarks.all,"user",e]},notifications:{all:["notifications"],list:()=>[...l.notifications.all,"list"]}},i={allPosts:()=>s.invalidateQueries({queryKey:l.posts.all}),post:e=>s.invalidateQueries({queryKey:l.posts.detail(e)}),userPosts:e=>s.invalidateQueries({queryKey:[...l.posts.all,"user",e]}),auth:()=>s.invalidateQueries({queryKey:l.auth.all}),connections:()=>s.invalidateQueries({queryKey:l.connections.all}),iqScores:()=>s.invalidateQueries({queryKey:l.iqScore.all}),forum:()=>s.invalidateQueries({queryKey:l.forum.all}),messages:()=>s.invalidateQueries({queryKey:l.messages.all}),notifications:()=>s.invalidateQueries({queryKey:l.notifications.all})}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>a,Dc:()=>u,TL:()=>n});var s=r(12115),l=r(6101),i=r(95155);function n(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...i}=e;if(s.isValidElement(r)){var n;let e,a,o=(n=r,(a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(a=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),u=function(e,t){let r={...t};for(let s in t){let l=e[s],i=t[s];/^on[A-Z]/.test(s)?l&&i?r[s]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(r[s]=l):"style"===s?r[s]={...l,...i}:"className"===s&&(r[s]=[l,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==s.Fragment&&(u.ref=t?(0,l.t)(t,o):o),s.cloneElement(r,u)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:l,...n}=e,a=s.Children.toArray(l),o=a.find(d);if(o){let e=o.props.children,l=a.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...n,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...n,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var a=n("Slot"),o=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function d(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,5272,8441,1684,7358],()=>t(88767)),_N_E=e.O()}]);