"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5794],{15452:(e,n,t)=>{t.d(n,{G$:()=>K,Hs:()=>O,UC:()=>et,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>el,hE:()=>er,hJ:()=>en,l9:()=>X});var r=t(12115),o=t(85185),l=t(6101),a=t(46081),i=t(61285),s=t(5845),u=t(19178),d=t(25519),c=t(34378),f=t(28905),p=t(63655),m=t(92293),g=t(93795),v=t(38168),y=t(99708),N=t(95155),D="Dialog",[h,O]=(0,a.A)(D),[b,R]=h(D),w=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:l,onOpenChange:a,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:D});return(0,N.jsx)(b,{scope:n,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:t})};w.displayName=D;var x="DialogTrigger",C=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,a=R(x,t),i=(0,l.s)(n,a.triggerRef);return(0,N.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":Z(a.open),...r,ref:i,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});C.displayName=x;var I="DialogPortal",[j,E]=h(I,{forceMount:void 0}),M=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:l}=e,a=R(I,n);return(0,N.jsx)(j,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,N.jsx)(f.C,{present:t||a.open,children:(0,N.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};M.displayName=I;var A="DialogOverlay",T=r.forwardRef((e,n)=>{let t=E(A,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,l=R(A,e.__scopeDialog);return l.modal?(0,N.jsx)(f.C,{present:r||l.open,children:(0,N.jsx)(F,{...o,ref:n})}):null});T.displayName=A;var _=(0,y.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=R(A,t);return(0,N.jsx)(g.A,{as:_,allowPinchZoom:!0,shards:[o.contentRef],children:(0,N.jsx)(p.sG.div,{"data-state":Z(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",U=r.forwardRef((e,n)=>{let t=E(P,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,l=R(P,e.__scopeDialog);return(0,N.jsx)(f.C,{present:r||l.open,children:l.modal?(0,N.jsx)(k,{...o,ref:n}):(0,N.jsx)(L,{...o,ref:n})})});U.displayName=P;var k=r.forwardRef((e,n)=>{let t=R(P,e.__scopeDialog),a=r.useRef(null),i=(0,l.s)(n,t.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,N.jsx)(S,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null==(n=t.triggerRef.current)||n.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,n)=>{let t=R(P,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,N.jsx)(S,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,n),n.defaultPrevented||(o.current||null==(a=t.triggerRef.current)||a.focus(),n.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:n=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"===n.detail.originalEvent.type&&(l.current=!0));let i=n.target;(null==(a=t.triggerRef.current)?void 0:a.contains(i))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&l.current&&n.preventDefault()}})}),S=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=e,c=R(P,t),f=r.useRef(null),p=(0,l.s)(n,f);return(0,m.Oh)(),(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,N.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)($,{titleId:c.titleId}),(0,N.jsx)(z,{contentRef:f,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",G=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=R(W,t);return(0,N.jsx)(p.sG.h2,{id:o.titleId,...r,ref:n})});G.displayName=W;var q="DialogDescription",B=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=R(q,t);return(0,N.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:n})});B.displayName=q;var H="DialogClose",V=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,l=R(H,t);return(0,N.jsx)(p.sG.button,{type:"button",...r,ref:n,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}V.displayName=H;var J="DialogTitleWarning",[K,Y]=(0,a.q)(J,{contentName:P,titleName:W,docsSlug:"dialog"}),$=e=>{let{titleId:n}=e,t=Y(J),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&(document.getElementById(n)||console.error(o))},[o,n]),null},z=e=>{let{contentRef:n,descriptionId:t}=e,o=Y("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=n.current)?void 0:e.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(l))},[l,n,t]),null},Q=w,X=C,ee=M,en=T,et=U,er=G,eo=B,el=V},28905:(e,n,t)=>{t.d(n,{C:()=>a});var r=t(12115),o=t(6101),l=t(52712),a=e=>{let{present:n,children:t}=e,a=function(e){var n,t;let[o,a]=r.useState(),s=r.useRef(null),u=r.useRef(e),d=r.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=i(s.current);d.current="mounted"===c?e:"none"},[c]),(0,l.N)(()=>{let n=s.current,t=u.current;if(t!==e){let r=d.current,o=i(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):t&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,l.N)(()=>{if(o){var e;let n,t=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(s.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(d.current=i(s.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,a(e)},[])}}(n),s="function"==typeof t?t({present:a.isPresent}):r.Children.only(t),u=(0,o.s)(a.ref,function(e){var n,t;let r=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(t=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof t||a.isPresent?r.cloneElement(s,{ref:u}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},40968:(e,n,t)=>{t.d(n,{b:()=>i});var r=t(12115),o=t(63655),l=t(95155),a=r.forwardRef((e,n)=>(0,l.jsx)(o.sG.label,{...e,ref:n,onMouseDown:n=>{var t;n.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));a.displayName="Label";var i=a},55868:(e,n,t)=>{t.d(n,{A:()=>r});let r=(0,t(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])}}]);