{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images|fonts|svg|manifest.json|robots.txt|sw.js|workbox-.*\\.js).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images|fonts|svg|manifest.json|robots.txt|sw.js|workbox-.*\\.js).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "kPeSF2gEQuCjfu22LPD6B", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HfsZ6Perfz63WUekb+dK7FY2Xg1bXORubTm2r7hlmHU=", "__NEXT_PREVIEW_MODE_ID": "d37e03e68d211065653c07aac7b02677", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "21a8f8a0778737bed06583e4136d9746281556ae767930c991bd167524496ef1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "79eb4a5795c73e37ca10302a6ff71fbc38d06fccac2112f49d7ca986ff43ec17"}}}, "functions": {}, "sortedMiddleware": ["/"]}