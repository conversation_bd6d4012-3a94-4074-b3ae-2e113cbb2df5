globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/RightSidebar.tsx":{"*":{"id":"(ssr)/./src/components/layout/RightSidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Sidebar.tsx":{"*":{"id":"(ssr)/./src/components/layout/Sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/messaging/chat-window.tsx":{"*":{"id":"(ssr)/./src/components/messaging/chat-window.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/messaging/message-list.tsx":{"*":{"id":"(ssr)/./src/components/messaging/message-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/messaging/messaging-provider.tsx":{"*":{"id":"(ssr)/./src/components/messaging/messaging-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/CacheInvalidationProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/CacheInvalidationProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/ReactQueryProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/ReactQueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/ErrorBoundary.tsx":{"*":{"id":"(ssr)/./src/components/ui/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/IQPointsToast.tsx":{"*":{"id":"(ssr)/./src/components/ui/IQPointsToast.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/WelcomeModal.tsx":{"*":{"id":"(ssr)/./src/components/ui/WelcomeModal.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/auth-context.tsx":{"*":{"id":"(ssr)/./src/contexts/auth-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/connection-context.tsx":{"*":{"id":"(ssr)/./src/contexts/connection-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/iq-score-context.tsx":{"*":{"id":"(ssr)/./src/contexts/iq-score-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/NotificationContext.tsx":{"*":{"id":"(ssr)/./src/contexts/NotificationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/UpvoteContext.tsx":{"*":{"id":"(ssr)/./src/contexts/UpvoteContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/HomeClient.tsx":{"*":{"id":"(ssr)/./src/app/HomeClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/script.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/RightSidebar.tsx":{"id":"(app-pages-browser)/./src/components/layout/RightSidebar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Sidebar.tsx":{"id":"(app-pages-browser)/./src/components/layout/Sidebar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/messaging/chat-window.tsx":{"id":"(app-pages-browser)/./src/components/messaging/chat-window.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/messaging/message-list.tsx":{"id":"(app-pages-browser)/./src/components/messaging/message-list.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/messaging/messaging-provider.tsx":{"id":"(app-pages-browser)/./src/components/messaging/messaging-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/providers/CacheInvalidationProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/CacheInvalidationProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/providers/ReactQueryProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/ReactQueryProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/ui/ErrorBoundary.tsx":{"id":"(app-pages-browser)/./src/components/ui/ErrorBoundary.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/ui/IQPointsToast.tsx":{"id":"(app-pages-browser)/./src/components/ui/IQPointsToast.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/ui/WelcomeModal.tsx":{"id":"(app-pages-browser)/./src/components/ui/WelcomeModal.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/auth-context.tsx":{"id":"(app-pages-browser)/./src/contexts/auth-context.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/connection-context.tsx":{"id":"(app-pages-browser)/./src/contexts/connection-context.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/iq-score-context.tsx":{"id":"(app-pages-browser)/./src/contexts/iq-score-context.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/NotificationContext.tsx":{"id":"(app-pages-browser)/./src/contexts/NotificationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/UpvoteContext.tsx":{"id":"(app-pages-browser)/./src/contexts/UpvoteContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/app-dir/link.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/HomeClient.tsx":{"id":"(app-pages-browser)/./src/app/HomeClient.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/login/page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/":[],"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/not-found":[],"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(rsc)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/RightSidebar.tsx":{"*":{"id":"(rsc)/./src/components/layout/RightSidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Sidebar.tsx":{"*":{"id":"(rsc)/./src/components/layout/Sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/messaging/chat-window.tsx":{"*":{"id":"(rsc)/./src/components/messaging/chat-window.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/messaging/message-list.tsx":{"*":{"id":"(rsc)/./src/components/messaging/message-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/messaging/messaging-provider.tsx":{"*":{"id":"(rsc)/./src/components/messaging/messaging-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/CacheInvalidationProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/CacheInvalidationProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/ReactQueryProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/ReactQueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/ErrorBoundary.tsx":{"*":{"id":"(rsc)/./src/components/ui/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/IQPointsToast.tsx":{"*":{"id":"(rsc)/./src/components/ui/IQPointsToast.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/WelcomeModal.tsx":{"*":{"id":"(rsc)/./src/components/ui/WelcomeModal.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/auth-context.tsx":{"*":{"id":"(rsc)/./src/contexts/auth-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/connection-context.tsx":{"*":{"id":"(rsc)/./src/contexts/connection-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/iq-score-context.tsx":{"*":{"id":"(rsc)/./src/contexts/iq-score-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/NotificationContext.tsx":{"*":{"id":"(rsc)/./src/contexts/NotificationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/UpvoteContext.tsx":{"*":{"id":"(rsc)/./src/contexts/UpvoteContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/HomeClient.tsx":{"*":{"id":"(rsc)/./src/app/HomeClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}