(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6239],{6654:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"useMergedRef",{enumerable:!0,get:function(){return l}});let r=t(12115);function l(e,s){let t=(0,r.useRef)(null),l=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=t.current;e&&(t.current=null,e());let s=l.current;s&&(l.current=null,s())}else e&&(t.current=a(e,r)),s&&(l.current=a(s,r))},[e,s])}function a(e,s){if("function"!=typeof e)return e.current=s,()=>{e.current=null};{let t=e(s);return"function"==typeof t?t:()=>e(null)}}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},17580:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,s,t)=>{"use strict";t.d(s,{A:()=>u});var r=t(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),n=e=>{let s=a(e);return s.charAt(0).toUpperCase()+s.slice(1)},i=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},o=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:m,...h}=e;return(0,r.createElement)("svg",{ref:s,...c,width:l,height:l,stroke:t,strokeWidth:n?24*Number(a)/Number(l):a,className:i("lucide",d),...!u&&!o(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(u)?u:[u]])}),u=(e,s)=>{let t=(0,r.forwardRef)((t,a)=>{let{className:o,...c}=t;return(0,r.createElement)(d,{ref:a,iconNode:s,className:i("lucide-".concat(l(n(e))),"lucide-".concat(e),o),...c})});return t.displayName=n(e),t}},21855:(e,s,t)=>{Promise.resolve().then(t.bind(t,88853))},88853:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(95155),l=t(66766),a=t(17580);function n(e){let{images:s}=e,t=e=>e.sizes.large||e.sizes.medium||e.url||"",a=e=>1===e?"grid-cols-1 max-w-[140px]":2===e?"grid-cols-2 max-w-[284px]":3===e?"grid-cols-3 max-w-[426px]":"grid-cols-4",n=(e=>{let s=[];for(let t=0;t<e.length;t+=4)s.push(e.slice(t,t+4));return s})(s);return(0,r.jsx)("div",{className:"w-full py-10 px-10 rounded-lg",style:{backgroundColor:"rgba(170, 215, 238, 0.6)"},children:(0,r.jsxs)("div",{className:"space-y-2",children:[n.map((e,s)=>(0,r.jsx)("div",{className:"grid gap-2 mx-auto ".concat(a(e.length)),children:e.map((e,s)=>(0,r.jsx)("div",{className:"aspect-square overflow-hidden bg-muted rounded-lg max-w-[140px] max-h-[140px]",children:(0,r.jsx)(l.default,{src:t(e),alt:e.alt||"Person image",width:140,height:140,className:"object-cover w-full h-full",priority:!1})},e.ID||s))},s)),s.length>10&&(0,r.jsxs)("div",{className:"text-center text-sm text-gray-500 mt-2",children:["+",s.length-10," more images"]})]})})}function i(){return(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("header",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-[#3d405b] mb-4",children:"\uD83E\uDDEA People on the Move - Layout Preview"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:"Development preview showing all possible image grid layouts (1-10 images) for People on the Move posts."}),(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[(0,r.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"\uD83D\uDD27 Testing Notes:"}),(0,r.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Template:"})," Uses 4-image variation as template for all layouts"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Background:"})," Light blue (#aad7ee) at 60% opacity with 40px horizontal padding"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Images:"})," All square, same size (300x300px), centered rows"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Rows:"})," Maximum 4 images per row, centered based on count"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Simplified Code:"})," Single logic handles all variations (1-10+ images)"]})]})]})]}),(0,r.jsx)("div",{className:"space-y-8",children:Array.from({length:10},(e,s)=>{let t=s+1,l=Array.from({length:t},(e,s)=>{let t=100+s;return{ID:t,url:"https://picsum.photos/400/400?random=".concat(t),alt:"Person ".concat(s+1),title:"Person Image ".concat(s+1),sizes:{full:"https://picsum.photos/800/800?random=".concat(t),large:"https://picsum.photos/600/600?random=".concat(t),medium:"https://picsum.photos/400/400?random=".concat(t),thumbnail:"https://picsum.photos/200/200?random=".concat(t)}}});return(0,r.jsxs)("div",{className:"bg-white shadow-lg border rounded-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-3 border-b",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-gray-700",children:[t," Image",t>1?"s":""," Layout"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 bg-purple-100 px-3 py-1 rounded-full",children:[(0,r.jsx)(a.A,{className:"h-4 w-4 text-purple-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-purple-700",children:"People on the Move"})]})]})}),(0,r.jsx)(n,{images:l}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-[#3d405b] mb-2",children:["Sample People on the Move Post with ",t," Image",t>1?"s":""]}),(0,r.jsxs)("p",{className:"text-gray-600 mb-4",children:["This is a sample layout showing how the simplified grid behaves with"," ",t," professional headshot",t>1?"s":"",". All images are the same size (300x300px) and centered based on count per row."]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Layout:"," ",1===t?"Single centered image":2===t?"Two centered images":3===t?"Three centered images":4===t?"Four images (full row)":t<=8?"".concat(Math.ceil(t/4)," rows, last row centered"):"Multiple rows, max 4 per row, all centered"]})]})]},t)})}),(0,r.jsxs)("div",{className:"mt-12 p-6 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"\uD83D\uDCCB Simplified Implementation:"}),(0,r.jsxs)("div",{className:"text-sm text-blue-700 space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Template Approach:"})," Uses 4-image layout as base template for all variations"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Consistent Sizing:"})," All images are 300x300px squares, no size variations"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Smart Centering:"})," Rows automatically center based on image count (1-4 per row)"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Background:"})," Light blue (#aad7ee) at 60% opacity with 40px horizontal padding"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Simplified Logic:"})," Single function handles all layouts, much cleaner code"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Responsive:"})," Grids adapt while maintaining consistent square aspect ratios"]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6766,8441,1684,7358],()=>s(21855)),_N_E=e.O()}]);