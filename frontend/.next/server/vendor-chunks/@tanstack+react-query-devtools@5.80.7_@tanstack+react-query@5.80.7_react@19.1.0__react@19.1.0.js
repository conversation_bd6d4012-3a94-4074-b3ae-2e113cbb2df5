"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0";
exports.ids = ["vendor-chunks/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.80.7/node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtools auto */ // src/ReactQueryDevtools.tsx\n\n\n\n\nfunction ReactQueryDevtools(props) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { buttonPosition, position, initialIsOpen, errorTypes, styleNonce, shadowDOMTarget } = props;\n    const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtools({\n        client: queryClient,\n        queryFlavor: \"React Query\",\n        version: \"5\",\n        onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.onlineManager,\n        buttonPosition,\n        position,\n        initialIsOpen,\n        errorTypes,\n        styleNonce,\n        shadowDOMTarget\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setClient(queryClient);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        queryClient,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (buttonPosition) {\n                devtools.setButtonPosition(buttonPosition);\n            }\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        buttonPosition,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (position) {\n                devtools.setPosition(position);\n            }\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        position,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setInitialIsOpen(initialIsOpen || false);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        initialIsOpen,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setErrorTypes(errorTypes || []);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        errorTypes,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (ref.current) {\n                devtools.mount(ref.current);\n            }\n            return ({\n                \"ReactQueryDevtools.useEffect\": ()=>{\n                    devtools.unmount();\n                }\n            })[\"ReactQueryDevtools.useEffect\"];\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        devtools\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        dir: \"ltr\",\n        className: \"tsqd-parent-container\",\n        ref\n    });\n}\n //# sourceMappingURL=ReactQueryDevtools.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.80.7/node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-devtools@5.80.0/node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtoolsPanel auto */ // src/ReactQueryDevtoolsPanel.tsx\n\n\n\n\nfunction ReactQueryDevtoolsPanel(props) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { errorTypes, styleNonce, shadowDOMTarget } = props;\n    const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtoolsPanel({\n        client: queryClient,\n        queryFlavor: \"React Query\",\n        version: \"5\",\n        onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.onlineManager,\n        buttonPosition: \"bottom-left\",\n        position: \"bottom\",\n        initialIsOpen: true,\n        errorTypes,\n        styleNonce,\n        shadowDOMTarget,\n        onClose: props.onClose\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setClient(queryClient);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        queryClient,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setOnClose(props.onClose ?? ({\n                \"ReactQueryDevtoolsPanel.useEffect\": ()=>{}\n            })[\"ReactQueryDevtoolsPanel.useEffect\"]);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        props.onClose,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setErrorTypes(errorTypes || []);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        errorTypes,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            if (ref.current) {\n                devtools.mount(ref.current);\n            }\n            return ({\n                \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n                    devtools.unmount();\n                }\n            })[\"ReactQueryDevtoolsPanel.useEffect\"];\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        devtools\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        style: {\n            height: \"500px\",\n            ...props.style\n        },\n        className: \"tsqd-parent-container\",\n        ref\n    });\n}\n //# sourceMappingURL=ReactQueryDevtoolsPanel.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/index.js":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/index.js ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools2),\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel2)\n/* harmony export */ });\n/* harmony import */ var _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ReactQueryDevtools.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\");\n/* harmony import */ var _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ReactQueryDevtoolsPanel.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtools,ReactQueryDevtoolsPanel auto */ // src/index.ts\n\n\nvar ReactQueryDevtools2 =  false ? 0 : _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__.ReactQueryDevtools;\nvar ReactQueryDevtoolsPanel2 =  false ? 0 : _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__.ReactQueryDevtoolsPanel;\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRhbnN0YWNrK3JlYWN0LXF1ZXJ5LWRldnRvb2xzQDUuODAuN19AdGFuc3RhY2srcmVhY3QtcXVlcnlANS44MC43X3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMvYnVpbGQvbW9kZXJuL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRTBCO0FBQ0s7QUFFeEIsSUFBTUEsc0JBQ1gsTUFBeUIsR0FDckIsQ0FFQyxHQUNRO0FBRVIsSUFBTUMsMkJBQ1gsTUFBeUIsR0FDckIsQ0FFQyxHQUNhIiwic291cmNlcyI6WyIvVm9sdW1lcy9NYWMgU3RvcmFnZS9Mb2NhbCBTaXRlcy90b3VyaXNtaXEtaGVhZGxlc3MvYXBwL3B1YmxpYy93cC1jb250ZW50L3RoZW1lcy9zcmMvaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCAqIGFzIERldnRvb2xzIGZyb20gJy4vUmVhY3RRdWVyeURldnRvb2xzJ1xuaW1wb3J0ICogYXMgRGV2dG9vbHNQYW5lbCBmcm9tICcuL1JlYWN0UXVlcnlEZXZ0b29sc1BhbmVsJ1xuXG5leHBvcnQgY29uc3QgUmVhY3RRdWVyeURldnRvb2xzOiAodHlwZW9mIERldnRvb2xzKVsnUmVhY3RRdWVyeURldnRvb2xzJ10gPVxuICBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ2RldmVsb3BtZW50J1xuICAgID8gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgfVxuICAgIDogRGV2dG9vbHMuUmVhY3RRdWVyeURldnRvb2xzXG5cbmV4cG9ydCBjb25zdCBSZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbDogKHR5cGVvZiBEZXZ0b29sc1BhbmVsKVsnUmVhY3RRdWVyeURldnRvb2xzUGFuZWwnXSA9XG4gIHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAnZGV2ZWxvcG1lbnQnXG4gICAgPyBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBudWxsXG4gICAgICB9XG4gICAgOiBEZXZ0b29sc1BhbmVsLlJlYWN0UXVlcnlEZXZ0b29sc1BhbmVsXG4iXSwibmFtZXMiOlsiUmVhY3RRdWVyeURldnRvb2xzIiwiUmVhY3RRdWVyeURldnRvb2xzUGFuZWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query-devtools@5.80.7_@tanstack+react-query@5.80.7_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/index.js\n");

/***/ })

};
;