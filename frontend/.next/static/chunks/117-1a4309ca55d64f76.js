"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[117],{15452:(e,t,n)=>{n.d(t,{G$:()=>J,Hs:()=>D,UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>X});var r=n(12115),o=n(85185),a=n(6101),l=n(46081),i=n(61285),u=n(5845),s=n(19178),d=n(25519),c=n(34378),f=n(28905),p=n(63655),m=n(92293),v=n(93795),g=n(38168),y=n(99708),N=n(95155),w="Dialog",[h,D]=(0,l.A)(w),[E,b]=h(w),x=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:l,modal:s=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:w});return(0,N.jsx)(E,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};x.displayName=w;var O="DialogTrigger",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=b(O,n),i=(0,a.s)(t,l.triggerRef);return(0,N.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Z(l.open),...r,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});R.displayName=O;var j="DialogPortal",[I,C]=h(j,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,l=b(j,t);return(0,N.jsx)(I,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,N.jsx)(f.C,{present:n||l.open,children:(0,N.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};A.displayName=j;var S="DialogOverlay",M=r.forwardRef((e,t)=>{let n=C(S,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=b(S,e.__scopeDialog);return a.modal?(0,N.jsx)(f.C,{present:r||a.open,children:(0,N.jsx)(_,{...o,ref:t})}):null});M.displayName=S;var T=(0,y.TL)("DialogOverlay.RemoveScroll"),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(S,n);return(0,N.jsx)(v.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,N.jsx)(p.sG.div,{"data-state":Z(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),L="DialogContent",F=r.forwardRef((e,t)=>{let n=C(L,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=b(L,e.__scopeDialog);return(0,N.jsx)(f.C,{present:r||a.open,children:a.modal?(0,N.jsx)(P,{...o,ref:t}):(0,N.jsx)(k,{...o,ref:t})})});F.displayName=L;var P=r.forwardRef((e,t)=>{let n=b(L,e.__scopeDialog),l=r.useRef(null),i=(0,a.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,N.jsx)(U,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),k=r.forwardRef((e,t)=>{let n=b(L,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,N.jsx)(U,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),U=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...u}=e,c=b(L,n),f=r.useRef(null),p=(0,a.s)(t,f);return(0,m.Oh)(),(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,N.jsx)(s.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...u,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(Y,{titleId:c.titleId}),(0,N.jsx)($,{contentRef:f,descriptionId:c.descriptionId})]})]})}),G="DialogTitle",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(G,n);return(0,N.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});W.displayName=G;var B="DialogDescription",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(B,n);return(0,N.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});V.displayName=B;var q="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(q,n);return(0,N.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}H.displayName=q;var z="DialogTitleWarning",[J,K]=(0,l.q)(z,{contentName:L,titleName:G,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,n=K(z),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(a))},[a,t,n]),null},Q=x,X=R,ee=A,et=M,en=F,er=W,eo=V,ea=H},22436:(e,t,n)=>{var r=n(12115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,l=r.useEffect,i=r.useLayoutEffect,u=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=a({inst:{value:n,getSnapshot:t}}),o=r[0].inst,d=r[1];return i(function(){o.value=n,o.getSnapshot=t,s(o)&&d({inst:o})},[e,n,t]),l(function(){return s(o)&&d({inst:o}),e(function(){s(o)&&d({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),a=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),s=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(u.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=d.current,o=i(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=i(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),s=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:s}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},38564:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},40968:(e,t,n)=>{n.d(t,{b:()=>i});var r=n(12115),o=n(63655),a=n(95155),l=r.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},49033:(e,t,n)=>{e.exports=n(22436)},51154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54011:(e,t,n)=>{n.d(t,{H4:()=>x,_V:()=>b,bL:()=>E});var r=n(12115),o=n(46081),a=n(39033),l=n(52712),i=n(63655),u=n(49033);function s(){return()=>{}}var d=n(95155),c="Avatar",[f,p]=(0,o.A)(c),[m,v]=f(c),g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[a,l]=r.useState("idle");return(0,d.jsx)(m,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,d.jsx)(i.sG.span,{...o,ref:t})})});g.displayName=c;var y="AvatarImage",N=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:c=()=>{},...f}=e,p=v(y,n),m=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,a=(0,u.useSyncExternalStore)(s,()=>!0,()=>!1),i=r.useRef(null),d=a?(i.current||(i.current=new window.Image),i.current):null,[c,f]=r.useState(()=>D(d,e));return(0,l.N)(()=>{f(D(d,e))},[d,e]),(0,l.N)(()=>{let e=e=>()=>{f(e)};if(!d)return;let t=e("loaded"),r=e("error");return d.addEventListener("load",t),d.addEventListener("error",r),n&&(d.referrerPolicy=n),"string"==typeof o&&(d.crossOrigin=o),()=>{d.removeEventListener("load",t),d.removeEventListener("error",r)}},[d,o,n]),c}(o,f),g=(0,a.c)(e=>{c(e),p.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==m&&g(m)},[m,g]),"loaded"===m?(0,d.jsx)(i.sG.img,{...f,ref:t,src:o}):null});N.displayName=y;var w="AvatarFallback",h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...a}=e,l=v(w,n),[u,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==l.imageLoadingStatus?(0,d.jsx)(i.sG.span,{...a,ref:t}):null});function D(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}h.displayName=w;var E=g,b=N,x=h}}]);