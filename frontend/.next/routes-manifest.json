{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/blog/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/blog(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/leadership/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/leadership(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/case-studies/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/case-studies(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/book/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/book(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/course/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/course(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/event/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/event(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/news/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/news(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/podcast/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/podcast(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/presentation/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/presentation(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/press-release/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/press-release(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/templates/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/templates(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/video/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/video(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/webinar/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/webinar(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/whitepaper/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)/whitepaper(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/recent-job/:path*", "destination": "/jobs", "statusCode": 308, "regex": "^(?!/_next)/recent-job(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/job/:path*", "destination": "/jobs", "statusCode": 308, "regex": "^(?!/_next)/job(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/:year(\\d{4})/:month(\\d{2})/:day(\\d{2})/:slug*", "destination": "/posts/:slug*", "statusCode": 308, "regex": "^(?!/_next)(?:/(\\d{4}))(?:/(\\d{2}))(?:/(\\d{2}))(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/blog-post/:path*", "destination": "/category/news/:path*", "statusCode": 308, "regex": "^(?!/_next)/blog-post(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/sitemap_index.xml", "destination": "/sitemap.xml", "statusCode": 308, "regex": "^(?!/_next)/sitemap_index\\.xml(?:/)?$"}, {"source": "/:category(blog-post|book|case-study|course|event|forum|job|leadership|news|podcast|presentation|press-release|templates|video|webinar|whitepaper)-sitemap.xml", "destination": "/sitemap.xml", "statusCode": 308, "regex": "^(?!/_next)(?:/(blog-post|book|case-study|course|event|forum|job|leadership|news|podcast|presentation|press-release|templates|video|webinar|whitepaper))-sitemap\\.xml(?:/)?$"}, {"source": "/wp-admin/:path*", "destination": "/login", "statusCode": 307, "regex": "^(?!/_next)/wp-admin(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/wp-login.php", "destination": "/login", "statusCode": 308, "regex": "^(?!/_next)/wp-login\\.php(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/posts/user/[userId]", "regex": "^/api/posts/user/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/posts/user/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/api/stripe/verify-session/[sessionId]", "regex": "^/api/stripe/verify\\-session/([^/]+?)(?:/)?$", "routeKeys": {"nxtPsessionId": "nxtPsessionId"}, "namedRegex": "^/api/stripe/verify\\-session/(?<nxtPsessionId>[^/]+?)(?:/)?$"}, {"page": "/api/user/[id]/meta", "regex": "^/api/user/([^/]+?)/meta(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/user/(?<nxtPid>[^/]+?)/meta(?:/)?$"}, {"page": "/api/wp-proxy/connections/status/[otherUserId]", "regex": "^/api/wp\\-proxy/connections/status/([^/]+?)(?:/)?$", "routeKeys": {"nxtPotherUserId": "nxtPotherUserId"}, "namedRegex": "^/api/wp\\-proxy/connections/status/(?<nxtPotherUserId>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/forum/user/[userId]", "regex": "^/api/wp\\-proxy/forum/user/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/wp\\-proxy/forum/user/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/forum/[id]", "regex": "^/api/wp\\-proxy/forum/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/forum/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/forum/[id]/comments", "regex": "^/api/wp\\-proxy/forum/([^/]+?)/comments(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/forum/(?<nxtPid>[^/]+?)/comments(?:/)?$"}, {"page": "/api/wp-proxy/iq-score/[userId]", "regex": "^/api/wp\\-proxy/iq\\-score/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/wp\\-proxy/iq\\-score/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/jobs/[slug]", "regex": "^/api/wp\\-proxy/jobs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/wp\\-proxy/jobs/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/legacy-messages/conversation/[id]", "regex": "^/api/wp\\-proxy/legacy\\-messages/conversation/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/legacy\\-messages/conversation/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/messages/can-message/[userId]", "regex": "^/api/wp\\-proxy/messages/can\\-message/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/wp\\-proxy/messages/can\\-message/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/messages/conversation/[userId]", "regex": "^/api/wp\\-proxy/messages/conversation/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/wp\\-proxy/messages/conversation/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/posts/by-category/[slug]", "regex": "^/api/wp\\-proxy/posts/by\\-category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/wp\\-proxy/posts/by\\-category/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/posts/user/[userId]", "regex": "^/api/wp\\-proxy/posts/user/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/wp\\-proxy/posts/user/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/posts/[postId]", "regex": "^/api/wp\\-proxy/posts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPpostId": "nxtPpostId"}, "namedRegex": "^/api/wp\\-proxy/posts/(?<nxtPpostId>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/posts/[postId]/comments", "regex": "^/api/wp\\-proxy/posts/([^/]+?)/comments(?:/)?$", "routeKeys": {"nxtPpostId": "nxtPpostId"}, "namedRegex": "^/api/wp\\-proxy/posts/(?<nxtPpostId>[^/]+?)/comments(?:/)?$"}, {"page": "/api/wp-proxy/posts/[postId]/upvote", "regex": "^/api/wp\\-proxy/posts/([^/]+?)/upvote(?:/)?$", "routeKeys": {"nxtPpostId": "nxtPpostId"}, "namedRegex": "^/api/wp\\-proxy/posts/(?<nxtPpostId>[^/]+?)/upvote(?:/)?$"}, {"page": "/api/wp-proxy/posts/[postId]/upvotes", "regex": "^/api/wp\\-proxy/posts/([^/]+?)/upvotes(?:/)?$", "routeKeys": {"nxtPpostId": "nxtPpostId"}, "namedRegex": "^/api/wp\\-proxy/posts/(?<nxtPpostId>[^/]+?)/upvotes(?:/)?$"}, {"page": "/api/wp-proxy/rfps/by-id/[id]", "regex": "^/api/wp\\-proxy/rfps/by\\-id/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/rfps/by\\-id/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/rfps/[slug]", "regex": "^/api/wp\\-proxy/rfps/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/wp\\-proxy/rfps/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/users/[userId]/comments-count", "regex": "^/api/wp\\-proxy/users/([^/]+?)/comments\\-count(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/wp\\-proxy/users/(?<nxtPuserId>[^/]+?)/comments\\-count(?:/)?$"}, {"page": "/api/wp-proxy/vendor-subscription/[id]", "regex": "^/api/wp\\-proxy/vendor\\-subscription/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/vendor\\-subscription/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/vendor-subscription/[id]/customer-portal", "regex": "^/api/wp\\-proxy/vendor\\-subscription/([^/]+?)/customer\\-portal(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/vendor\\-subscription/(?<nxtPid>[^/]+?)/customer\\-portal(?:/)?$"}, {"page": "/api/wp-proxy/vendor-upgrade/[id]", "regex": "^/api/wp\\-proxy/vendor\\-upgrade/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/vendor\\-upgrade/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/vendors/by-id/[id]", "regex": "^/api/wp\\-proxy/vendors/by\\-id/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/vendors/by\\-id/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/vendors/by-id/[id]/request-upgrade-link", "regex": "^/api/wp\\-proxy/vendors/by\\-id/([^/]+?)/request\\-upgrade\\-link(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/wp\\-proxy/vendors/by\\-id/(?<nxtPid>[^/]+?)/request\\-upgrade\\-link(?:/)?$"}, {"page": "/api/wp-proxy/vendors/[slug]", "regex": "^/api/wp\\-proxy/vendors/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/wp\\-proxy/vendors/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/wp-proxy/vendors/[slug]/posts", "regex": "^/api/wp\\-proxy/vendors/([^/]+?)/posts(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/wp\\-proxy/vendors/(?<nxtPslug>[^/]+?)/posts(?:/)?$"}, {"page": "/category/[slug]", "regex": "^/category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/category/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/forum/[id]", "regex": "^/forum/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/forum/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/jobs/[slug]", "regex": "^/jobs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/jobs/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/posts/[slug]", "regex": "^/posts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/posts/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/profile/[username]", "regex": "^/profile/([^/]+?)(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername"}, "namedRegex": "^/profile/(?<nxtPusername>[^/]+?)(?:/)?$"}, {"page": "/rfps/[slug]", "regex": "^/rfps/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/rfps/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/vendors/[slug]", "regex": "^/vendors/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/vendors/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/cache-debug", "regex": "^/cache\\-debug(?:/)?$", "routeKeys": {}, "namedRegex": "^/cache\\-debug(?:/)?$"}, {"page": "/debug-auth", "regex": "^/debug\\-auth(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-auth(?:/)?$"}, {"page": "/dev-people-preview", "regex": "^/dev\\-people\\-preview(?:/)?$", "routeKeys": {}, "namedRegex": "^/dev\\-people\\-preview(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/forum", "regex": "^/forum(?:/)?$", "routeKeys": {}, "namedRegex": "^/forum(?:/)?$"}, {"page": "/forum/new", "regex": "^/forum/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/forum/new(?:/)?$"}, {"page": "/jobs", "regex": "^/jobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/jobs(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/member-directory", "regex": "^/member\\-directory(?:/)?$", "routeKeys": {}, "namedRegex": "^/member\\-directory(?:/)?$"}, {"page": "/people", "regex": "^/people(?:/)?$", "routeKeys": {}, "namedRegex": "^/people(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/rfps", "regex": "^/rfps(?:/)?$", "routeKeys": {}, "namedRegex": "^/rfps(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/sitemap-test.xml", "regex": "^/sitemap\\-test\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\-test\\.xml(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/sponsorships", "regex": "^/sponsorships(?:/)?$", "routeKeys": {}, "namedRegex": "^/sponsorships(?:/)?$"}, {"page": "/terms-of-use", "regex": "^/terms\\-of\\-use(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-of\\-use(?:/)?$"}, {"page": "/test-error-dialogs", "regex": "^/test\\-error\\-dialogs(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-error\\-dialogs(?:/)?$"}, {"page": "/test-iq-notifications", "regex": "^/test\\-iq\\-notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-iq\\-notifications(?:/)?$"}, {"page": "/test-sidebar-height", "regex": "^/test\\-sidebar\\-height(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-sidebar\\-height(?:/)?$"}, {"page": "/vendor-upgrade", "regex": "^/vendor\\-upgrade(?:/)?$", "routeKeys": {}, "namedRegex": "^/vendor\\-upgrade(?:/)?$"}, {"page": "/vendor-upgrade-success", "regex": "^/vendor\\-upgrade\\-success(?:/)?$", "routeKeys": {}, "namedRegex": "^/vendor\\-upgrade\\-success(?:/)?$"}, {"page": "/vendors", "regex": "^/vendors(?:/)?$", "routeKeys": {}, "namedRegex": "^/vendors(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}