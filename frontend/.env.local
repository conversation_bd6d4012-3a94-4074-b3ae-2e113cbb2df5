# WordPress REST API Configuration
NEXT_PUBLIC_WORDPRESS_API_URL=http://tourismiq-headless.local

NEXT_PUBLIC_WORDPRESS_URL=http://tourismiq-headless.local
#NEXT_PUBLIC_WORDPRESS_API_URL=http://tourismiq.local
#NEXT_PUBLIC_WORDPRESS_URL=http://tourismiq.local
WORDPRESS_USERNAME=grobear
WORDPRESS_APPLICATION_PASSWORD='u6jI UT93 Y76e YUJJ sC0f PcF8'

# Frontend URL (used by socket server for CORS) - Update this to your WPEngine frontend URL
NEXT_PUBLIC_FRONTEND_URL=https://hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com

# Socket server connection (for the frontend to connect to)
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
# Socket server configuration
SOCKET_PORT=3000

# Stripe
NEXT_PUBLIC_STRIPE_PAYMENT_LINK=https://buy.stripe.com/test_dRm5kFgeB9SU6z72gr2cg00