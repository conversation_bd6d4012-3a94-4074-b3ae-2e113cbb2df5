(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7635],{1763:(t,e,r)=>{"use strict";var i;Object.defineProperty(e,"__esModule",{value:!0});let n=r(46120),s=r(49231);!function(t){t.compose=function(t={},e={},r=!1){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});let i=n(e);for(let n in r||(i=Object.keys(i).reduce((t,e)=>(null!=i[e]&&(t[e]=i[e]),t),{})),t)void 0!==t[n]&&void 0===e[n]&&(i[n]=t[n]);return Object.keys(i).length>0?i:void 0},t.diff=function(t={},e={}){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});let r=Object.keys(t).concat(Object.keys(e)).reduce((r,i)=>(s(t[i],e[i])||(r[i]=void 0===e[i]?null:e[i]),r),{});return Object.keys(r).length>0?r:void 0},t.invert=function(t={},e={}){t=t||{};let r=Object.keys(e).reduce((r,i)=>(e[i]!==t[i]&&void 0!==t[i]&&(r[i]=e[i]),r),{});return Object.keys(t).reduce((r,i)=>(t[i]!==e[i]&&void 0===e[i]&&(r[i]=null),r),r)},t.transform=function(t,e,r=!1){if("object"!=typeof t)return e;if("object"!=typeof e)return;if(!r)return e;let i=Object.keys(e).reduce((r,i)=>(void 0===t[i]&&(r[i]=e[i]),r),{});return Object.keys(i).length>0?i:void 0}}(i||(i={})),e.default=i},7635:(t,e,r)=>{"use strict";r.d(e,{default:()=>sv});var i={};r.r(i),r.d(i,{Attributor:()=>t4,AttributorStore:()=>es,BlockBlot:()=>eb,ClassAttributor:()=>et,ContainerBlot:()=>ev,EmbedBlot:()=>eN,InlineBlot:()=>eg,LeafBlot:()=>ec,ParentBlot:()=>ef,Registry:()=>t8,Scope:()=>t5,ScrollBlot:()=>ew,StyleAttributor:()=>ei,TextBlot:()=>ek});let n=function(t,e){return t===e||t!=t&&e!=e},s=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1};var l=Array.prototype.splice;function o(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}o.prototype.clear=function(){this.__data__=[],this.size=0},o.prototype.delete=function(t){var e=this.__data__,r=s(e,t);return!(r<0)&&(r==e.length-1?e.pop():l.call(e,r,1),--this.size,!0)},o.prototype.get=function(t){var e=this.__data__,r=s(e,t);return r<0?void 0:e[r][1]},o.prototype.has=function(t){return s(this.__data__,t)>-1},o.prototype.set=function(t,e){var r=this.__data__,i=s(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this};var a="object"==typeof global&&global&&global.Object===Object&&global,c="object"==typeof self&&self&&self.Object===Object&&self,h=a||c||Function("return this")(),u=h.Symbol,d=Object.prototype,f=d.hasOwnProperty,p=d.toString,g=u?u.toStringTag:void 0;let m=function(t){var e=f.call(t,g),r=t[g];try{t[g]=void 0;var i=!0}catch(t){}var n=p.call(t);return i&&(e?t[g]=r:delete t[g]),n};var b=Object.prototype.toString,y=u?u.toStringTag:void 0;let v=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":y&&y in Object(t)?m(t):b.call(t)},x=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)},N=function(t){if(!x(t))return!1;var e=v(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e};var E=h["__core-js_shared__"],A=function(){var t=/[^.]+$/.exec(E&&E.keys&&E.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),w=Function.prototype.toString;let q=function(t){if(null!=t){try{return w.call(t)}catch(t){}try{return t+""}catch(t){}}return""};var k=/^\[object .+?Constructor\]$/,_=Object.prototype,L=Function.prototype.toString,O=_.hasOwnProperty,S=RegExp("^"+L.call(O).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");let T=function(t){return!!x(t)&&(!A||!(A in t))&&(N(t)?S:k).test(q(t))},j=function(t,e){var r=null==t?void 0:t[e];return T(r)?r:void 0};var C=j(h,"Map"),R=j(Object,"create"),I=Object.prototype.hasOwnProperty,M=Object.prototype.hasOwnProperty;function B(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}B.prototype.clear=function(){this.__data__=R?R(null):{},this.size=0},B.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e},B.prototype.get=function(t){var e=this.__data__;if(R){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return I.call(e,t)?e[t]:void 0},B.prototype.has=function(t){var e=this.__data__;return R?void 0!==e[t]:M.call(e,t)},B.prototype.set=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=R&&void 0===e?"__lodash_hash_undefined__":e,this};let D=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t},U=function(t,e){var r=t.__data__;return D(e)?r["string"==typeof e?"string":"hash"]:r.map};function P(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function z(t){var e=this.__data__=new o(t);this.size=e.size}P.prototype.clear=function(){this.size=0,this.__data__={hash:new B,map:new(C||o),string:new B}},P.prototype.delete=function(t){var e=U(this,t).delete(t);return this.size-=!!e,e},P.prototype.get=function(t){return U(this,t).get(t)},P.prototype.has=function(t){return U(this,t).has(t)},P.prototype.set=function(t,e){var r=U(this,t),i=r.size;return r.set(t,e),this.size+=+(r.size!=i),this},z.prototype.clear=function(){this.__data__=new o,this.size=0},z.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},z.prototype.get=function(t){return this.__data__.get(t)},z.prototype.has=function(t){return this.__data__.has(t)},z.prototype.set=function(t,e){var r=this.__data__;if(r instanceof o){var i=r.__data__;if(!C||i.length<199)return i.push([t,e]),this.size=++r.size,this;r=this.__data__=new P(i)}return r.set(t,e),this.size=r.size,this};var F=function(){try{var t=j(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();let $=function(t,e,r){"__proto__"==e&&F?F(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r},H=function(t,e,r){(void 0===r||n(t[e],r))&&(void 0!==r||e in t)||$(t,e,r)},V=function(t,e,r){for(var i=-1,n=Object(t),s=r(t),l=s.length;l--;){var o=s[++i];if(!1===e(n[o],o,n))break}return t};var K="object"==typeof exports&&exports&&!exports.nodeType&&exports,W=K&&"object"==typeof module&&module&&!module.nodeType&&module,Z=W&&W.exports===K?h.Buffer:void 0,G=Z?Z.allocUnsafe:void 0;let X=function(t,e){if(e)return t.slice();var r=t.length,i=G?G(r):new t.constructor(r);return t.copy(i),i};var Y=h.Uint8Array;let Q=function(t){var e=new t.constructor(t.byteLength);return new Y(e).set(new Y(t)),e},J=function(t,e){var r=e?Q(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)},tt=function(t,e){var r=-1,i=t.length;for(e||(e=Array(i));++r<i;)e[r]=t[r];return e};var te=Object.create,tr=function(){function t(){}return function(e){if(!x(e))return{};if(te)return te(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();let ti=function(t,e){return function(r){return t(e(r))}};var tn=ti(Object.getPrototypeOf,Object),ts=Object.prototype;let tl=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||ts)},to=function(t){return"function"!=typeof t.constructor||tl(t)?{}:tr(tn(t))},ta=function(t){return null!=t&&"object"==typeof t},tc=function(t){return ta(t)&&"[object Arguments]"==v(t)};var th=Object.prototype,tu=th.hasOwnProperty,td=th.propertyIsEnumerable,tf=tc(function(){return arguments}())?tc:function(t){return ta(t)&&tu.call(t,"callee")&&!td.call(t,"callee")},tp=Array.isArray;let tg=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff},tm=function(t){return null!=t&&tg(t.length)&&!N(t)};var tb="object"==typeof exports&&exports&&!exports.nodeType&&exports,ty=tb&&"object"==typeof module&&module&&!module.nodeType&&module,tv=ty&&ty.exports===tb?h.Buffer:void 0;let tx=(tv?tv.isBuffer:void 0)||function(){return!1};var tN=Object.prototype,tE=Function.prototype.toString,tA=tN.hasOwnProperty,tw=tE.call(Object);let tq=function(t){if(!ta(t)||"[object Object]"!=v(t))return!1;var e=tn(t);if(null===e)return!0;var r=tA.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&tE.call(r)==tw};var tk={};tk["[object Float32Array]"]=tk["[object Float64Array]"]=tk["[object Int8Array]"]=tk["[object Int16Array]"]=tk["[object Int32Array]"]=tk["[object Uint8Array]"]=tk["[object Uint8ClampedArray]"]=tk["[object Uint16Array]"]=tk["[object Uint32Array]"]=!0,tk["[object Arguments]"]=tk["[object Array]"]=tk["[object ArrayBuffer]"]=tk["[object Boolean]"]=tk["[object DataView]"]=tk["[object Date]"]=tk["[object Error]"]=tk["[object Function]"]=tk["[object Map]"]=tk["[object Number]"]=tk["[object Object]"]=tk["[object RegExp]"]=tk["[object Set]"]=tk["[object String]"]=tk["[object WeakMap]"]=!1;let t_=function(t){return function(e){return t(e)}};var tL="object"==typeof exports&&exports&&!exports.nodeType&&exports,tO=tL&&"object"==typeof module&&module&&!module.nodeType&&module,tS=tO&&tO.exports===tL&&a.process,tT=function(){try{var t=tO&&tO.require&&tO.require("util").types;if(t)return t;return tS&&tS.binding&&tS.binding("util")}catch(t){}}(),tj=tT&&tT.isTypedArray,tC=tj?t_(tj):function(t){return ta(t)&&tg(t.length)&&!!tk[v(t)]};let tR=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]};var tI=Object.prototype.hasOwnProperty;let tM=function(t,e,r){var i=t[e];tI.call(t,e)&&n(i,r)&&(void 0!==r||e in t)||$(t,e,r)},tB=function(t,e,r,i){var n=!r;r||(r={});for(var s=-1,l=e.length;++s<l;){var o=e[s],a=i?i(r[o],t[o],o,r,t):void 0;void 0===a&&(a=t[o]),n?$(r,o,a):tM(r,o,a)}return r},tD=function(t,e){for(var r=-1,i=Array(t);++r<t;)i[r]=e(r);return i};var tU=/^(?:0|[1-9]\d*)$/;let tP=function(t,e){var r=typeof t;return!!(e=null==e?0x1fffffffffffff:e)&&("number"==r||"symbol"!=r&&tU.test(t))&&t>-1&&t%1==0&&t<e};var tz=Object.prototype.hasOwnProperty;let tF=function(t,e){var r=tp(t),i=!r&&tf(t),n=!r&&!i&&tx(t),s=!r&&!i&&!n&&tC(t),l=r||i||n||s,o=l?tD(t.length,String):[],a=o.length;for(var c in t)(e||tz.call(t,c))&&!(l&&("length"==c||n&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||tP(c,a)))&&o.push(c);return o},t$=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e};var tH=Object.prototype.hasOwnProperty;let tV=function(t){if(!x(t))return t$(t);var e=tl(t),r=[];for(var i in t)"constructor"==i&&(e||!tH.call(t,i))||r.push(i);return r},tK=function(t){return tm(t)?tF(t,!0):tV(t)},tW=function(t,e,r,i,n,s,l){var o=tR(t,r),a=tR(e,r),c=l.get(a);if(c)return void H(t,r,c);var h=s?s(o,a,r+"",t,e,l):void 0,u=void 0===h;if(u){var d=tp(a),f=!d&&tx(a),p=!d&&!f&&tC(a);(h=a,d||f||p)?tp(o)?h=o:ta(o)&&tm(o)?h=tt(o):f?(u=!1,h=X(a,!0)):p?(u=!1,h=J(a,!0)):h=[]:tq(a)||tf(a)?(h=o,tf(o))?h=tB(o,tK(o)):(!x(o)||N(o))&&(h=to(a)):u=!1}u&&(l.set(a,h),n(h,a,i,s,l),l.delete(a)),H(t,r,h)},tZ=function t(e,r,i,n,s){e!==r&&V(r,function(l,o){if(s||(s=new z),x(l))tW(e,r,o,i,t,n,s);else{var a=n?n(tR(e,o),l,o+"",e,r,s):void 0;void 0===a&&(a=l),H(e,o,a)}},tK)},tG=function(t){return t},tX=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)};var tY=Math.max,tQ=Date.now,tJ=function(t){var e=0,r=0;return function(){var i=tQ(),n=16-(i-r);if(r=i,n>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(F?function(t,e){return F(t,"toString",{configurable:!0,enumerable:!1,value:function(){return e},writable:!0})}:tG);let t1=function(t,e){var r;return tJ((r=tY(void 0===(r=e)?t.length-1:r,0),function(){for(var e=arguments,i=-1,n=tY(e.length-r,0),s=Array(n);++i<n;)s[i]=e[r+i];i=-1;for(var l=Array(r+1);++i<r;)l[i]=e[i];return l[r]=tG(s),tX(t,this,l)}),t+"")},t0=function(t,e,r){if(!x(r))return!1;var i=typeof e;return("number"==i?!!(tm(r)&&tP(e,r.length)):"string"==i&&e in r)&&n(r[e],t)};var t2=function(t){return t1(function(e,r){var i=-1,n=r.length,s=n>1?r[n-1]:void 0,l=n>2?r[2]:void 0;for(s=t.length>3&&"function"==typeof s?(n--,s):void 0,l&&t0(r[0],r[1],l)&&(s=n<3?void 0:s,n=1),e=Object(e);++i<n;){var o=r[i];o&&t(e,o,i,s)}return e})}(function(t,e,r){tZ(t,e,r)}),t5=(t=>(t[t.TYPE=3]="TYPE",t[t.LEVEL=12]="LEVEL",t[t.ATTRIBUTE=13]="ATTRIBUTE",t[t.BLOT=14]="BLOT",t[t.INLINE=7]="INLINE",t[t.BLOCK=11]="BLOCK",t[t.BLOCK_BLOT=10]="BLOCK_BLOT",t[t.INLINE_BLOT=6]="INLINE_BLOT",t[t.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",t[t.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",t[t.ANY=15]="ANY",t))(t5||{});class t4{constructor(t,e,r={}){this.attrName=t,this.keyName=e;let i=t5.TYPE&t5.ATTRIBUTE;this.scope=null!=r.scope?r.scope&t5.LEVEL|i:t5.ATTRIBUTE,null!=r.whitelist&&(this.whitelist=r.whitelist)}static keys(t){return Array.from(t.attributes).map(t=>t.name)}add(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)}canAdd(t,e){return null==this.whitelist||("string"==typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1)}remove(t){t.removeAttribute(this.keyName)}value(t){let e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class t3 extends Error{constructor(t){super(t="[Parchment] "+t),this.message=t,this.name=this.constructor.name}}let t6=class t{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(null==t)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let r=null;try{r=t.parentNode}catch{return null}return this.find(r,e)}return null}create(e,r,i){let n=this.query(r);if(null==n)throw new t3(`Unable to create ${r} blot`);let s=r instanceof Node||r.nodeType===Node.TEXT_NODE?r:n.create(i),l=new n(e,s,i);return t.blots.set(l.domNode,l),l}find(e,r=!1){return t.find(e,r)}query(t,e=t5.ANY){let r;return"string"==typeof t?r=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?r=this.types.text:"number"==typeof t?t&t5.LEVEL&t5.BLOCK?r=this.types.block:t&t5.LEVEL&t5.INLINE&&(r=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(t=>!!(r=this.classes[t])),r=r||this.tags[t.tagName]),null==r?null:"scope"in r&&e&t5.LEVEL&r.scope&&e&t5.TYPE&r.scope?r:null}register(...t){return t.map(t=>{let e="blotName"in t,r="attrName"in t;if(!e&&!r)throw new t3("Invalid definition");if(e&&"abstract"===t.blotName)throw new t3("Cannot register abstract class");let i=e?t.blotName:r?t.attrName:void 0;return this.types[i]=t,r?"string"==typeof t.keyName&&(this.attributes[t.keyName]=t):e&&(t.className&&(this.classes[t.className]=t),t.tagName&&(Array.isArray(t.tagName)?t.tagName=t.tagName.map(t=>t.toUpperCase()):t.tagName=t.tagName.toUpperCase(),(Array.isArray(t.tagName)?t.tagName:[t.tagName]).forEach(e=>{(null==this.tags[e]||null==t.className)&&(this.tags[e]=t)}))),t})}};t6.blots=new WeakMap;let t8=t6;function t9(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter(t=>0===t.indexOf(`${e}-`))}class t7 extends t4{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map(t=>t.split("-").slice(0,-1).join("-"))}add(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0)}remove(t){t9(t,this.keyName).forEach(e=>{t.classList.remove(e)}),0===t.classList.length&&t.removeAttribute("class")}value(t){let e=(t9(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}}let et=t7;function ee(t){let e=t.split("-"),r=e.slice(1).map(t=>t[0].toUpperCase()+t.slice(1)).join("");return e[0]+r}class er extends t4{static keys(t){return(t.getAttribute("style")||"").split(";").map(t=>t.split(":")[0].trim())}add(t,e){return!!this.canAdd(t,e)&&(t.style[ee(this.keyName)]=e,!0)}remove(t){t.style[ee(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){let e=t.style[ee(this.keyName)];return this.canAdd(t,e)?e:""}}let ei=er;class en{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};let t=t8.find(this.domNode);if(null==t)return;let e=t4.keys(this.domNode),r=et.keys(this.domNode),i=ei.keys(this.domNode);e.concat(r).concat(i).forEach(e=>{let r=t.scroll.query(e,t5.ATTRIBUTE);r instanceof t4&&(this.attributes[r.attrName]=r)})}copy(t){Object.keys(this.attributes).forEach(e=>{let r=this.attributes[e].value(this.domNode);t.format(e,r)})}move(t){this.copy(t),Object.keys(this.attributes).forEach(t=>{this.attributes[t].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t),{})}}let es=en,el=class{constructor(t,e){this.scroll=t,this.domNode=e,t8.blots.set(e,this),this.prev=null,this.next=null}static create(t){let e,r;if(null==this.tagName)throw new t3("Blot definition missing tagName");return Array.isArray(this.tagName)?("string"==typeof t?parseInt(r=t.toUpperCase(),10).toString()===r&&(r=parseInt(r,10)):"number"==typeof t&&(r=t),e="number"==typeof r?document.createElement(this.tagName[r-1]):r&&this.tagName.indexOf(r)>-1?document.createElement(r):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){let t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){null!=this.parent&&this.parent.removeChild(this),t8.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,r,i){let n=this.isolate(t,e);if(null!=this.scroll.query(r,t5.BLOT)&&i)n.wrap(r,i);else if(null!=this.scroll.query(r,t5.ATTRIBUTE)){let t=this.scroll.create(this.statics.scope);n.wrap(t),t.format(r,i)}}insertAt(t,e,r){let i=null==r?this.scroll.create("text",e):this.scroll.create(e,r),n=this.split(t);this.parent.insertBefore(i,n||void 0)}isolate(t,e){let r=this.split(t);if(null==r)throw Error("Attempt to isolate at end");return r.split(e),r}length(){return 1}offset(t=this.parent){return null==this.parent||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){!this.statics.requiredContainer||this.parent instanceof this.statics.requiredContainer||this.wrap(this.statics.requiredContainer.blotName)}remove(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){let r="string"==typeof t?this.scroll.create(t,e):t;return null!=this.parent&&(this.parent.insertBefore(r,this.next||void 0),this.remove()),r}split(t,e){return 0===t?this:this.next}update(t,e){}wrap(t,e){let r="string"==typeof t?this.scroll.create(t,e):t;if(null!=this.parent&&this.parent.insertBefore(r,this.next||void 0),"function"!=typeof r.appendChild)throw new t3(`Cannot wrap ${t}`);return r.appendChild(this),r}};el.blotName="abstract";let eo=el,ea=class extends eo{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let r=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(r+=1),[this.parent.domNode,r]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};ea.scope=t5.INLINE_BLOT;let ec=ea;class eh{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){let e=t.slice(1);this.append(...e)}}at(t){let e=this.iterator(),r=e();for(;r&&t>0;)t-=1,r=e();return r}contains(t){let e=this.iterator(),r=e();for(;r;){if(r===t)return!0;r=e()}return!1}indexOf(t){let e=this.iterator(),r=e(),i=0;for(;r;){if(r===t)return i;i+=1,r=e()}return -1}insertBefore(t,e){null!=t&&(this.remove(t),t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,r=this.head;for(;null!=r;){if(r===t)return e;e+=r.length(),r=r.next}return -1}remove(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{let e=t;return null!=t&&(t=t.next),e}}find(t,e=!1){let r=this.iterator(),i=r();for(;i;){let n=i.length();if(t<n||e&&t===n&&(null==i.next||0!==i.next.length()))return[i,t];t-=n,i=r()}return[null,0]}forEach(t){let e=this.iterator(),r=e();for(;r;)t(r),r=e()}forEachAt(t,e,r){if(e<=0)return;let[i,n]=this.find(t),s=t-n,l=this.iterator(i),o=l();for(;o&&s<t+e;){let i=o.length();t>s?r(o,t-s,Math.min(e,s+i-t)):r(o,0,Math.min(i,t+e-s)),s+=i,o=l()}}map(t){return this.reduce((e,r)=>(e.push(t(r)),e),[])}reduce(t,e){let r=this.iterator(),i=r();for(;i;)e=t(e,i),i=r();return e}}function eu(t,e){let r=e.find(t);if(r)return r;try{return e.create(t)}catch{let r=e.create(t5.INLINE);return Array.from(t.childNodes).forEach(t=>{r.domNode.appendChild(t)}),t.parentNode&&t.parentNode.replaceChild(r.domNode,t),r.attach(),r}}let ed=class t extends eo{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(e){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=e,t.uiClass&&this.uiNode.classList.add(t.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new eh,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{let e=eu(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(t){if(t instanceof t3)return;throw t}})}deleteAt(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,(t,e,r)=>{t.deleteAt(e,r)})}descendant(e,r=0){let[i,n]=this.children.find(r);return null==e.blotName&&e(i)||null!=e.blotName&&i instanceof e?[i,n]:i instanceof t?i.descendant(e,n):[null,-1]}descendants(e,r=0,i=Number.MAX_VALUE){let n=[],s=i;return this.children.forEachAt(r,i,(r,i,l)=>{(null==e.blotName&&e(r)||null!=e.blotName&&r instanceof e)&&n.push(r),r instanceof t&&(n=n.concat(r.descendants(e,i,s))),s-=l}),n}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let e=!1;this.children.forEach(r=>{e||this.statics.allowedChildren.some(t=>r instanceof t)||(r.statics.scope===t5.BLOCK_BLOT?(null!=r.next&&this.splitAfter(r),null!=r.prev&&this.splitAfter(r.prev),r.parent.unwrap(),e=!0):r instanceof t?r.unwrap():r.remove())})}formatAt(t,e,r,i){this.children.forEachAt(t,e,(t,e,n)=>{t.formatAt(e,n,r,i)})}insertAt(t,e,r){let[i,n]=this.children.find(t);if(i)i.insertAt(n,e,r);else{let t=null==r?this.scroll.create("text",e):this.scroll.create(e,r);this.appendChild(t)}}insertBefore(t,e){null!=t.parent&&t.parent.children.remove(t);let r=null;this.children.insertBefore(t,e||null),t.parent=this,null!=e&&(r=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==r)&&this.domNode.insertBefore(t.domNode,r),t.attach()}length(){return this.children.reduce((t,e)=>t+e.length(),0)}moveChildren(t,e){this.children.forEach(r=>{t.insertBefore(r,e)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length)if(null!=this.statics.defaultChild){let t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t)}else this.remove()}path(e,r=!1){let[i,n]=this.children.find(e,r),s=[[this,e]];return i instanceof t?s.concat(i.path(n,r)):(null!=i&&s.push([i,n]),s)}removeChild(t){this.children.remove(t)}replaceWith(e,r){let i="string"==typeof e?this.scroll.create(e,r):e;return i instanceof t&&this.moveChildren(i),super.replaceWith(i)}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}let r=this.clone();return this.parent&&this.parent.insertBefore(r,this.next||void 0),this.children.forEachAt(t,this.length(),(t,i,n)=>{let s=t.split(i,e);null!=s&&r.appendChild(s)}),r}splitAfter(t){let e=this.clone();for(;null!=t.next;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){let r=[],i=[];t.forEach(t=>{t.target===this.domNode&&"childList"===t.type&&(r.push(...t.addedNodes),i.push(...t.removedNodes))}),i.forEach(t=>{if(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;let e=this.scroll.find(t);null!=e&&(null==e.domNode.parentNode||e.domNode.parentNode===this.domNode)&&e.detach()}),r.filter(t=>t.parentNode===this.domNode&&t!==this.uiNode).sort((t,e)=>t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(t=>{let e=null;null!=t.nextSibling&&(e=this.scroll.find(t.nextSibling));let r=eu(t,this.scroll);(r.next!==e||null==r.next)&&(null!=r.parent&&r.parent.removeChild(this),this.insertBefore(r,e||void 0))}),this.enforceAllowedChildren()}};ed.uiClass="";let ef=ed,ep=class t extends ef{static create(t){return super.create(t)}static formats(e,r){let i=r.query(t.blotName);if(null==i||e.tagName!==i.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new es(this.domNode)}format(e,r){if(e!==this.statics.blotName||r){let t=this.scroll.query(e,t5.INLINE);null!=t&&(t instanceof t4?this.attributes.attribute(t,r):r&&(e!==this.statics.blotName||this.formats()[e]!==r)&&this.replaceWith(e,r))}else this.children.forEach(e=>{e instanceof t||(e=e.wrap(t.blotName,!0)),this.attributes.copy(e)}),this.unwrap()}formats(){let t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,r,i){null!=this.formats()[r]||this.scroll.query(r,t5.ATTRIBUTE)?this.isolate(t,e).format(r,i):super.formatAt(t,e,r,i)}optimize(e){super.optimize(e);let r=this.formats();if(0===Object.keys(r).length)return this.unwrap();let i=this.next;i instanceof t&&i.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(let r in t)if(t[r]!==e[r])return!1;return!0}(r,i.formats())&&(i.moveChildren(this),i.remove())}replaceWith(t,e){let r=super.replaceWith(t,e);return this.attributes.copy(r),r}update(t,e){super.update(t,e),t.some(t=>t.target===this.domNode&&"attributes"===t.type)&&this.attributes.build()}wrap(e,r){let i=super.wrap(e,r);return i instanceof t&&this.attributes.move(i),i}};ep.allowedChildren=[ep,ec],ep.blotName="inline",ep.scope=t5.INLINE_BLOT,ep.tagName="SPAN";let eg=ep,em=class t extends ef{static create(t){return super.create(t)}static formats(e,r){let i=r.query(t.blotName);if(null==i||e.tagName!==i.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new es(this.domNode)}format(e,r){let i=this.scroll.query(e,t5.BLOCK);null!=i&&(i instanceof t4?this.attributes.attribute(i,r):e!==this.statics.blotName||r?r&&(e!==this.statics.blotName||this.formats()[e]!==r)&&this.replaceWith(e,r):this.replaceWith(t.blotName))}formats(){let t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,r,i){null!=this.scroll.query(r,t5.BLOCK)?this.format(r,i):super.formatAt(t,e,r,i)}insertAt(t,e,r){if(null==r||null!=this.scroll.query(e,t5.INLINE))super.insertAt(t,e,r);else{let i=this.split(t);if(null!=i){let t=this.scroll.create(e,r);i.parent.insertBefore(t,i)}else throw Error("Attempt to insertAt after block boundaries")}}replaceWith(t,e){let r=super.replaceWith(t,e);return this.attributes.copy(r),r}update(t,e){super.update(t,e),t.some(t=>t.target===this.domNode&&"attributes"===t.type)&&this.attributes.build()}};em.blotName="block",em.scope=t5.BLOCK_BLOT,em.tagName="P",em.allowedChildren=[eg,em,ec];let eb=em,ey=class extends ef{checkMerge(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,r,i){super.formatAt(t,e,r,i),this.enforceAllowedChildren()}insertAt(t,e,r){super.insertAt(t,e,r),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};ey.blotName="container",ey.scope=t5.BLOCK_BLOT;let ev=ey;class ex extends ec{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,r,i){0===t&&e===this.length()?this.format(r,i):super.formatAt(t,e,r,i)}formats(){return this.statics.formats(this.domNode,this.scroll)}}let eN=ex,eE={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},eA=class extends ef{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver(t=>{this.update(t)}),this.observer.observe(this.domNode,eE),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){let r=this.registry.find(t,e);return r?r.scroll===this?r:e?this.find(r.scroll.domNode.parentNode,!0):null:null}query(t,e=t5.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){null!=this.scroll&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),0===t&&e===this.length()?this.children.forEach(t=>{t.remove()}):super.deleteAt(t,e)}formatAt(t,e,r,i){this.update(),super.formatAt(t,e,r,i)}insertAt(t,e,r){this.update(),super.insertAt(t,e,r)}optimize(t=[],e={}){super.optimize(e);let r=e.mutationsMap||new WeakMap,i=Array.from(this.observer.takeRecords());for(;i.length>0;)t.push(i.pop());let n=(t,e=!0)=>{null==t||t===this||null!=t.domNode.parentNode&&(r.has(t.domNode)||r.set(t.domNode,[]),e&&n(t.parent))},s=t=>{r.has(t.domNode)&&(t instanceof ef&&t.children.forEach(s),r.delete(t.domNode),t.optimize(e))},l=t;for(let e=0;l.length>0;e+=1){if(e>=100)throw Error("[Parchment] Maximum optimize iterations reached");for(l.forEach(t=>{let e=this.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(n(this.find(t.previousSibling,!1)),Array.from(t.addedNodes).forEach(t=>{let e=this.find(t,!1);n(e,!1),e instanceof ef&&e.children.forEach(t=>{n(t,!1)})})):"attributes"===t.type&&n(e.prev)),n(e))}),this.children.forEach(s),i=(l=Array.from(this.observer.takeRecords())).slice();i.length>0;)t.push(i.pop())}}update(t,e={}){t=t||this.observer.takeRecords();let r=new WeakMap;t.map(t=>{let e=this.find(t.target,!0);return null==e?null:r.has(e.domNode)?(r.get(e.domNode).push(t),null):(r.set(e.domNode,[t]),e)}).forEach(t=>{null!=t&&t!==this&&r.has(t.domNode)&&t.update(r.get(t.domNode)||[],e)}),e.mutationsMap=r,r.has(this.domNode)&&super.update(r.get(this.domNode),e),this.optimize(t,e)}};eA.blotName="scroll",eA.defaultChild=eb,eA.allowedChildren=[eb,ev],eA.scope=t5.BLOCK_BLOT,eA.tagName="DIV";let ew=eA,eq=class t extends ec{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,r){null==r?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,r)}length(){return this.text.length}optimize(e){super.optimize(e),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof t&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}let r=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(r,this.next||void 0),this.text=this.statics.value(this.domNode),r}update(t,e){t.some(t=>"characterData"===t.type&&t.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};eq.blotName="text",eq.scope=t5.INLINE_BLOT;let ek=eq;var e_=r(49882);let eL=function(t,e){for(var r=-1,i=null==t?0:t.length;++r<i&&!1!==e(t[r],r,t););return t};var eO=ti(Object.keys,Object),eS=Object.prototype.hasOwnProperty;let eT=function(t){if(!tl(t))return eO(t);var e=[];for(var r in Object(t))eS.call(t,r)&&"constructor"!=r&&e.push(r);return e},ej=function(t){return tm(t)?tF(t):eT(t)},eC=function(t,e){for(var r=-1,i=null==t?0:t.length,n=0,s=[];++r<i;){var l=t[r];e(l,r,t)&&(s[n++]=l)}return s},eR=function(){return[]};var eI=Object.prototype.propertyIsEnumerable,eM=Object.getOwnPropertySymbols,eB=eM?function(t){return null==t?[]:eC(eM(t=Object(t)),function(e){return eI.call(t,e)})}:eR;let eD=function(t,e){for(var r=-1,i=e.length,n=t.length;++r<i;)t[n+r]=e[r];return t};var eU=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)eD(e,eB(t)),t=tn(t);return e}:eR;let eP=function(t,e,r){var i=e(t);return tp(t)?i:eD(i,r(t))},ez=function(t){return eP(t,ej,eB)},eF=function(t){return eP(t,tK,eU)};var e$=j(h,"DataView"),eH=j(h,"Promise"),eV=j(h,"Set"),eK=j(h,"WeakMap"),eW="[object Map]",eZ="[object Promise]",eG="[object Set]",eX="[object WeakMap]",eY="[object DataView]",eQ=q(e$),eJ=q(C),e1=q(eH),e0=q(eV),e2=q(eK),e5=v;(e$&&e5(new e$(new ArrayBuffer(1)))!=eY||C&&e5(new C)!=eW||eH&&e5(eH.resolve())!=eZ||eV&&e5(new eV)!=eG||eK&&e5(new eK)!=eX)&&(e5=function(t){var e=v(t),r="[object Object]"==e?t.constructor:void 0,i=r?q(r):"";if(i)switch(i){case eQ:return eY;case eJ:return eW;case e1:return eZ;case e0:return eG;case e2:return eX}return e});let e4=e5;var e3=Object.prototype.hasOwnProperty;let e6=function(t){var e=t.length,r=new t.constructor(e);return e&&"string"==typeof t[0]&&e3.call(t,"index")&&(r.index=t.index,r.input=t.input),r},e8=function(t,e){var r=e?Q(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)};var e9=/\w*$/;let e7=function(t){var e=new t.constructor(t.source,e9.exec(t));return e.lastIndex=t.lastIndex,e};var rt=u?u.prototype:void 0,re=rt?rt.valueOf:void 0;let rr=function(t,e,r){var i=t.constructor;switch(e){case"[object ArrayBuffer]":return Q(t);case"[object Boolean]":case"[object Date]":return new i(+t);case"[object DataView]":return e8(t,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return J(t,r);case"[object Map]":case"[object Set]":return new i;case"[object Number]":case"[object String]":return new i(t);case"[object RegExp]":return e7(t);case"[object Symbol]":return re?Object(re.call(t)):{}}};var ri=tT&&tT.isMap,rn=ri?t_(ri):function(t){return ta(t)&&"[object Map]"==e4(t)},rs=tT&&tT.isSet,rl=rs?t_(rs):function(t){return ta(t)&&"[object Set]"==e4(t)},ro="[object Arguments]",ra="[object Function]",rc="[object Object]",rh={};rh[ro]=rh["[object Array]"]=rh["[object ArrayBuffer]"]=rh["[object DataView]"]=rh["[object Boolean]"]=rh["[object Date]"]=rh["[object Float32Array]"]=rh["[object Float64Array]"]=rh["[object Int8Array]"]=rh["[object Int16Array]"]=rh["[object Int32Array]"]=rh["[object Map]"]=rh["[object Number]"]=rh[rc]=rh["[object RegExp]"]=rh["[object Set]"]=rh["[object String]"]=rh["[object Symbol]"]=rh["[object Uint8Array]"]=rh["[object Uint8ClampedArray]"]=rh["[object Uint16Array]"]=rh["[object Uint32Array]"]=!0,rh["[object Error]"]=rh[ra]=rh["[object WeakMap]"]=!1;let ru=function t(e,r,i,n,s,l){var o,a=1&r,c=2&r,h=4&r;if(i&&(o=s?i(e,n,s,l):i(e)),void 0!==o)return o;if(!x(e))return e;var u=tp(e);if(u){if(o=e6(e),!a)return tt(e,o)}else{var d,f,p,g,m=e4(e),b=m==ra||"[object GeneratorFunction]"==m;if(tx(e))return X(e,a);if(m==rc||m==ro||b&&!s){if(o=c||b?{}:to(e),!a)return c?(f=(d=o)&&tB(e,tK(e),d),tB(e,eU(e),f)):(g=(p=o)&&tB(e,ej(e),p),tB(e,eB(e),g))}else{if(!rh[m])return s?e:{};o=rr(e,m,a)}}l||(l=new z);var y=l.get(e);if(y)return y;l.set(e,o),rl(e)?e.forEach(function(n){o.add(t(n,r,i,n,e,l))}):rn(e)&&e.forEach(function(n,s){o.set(s,t(n,r,i,s,e,l))});var v=h?c?eF:ez:c?tK:ej,N=u?void 0:v(e);return eL(N||e,function(n,s){N&&(n=e[s=n]),tM(o,s,t(n,r,i,s,e,l))}),o},rd=function(t){return ru(t,5)};function rf(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new P;++e<r;)this.add(t[e])}rf.prototype.add=rf.prototype.push=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},rf.prototype.has=function(t){return this.__data__.has(t)};let rp=function(t,e){for(var r=-1,i=null==t?0:t.length;++r<i;)if(e(t[r],r,t))return!0;return!1},rg=function(t,e,r,i,n,s){var l=1&r,o=t.length,a=e.length;if(o!=a&&!(l&&a>o))return!1;var c=s.get(t),h=s.get(e);if(c&&h)return c==e&&h==t;var u=-1,d=!0,f=2&r?new rf:void 0;for(s.set(t,e),s.set(e,t);++u<o;){var p=t[u],g=e[u];if(i)var m=l?i(g,p,u,e,t,s):i(p,g,u,t,e,s);if(void 0!==m){if(m)continue;d=!1;break}if(f){if(!rp(e,function(t,e){if(!f.has(e)&&(p===t||n(p,t,r,i,s)))return f.push(e)})){d=!1;break}}else if(!(p===g||n(p,g,r,i,s))){d=!1;break}}return s.delete(t),s.delete(e),d},rm=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,i){r[++e]=[i,t]}),r},rb=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r};var ry=u?u.prototype:void 0,rv=ry?ry.valueOf:void 0;let rx=function(t,e,r,i,s,l,o){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!l(new Y(t),new Y(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return n(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var a=rm;case"[object Set]":var c=1&i;if(a||(a=rb),t.size!=e.size&&!c)break;var h=o.get(t);if(h)return h==e;i|=2,o.set(t,e);var u=rg(a(t),a(e),i,s,l,o);return o.delete(t),u;case"[object Symbol]":if(rv)return rv.call(t)==rv.call(e)}return!1};var rN=Object.prototype.hasOwnProperty;let rE=function(t,e,r,i,n,s){var l=1&r,o=ez(t),a=o.length;if(a!=ez(e).length&&!l)return!1;for(var c=a;c--;){var h=o[c];if(!(l?h in e:rN.call(e,h)))return!1}var u=s.get(t),d=s.get(e);if(u&&d)return u==e&&d==t;var f=!0;s.set(t,e),s.set(e,t);for(var p=l;++c<a;){var g=t[h=o[c]],m=e[h];if(i)var b=l?i(m,g,h,e,t,s):i(g,m,h,t,e,s);if(!(void 0===b?g===m||n(g,m,r,i,s):b)){f=!1;break}p||(p="constructor"==h)}if(f&&!p){var y=t.constructor,v=e.constructor;y!=v&&"constructor"in t&&"constructor"in e&&!("function"==typeof y&&y instanceof y&&"function"==typeof v&&v instanceof v)&&(f=!1)}return s.delete(t),s.delete(e),f};var rA="[object Arguments]",rw="[object Array]",rq="[object Object]",rk=Object.prototype.hasOwnProperty;let r_=function(t,e,r,i,n,s){var l=tp(t),o=tp(e),a=l?rw:e4(t),c=o?rw:e4(e);a=a==rA?rq:a,c=c==rA?rq:c;var h=a==rq,u=c==rq,d=a==c;if(d&&tx(t)){if(!tx(e))return!1;l=!0,h=!1}if(d&&!h)return s||(s=new z),l||tC(t)?rg(t,e,r,i,n,s):rx(t,e,a,r,i,n,s);if(!(1&r)){var f=h&&rk.call(t,"__wrapped__"),p=u&&rk.call(e,"__wrapped__");if(f||p){var g=f?t.value():t,m=p?e.value():e;return s||(s=new z),n(g,m,r,i,s)}}return!!d&&(s||(s=new z),rE(t,e,r,i,n,s))},rL=function(t,e){return function t(e,r,i,n,s){return e===r||(null!=e&&null!=r&&(ta(e)||ta(r))?r_(e,r,i,n,t,s):e!=e&&r!=r)}(t,e)};class rO extends eN{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}rO.blotName="break",rO.tagName="BR";class rS extends ek{}let rT={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function rj(t){return t.replace(/[&<>"']/g,t=>rT[t])}class rC extends eg{static allowedChildren=[rC,rO,eN,rS];static order=["cursor","inline","link","underline","strike","italic","bold","script","code"];static compare(t,e){let r=rC.order.indexOf(t),i=rC.order.indexOf(e);return r>=0||i>=0?r-i:t===e?0:t<e?-1:1}formatAt(t,e,r,i){if(0>rC.compare(this.statics.blotName,r)&&this.scroll.query(r,t5.BLOT)){let n=this.isolate(t,e);i&&n.wrap(r,i)}else super.formatAt(t,e,r,i)}optimize(t){if(super.optimize(t),this.parent instanceof rC&&rC.compare(this.statics.blotName,this.parent.statics.blotName)>0){let t=this.parent.isolate(this.offset(),this.length());this.moveChildren(t),t.wrap(this)}}}let rR=rC;class rI extends eb{cache={};delta(){return null==this.cache.delta&&(this.cache.delta=rB(this)),this.cache.delta}deleteAt(t,e){super.deleteAt(t,e),this.cache={}}formatAt(t,e,r,i){e<=0||(this.scroll.query(r,t5.BLOCK)?t+e===this.length()&&this.format(r,i):super.formatAt(t,Math.min(e,this.length()-t-1),r,i),this.cache={})}insertAt(t,e,r){if(null!=r){super.insertAt(t,e,r),this.cache={};return}if(0===e.length)return;let i=e.split("\n"),n=i.shift();n.length>0&&(t<this.length()-1||null==this.children.tail?super.insertAt(Math.min(t,this.length()-1),n):this.children.tail.insertAt(this.children.tail.length(),n),this.cache={});let s=this;i.reduce((t,e)=>((s=s.split(t,!0)).insertAt(0,e),e.length),t+n.length)}insertBefore(t,e){let{head:r}=this.children;super.insertBefore(t,e),r instanceof rO&&r.remove(),this.cache={}}length(){return null==this.cache.length&&(this.cache.length=super.length()+1),this.cache.length}moveChildren(t,e){super.moveChildren(t,e),this.cache={}}optimize(t){super.optimize(t),this.cache={}}path(t){return super.path(t,!0)}removeChild(t){super.removeChild(t),this.cache={}}split(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e&&(0===t||t>=this.length()-1)){let e=this.clone();return 0===t?(this.parent.insertBefore(e,this),this):(this.parent.insertBefore(e,this.next),e)}let r=super.split(t,e);return this.cache={},r}}rI.blotName="block",rI.tagName="P",rI.defaultChild=rO,rI.allowedChildren=[rO,rR,eN,rS];class rM extends eN{attach(){super.attach(),this.attributes=new es(this.domNode)}delta(){return new e_().insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){let r=this.scroll.query(t,t5.BLOCK_ATTRIBUTE);null!=r&&this.attributes.attribute(r,e)}formatAt(t,e,r,i){this.format(r,i)}insertAt(t,e,r){if(null!=r)return void super.insertAt(t,e,r);let i=e.split("\n"),n=i.pop(),s=i.map(t=>{let e=this.scroll.create(rI.blotName);return e.insertAt(0,t),e}),l=this.split(t);s.forEach(t=>{this.parent.insertBefore(t,l)}),n&&this.parent.insertBefore(this.scroll.create("text",n),l)}}function rB(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return t.descendants(ec).reduce((t,r)=>0===r.length()?t:t.insert(r.value(),rD(r,{},e)),new e_).insert("\n",rD(t))}function rD(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return null==t||("formats"in t&&"function"==typeof t.formats&&(e={...e,...t.formats()},r&&delete e["code-token"]),null==t.parent||"scroll"===t.parent.statics.blotName||t.parent.statics.scope!==t.statics.scope)?e:rD(t.parent,e,r)}rM.scope=t5.BLOCK_BLOT;class rU extends eN{static blotName="cursor";static className="ql-cursor";static tagName="span";static CONTENTS="\uFEFF";static value(){}constructor(t,e,r){super(t,e),this.selection=r,this.textNode=document.createTextNode(rU.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){null!=this.parent&&this.parent.removeChild(this)}format(t,e){if(0!==this.savedLength)return void super.format(t,e);let r=this,i=0;for(;null!=r&&r.statics.scope!==t5.BLOCK_BLOT;)i+=r.offset(r.parent),r=r.parent;null!=r&&(this.savedLength=rU.CONTENTS.length,r.optimize(),r.formatAt(i,rU.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){let t;if(this.selection.composing||null==this.parent)return null;let e=this.selection.getNativeRange();for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);let r=this.prev instanceof rS?this.prev:null,i=r?r.length():0,n=this.next instanceof rS?this.next:null,s=n?n.text:"",{textNode:l}=this,o=l.data.split(rU.CONTENTS).join("");if(l.data=rU.CONTENTS,r)t=r,(o||n)&&(r.insertAt(r.length(),o+s),n&&n.remove());else if(n)t=n,n.insertAt(0,o);else{let e=document.createTextNode(o);t=this.scroll.create(e),this.parent.insertBefore(t,this)}if(this.remove(),e){let s=(t,e)=>r&&t===r.domNode?e:t===l?i+e-1:n&&t===n.domNode?i+o.length+e:null,a=s(e.start.node,e.start.offset),c=s(e.end.node,e.end.offset);if(null!==a&&null!==c)return{startNode:t.domNode,startOffset:a,endNode:t.domNode,endOffset:c}}return null}update(t,e){if(t.some(t=>"characterData"===t.type&&t.target===this.textNode)){let t=this.restore();t&&(e.range=t)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if("A"===e.domNode.tagName){this.savedLength=rU.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}}var rP=r(82661);let rz=new WeakMap,rF=["error","warn","log","info"],r$="warn";function rH(t){if(r$&&rF.indexOf(t)<=rF.indexOf(r$)){for(var e=arguments.length,r=Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];console[t](...r)}}function rV(t){return rF.reduce((e,r)=>(e[r]=rH.bind(console,r,t),e),{})}rV.level=t=>{r$=t},rH.level=rV.level;let rK=rV("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(t=>{document.addEventListener(t,function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];Array.from(document.querySelectorAll(".ql-container")).forEach(t=>{let r=rz.get(t);r&&r.emitter&&r.emitter.handleDOM(...e)})})});class rW extends rP{static events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"};static sources={API:"api",SILENT:"silent",USER:"user"};constructor(){super(),this.domListeners={},this.on("error",rK.error)}emit(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return rK.log.call(rK,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,r=Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];(this.domListeners[t.type]||[]).forEach(e=>{let{node:i,handler:n}=e;(t.target===i||i.contains(t.target))&&n(t,...r)})}listenDOM(t,e,r){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:r})}}let rZ=rV("quill:selection");class rG{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.index=t,this.length=e}}class rX{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new rG(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{this.mouseDown||this.composing||setTimeout(this.update.bind(this,rW.sources.USER),1)}),this.emitter.on(rW.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;let t=this.getNativeRange();null!=t&&t.start.node!==this.cursor.textNode&&this.emitter.once(rW.events.SCROLL_UPDATE,(e,r)=>{try{this.root.contains(t.start.node)&&this.root.contains(t.end.node)&&this.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset);let i=r.some(t=>"characterData"===t.type||"childList"===t.type||"attributes"===t.type&&t.target===this.root);this.update(i?rW.sources.SILENT:e)}catch(t){}})}),this.emitter.on(rW.events.SCROLL_OPTIMIZE,(t,e)=>{if(e.range){let{startNode:t,startOffset:r,endNode:i,endOffset:n}=e.range;this.setNativeRange(t,r,i,n),this.update(rW.sources.SILENT)}}),this.update(rW.sources.SILENT)}handleComposition(){this.emitter.on(rW.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(rW.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){let t=this.cursor.restore();t&&setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(rW.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();let r=this.getNativeRange();if(!(null==r||!r.native.collapsed||this.scroll.query(t,t5.BLOCK))){if(r.start.node!==this.cursor.textNode){let t=this.scroll.find(r.start.node,!1);if(null==t)return;if(t instanceof ec){let e=t.split(r.start.offset);t.parent.insertBefore(this.cursor,e)}else t.insertBefore(this.cursor,r.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e,r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this.scroll.length();i=Math.min((t=Math.min(t,n-1))+i,n-1)-t;let[s,l]=this.scroll.leaf(t);if(null==s)return null;if(i>0&&l===s.length()){let[e]=this.scroll.leaf(t+1);if(e){let[r]=this.scroll.line(t),[i]=this.scroll.line(t+1);r===i&&(s=e,l=0)}}[e,l]=s.position(l,!0);let o=document.createRange();if(i>0)return(o.setStart(e,l),[s,l]=this.scroll.leaf(t+i),null==s)?null:([e,l]=s.position(l,!0),o.setEnd(e,l),o.getBoundingClientRect());let a="left";if(e instanceof Text){if(!e.data.length)return null;l<e.data.length?(o.setStart(e,l),o.setEnd(e,l+1)):(o.setStart(e,l-1),o.setEnd(e,l),a="right"),r=o.getBoundingClientRect()}else{if(!(s.domNode instanceof Element))return null;r=s.domNode.getBoundingClientRect(),l>0&&(a="right")}return{bottom:r.top+r.height,height:r.height,left:r[a],right:r[a],top:r.top,width:0}}getNativeRange(){let t=document.getSelection();if(null==t||t.rangeCount<=0)return null;let e=t.getRangeAt(0);if(null==e)return null;let r=this.normalizeNative(e);return rZ.info("getNativeRange",r),r}getRange(){let t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];let e=this.getNativeRange();return null==e?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||null!=document.activeElement&&rY(this.root,document.activeElement)}normalizedToRange(t){let e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);let r=e.map(t=>{let[e,r]=t,i=this.scroll.find(e,!0),n=i.offset(this.scroll);return 0===r?n:i instanceof ec?n+i.index(e,r):n+i.length()}),i=Math.min(Math.max(...r),this.scroll.length()-1),n=Math.min(i,...r);return new rG(n,i-n)}normalizeNative(t){if(!rY(this.root,t.startContainer)||!t.collapsed&&!rY(this.root,t.endContainer))return null;let e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(t=>{let{node:e,offset:r}=t;for(;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>r)e=e.childNodes[r],r=0;else if(e.childNodes.length===r)r=(e=e.lastChild)instanceof Text?e.data.length:e.childNodes.length>0?e.childNodes.length:e.childNodes.length+1;else break;t.node=e,t.offset=r}),e}rangeToNative(t){let e=this.scroll.length(),r=(t,r)=>{t=Math.min(e-1,t);let[i,n]=this.scroll.leaf(t);return i?i.position(n,r):[null,-1]};return[...r(t.index,!1),...r(t.index+t.length,!0)]}setNativeRange(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(rZ.info("setNativeRange",t,e,r,i),null!=t&&(null==this.root.parentNode||null==t.parentNode||null==r.parentNode))return;let s=document.getSelection();if(null!=s)if(null!=t){this.hasFocus()||this.root.focus({preventScroll:!0});let{native:l}=this.getNativeRange()||{};if(null==l||n||t!==l.startContainer||e!==l.startOffset||r!==l.endContainer||i!==l.endOffset){t instanceof Element&&"BR"===t.tagName&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),r instanceof Element&&"BR"===r.tagName&&(i=Array.from(r.parentNode.childNodes).indexOf(r),r=r.parentNode);let n=document.createRange();n.setStart(t,e),n.setEnd(r,i),s.removeAllRanges(),s.addRange(n)}}else s.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:rW.sources.API;if("string"==typeof e&&(r=e,e=!1),rZ.info("setRange",t),null!=t){let r=this.rangeToNative(t);this.setNativeRange(...r,e)}else this.setNativeRange(null);this.update(r)}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:rW.sources.USER,e=this.lastRange,[r,i]=this.getRange();if(this.lastRange=r,this.lastNative=i,null!=this.lastRange&&(this.savedRange=this.lastRange),!rL(e,this.lastRange)){if(!this.composing&&null!=i&&i.native.collapsed&&i.start.node!==this.cursor.textNode){let t=this.cursor.restore();t&&this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)}let r=[rW.events.SELECTION_CHANGE,rd(this.lastRange),rd(e),t];this.emitter.emit(rW.events.EDITOR_CHANGE,...r),t!==rW.sources.SILENT&&this.emitter.emit(...r)}}}function rY(t,e){try{e.parentNode}catch(t){return!1}return t.contains(e)}let rQ=/^[ -~]*$/;class rJ{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();let r=r2(t),i=new e_;return(function(t){let e=[];return t.forEach(t=>{"string"==typeof t.insert?t.insert.split("\n").forEach((r,i)=>{i&&e.push({insert:"\n",attributes:t.attributes}),r&&e.push({insert:r,attributes:t.attributes})}):e.push(t)}),e})(r.ops.slice()).reduce((t,r)=>{let n=e_.Op.length(r),s=r.attributes||{},l=!1,o=!1;if(null!=r.insert){if(i.retain(n),"string"==typeof r.insert){let i=r.insert;o=!i.endsWith("\n")&&(e<=t||!!this.scroll.descendant(rM,t)[0]),this.scroll.insertAt(t,i);let[n,l]=this.scroll.line(t),a=t2({},rD(n));if(n instanceof rI){let[t]=n.descendant(ec,l);t&&(a=t2(a,rD(t)))}s=e_.AttributeMap.diff(a,s)||{}}else if("object"==typeof r.insert){let i=Object.keys(r.insert)[0];if(null==i)return t;let n=null!=this.scroll.query(i,t5.INLINE);if(n)(e<=t||this.scroll.descendant(rM,t)[0])&&(o=!0);else if(t>0){let[e,r]=this.scroll.descendant(ec,t-1);e instanceof rS?"\n"!==e.value()[r]&&(l=!0):e instanceof eN&&e.statics.scope===t5.INLINE_BLOT&&(l=!0)}if(this.scroll.insertAt(t,i,r.insert[i]),n){let[e]=this.scroll.descendant(ec,t);if(e){let t=t2({},rD(e));s=e_.AttributeMap.diff(t,s)||{}}}}e+=n}else if(i.push(r),null!==r.retain&&"object"==typeof r.retain){let e=Object.keys(r.retain)[0];if(null==e)return t;this.scroll.updateEmbedAt(t,e,r.retain[e])}Object.keys(s).forEach(e=>{this.scroll.formatAt(t,n,e,s[e])});let a=+!!l,c=+!!o;return e+=a+c,i.retain(a),i.delete(c),t+n+a+c},0),i.reduce((t,e)=>"number"==typeof e.delete?(this.scroll.deleteAt(t,e.delete),t):t+e_.Op.length(e),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(r)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new e_().retain(t).delete(e))}formatLine(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update(),Object.keys(r).forEach(i=>{this.scroll.lines(t,Math.max(e,1)).forEach(t=>{t.format(i,r[i])})}),this.scroll.optimize();let i=new e_().retain(t).retain(e,rd(r));return this.update(i)}formatText(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Object.keys(r).forEach(i=>{this.scroll.formatAt(t,e,i,r[i])});let i=new e_().retain(t).retain(e,rd(r));return this.update(i)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new e_)}getFormat(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[],i=[];0===e?this.scroll.path(t).forEach(t=>{let[e]=t;e instanceof rI?r.push(e):e instanceof ec&&i.push(e)}):(r=this.scroll.lines(t,e),i=this.scroll.descendants(ec,t,e));let[n,s]=[r,i].map(t=>{let e=t.shift();if(null==e)return{};let r=rD(e);for(;Object.keys(r).length>0;){let e=t.shift();if(null==e)break;r=function(t,e){return Object.keys(e).reduce((r,i)=>{if(null==t[i])return r;let n=e[i];return n===t[i]?r[i]=n:Array.isArray(n)?0>n.indexOf(t[i])?r[i]=n.concat([t[i]]):r[i]=n:r[i]=[n,t[i]],r},{})}(rD(e),r)}return r});return{...n,...s}}getHTML(t,e){let[r,i]=this.scroll.line(t);if(r){let n=r.length();return r.length()>=i+e&&(0!==i||e!==n)?r1(r,i,e,!0):r1(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(t=>"string"==typeof t.insert).map(t=>t.insert).join("")}insertContents(t,e){let r=r2(e),i=new e_().retain(t).concat(r);return this.scroll.insertContents(t,r),this.update(i)}insertEmbed(t,e,r){return this.scroll.insertAt(t,e,r),this.update(new e_().retain(t).insert({[e]:r}))}insertText(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(t,e),Object.keys(r).forEach(i=>{this.scroll.formatAt(t,e.length,i,r[i])}),this.update(new e_().retain(t).insert(e,rd(r)))}isBlank(){if(0===this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;let t=this.scroll.children.head;return t?.statics.blotName===rI.blotName&&!(t.children.length>1)&&t.children.head instanceof rO}removeFormat(t,e){let r=this.getText(t,e),[i,n]=this.scroll.line(t+e),s=0,l=new e_;null!=i&&(s=i.length()-n,l=i.delta().slice(n,n+s-1).insert("\n"));let o=this.getContents(t,e+s).diff(new e_().insert(r).concat(l)),a=new e_().retain(t).concat(o);return this.applyDelta(a)}update(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,i=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(rQ)&&this.scroll.find(e[0].target)){let n=this.scroll.find(e[0].target),s=rD(n),l=n.offset(this.scroll),o=e[0].oldValue.replace(rU.CONTENTS,""),a=new e_().insert(o),c=new e_().insert(n.value()),h=r&&{oldRange:r5(r.oldRange,-l),newRange:r5(r.newRange,-l)};t=new e_().retain(l).concat(a.diff(c,h)).reduce((t,e)=>e.insert?t.insert(e.insert,s):t.push(e),new e_),this.delta=i.compose(t)}else this.delta=this.getDelta(),t&&rL(i.compose(t),this.delta)||(t=i.diff(this.delta,r));return t}}function r1(t,e,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if("html"in t&&"function"==typeof t.html)return t.html(e,r);if(t instanceof rS)return rj(t.value().slice(e,e+r)).replaceAll(" ","&nbsp;");if(t instanceof ef){if("list-container"===t.statics.blotName){let i=[];return t.children.forEachAt(e,r,(t,e,r)=>{let n="formats"in t&&"function"==typeof t.formats?t.formats():{};i.push({child:t,offset:e,length:r,indent:n.indent||0,type:n.list})}),function t(e,r,i){if(0===e.length){let[e]=r0(i.pop());return r<=0?`</li></${e}>`:`</li></${e}>${t([],r-1,i)}`}let[{child:n,offset:s,length:l,indent:o,type:a},...c]=e,[h,u]=r0(a);if(o>r)return(i.push(a),o===r+1)?`<${h}><li${u}>${r1(n,s,l)}${t(c,o,i)}`:`<${h}><li>${t(e,r+1,i)}`;let d=i[i.length-1];if(o===r&&a===d)return`</li><li${u}>${r1(n,s,l)}${t(c,o,i)}`;let[f]=r0(i.pop());return`</li></${f}>${t(e,r-1,i)}`}(i,-1,[])}let n=[];if(t.children.forEachAt(e,r,(t,e,r)=>{n.push(r1(t,e,r))}),i||"list"===t.statics.blotName)return n.join("");let{outerHTML:s,innerHTML:l}=t.domNode,[o,a]=s.split(`>${l}<`);return"<table"===o?`<table style="border: 1px solid #000;">${n.join("")}<${a}`:`${o}>${n.join("")}<${a}`}return t.domNode instanceof Element?t.domNode.outerHTML:""}function r0(t){let e="ordered"===t?"ol":"ul";switch(t){case"checked":return[e,' data-list="checked"'];case"unchecked":return[e,' data-list="unchecked"'];default:return[e,""]}}function r2(t){return t.reduce((t,e)=>{if("string"==typeof e.insert){let r=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(r,e.attributes)}return t.push(e)},new e_)}function r5(t,e){let{index:r,length:i}=t;return new rG(r+e,i)}class r4{static DEFAULTS={};constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.quill=t,this.options=e}}let r3=r4;class r6 extends eN{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(t=>{this.contentNode.appendChild(t)}),this.leftGuard=document.createTextNode("\uFEFF"),this.rightGuard=document.createTextNode("\uFEFF"),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e,r=null,i=t.data.split("\uFEFF").join("");if(t===this.leftGuard)if(this.prev instanceof rS){let t=this.prev.length();this.prev.insertAt(t,i),r={startNode:this.prev.domNode,startOffset:t+i.length}}else e=document.createTextNode(i),this.parent.insertBefore(this.scroll.create(e),this),r={startNode:e,startOffset:i.length};else t===this.rightGuard&&(this.next instanceof rS?(this.next.insertAt(0,i),r={startNode:this.next.domNode,startOffset:i.length}):(e=document.createTextNode(i),this.parent.insertBefore(this.scroll.create(e),this.next),r={startNode:e,startOffset:i.length}));return t.data="\uFEFF",r}update(t,e){t.forEach(t=>{if("characterData"===t.type&&(t.target===this.leftGuard||t.target===this.rightGuard)){let r=this.restore(t.target);r&&(e.range=r)}})}}let r8=r6;class r9{isComposing=!1;constructor(t,e){this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){let e=t.target instanceof Node?this.scroll.find(t.target,!0):null;!e||e instanceof r8||(this.emitter.emit(rW.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(rW.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(rW.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(rW.events.COMPOSITION_END,t),this.isComposing=!1}}class r7{static DEFAULTS={modules:{}};static themes={default:r7};modules={};constructor(t,e){this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach(t=>{null==this.modules[t]&&this.addModule(t)})}addModule(t){let e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}let it=r7,ie=t=>t.parentElement||t.getRootNode().host||null,ir=t=>{let e=t.getBoundingClientRect(),r="offsetWidth"in t&&Math.abs(e.width)/t.offsetWidth||1,i="offsetHeight"in t&&Math.abs(e.height)/t.offsetHeight||1;return{top:e.top,right:e.left+t.clientWidth*r,bottom:e.top+t.clientHeight*i,left:e.left}},ii=t=>{let e=parseInt(t,10);return Number.isNaN(e)?0:e},is=(t,e,r,i,n,s)=>t<r&&e>i?0:t<r?-(r-t+n):e>i?e-t>i-r?t+n-r:e-i+s:0,il=(t,e)=>{let r=t.ownerDocument,i=e,n=t;for(;n;){let t=n===r.body,e=t?{top:0,right:window.visualViewport?.width??r.documentElement.clientWidth,bottom:window.visualViewport?.height??r.documentElement.clientHeight,left:0}:ir(n),s=getComputedStyle(n),l=is(i.left,i.right,e.left,e.right,ii(s.scrollPaddingLeft),ii(s.scrollPaddingRight)),o=is(i.top,i.bottom,e.top,e.bottom,ii(s.scrollPaddingTop),ii(s.scrollPaddingBottom));if(l||o)if(t)r.defaultView?.scrollBy(l,o);else{let{scrollLeft:t,scrollTop:e}=n;o&&(n.scrollTop+=o),l&&(n.scrollLeft+=l);let r=n.scrollLeft-t,s=n.scrollTop-e;i={left:i.left-r,top:i.top-s,right:i.right-r,bottom:i.bottom-s}}n=t||"fixed"===s.position?null:ie(n)}},io=["block","break","cursor","inline","scroll","text"],ia=(t,e,r)=>{let i=new t8;return io.forEach(t=>{let r=e.query(t);r&&i.register(r)}),t.forEach(t=>{let n=e.query(t);n||r.error(`Cannot register "${t}" specified in "formats" config. Are you sure it was registered?`);let s=0;for(;n;)if(i.register(n),n="blotName"in n?n.requiredContainer??null:null,(s+=1)>100){r.error(`Cycle detected in registering blot requiredContainer: "${t}"`);break}}),i},ic=rV("quill"),ih=new t8;ef.uiClass="ql-ui";class iu{static DEFAULTS={bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:ih,theme:"default"};static events=rW.events;static sources=rW.sources;static version="2.0.3";static imports={delta:e_,parchment:i,"core/module":r3,"core/theme":it};static debug(t){!0===t&&(t="log"),rV.level(t)}static find(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return rz.get(t)||ih.find(t,e)}static import(t){return null==this.imports[t]&&ic.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if("string"!=typeof(arguments.length<=0?void 0:arguments[0])){let t=arguments.length<=0?void 0:arguments[0],e=!!(arguments.length<=1?void 0:arguments[1]),r="attrName"in t?t.attrName:t.blotName;"string"==typeof r?this.register(`formats/${r}`,t,e):Object.keys(t).forEach(r=>{this.register(r,t[r],e)})}else{let t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],r=!!(arguments.length<=2?void 0:arguments[2]);null==this.imports[t]||r||ic.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&"boolean"!=typeof e&&"abstract"!==e.blotName&&ih.register(e),"function"==typeof e.register&&e.register(ih)}}constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.options=function(t,e){let r=id(t);if(!r)throw Error("Invalid Quill container");let i=e.theme&&e.theme!==iu.DEFAULTS.theme?iu.import(`themes/${e.theme}`):it;if(!i)throw Error(`Invalid theme ${e.theme}. Did you register it?`);let{modules:n,...s}=iu.DEFAULTS,{modules:l,...o}=i.DEFAULTS,a=ip(e.modules);null!=a&&a.toolbar&&a.toolbar.constructor!==Object&&(a={...a,toolbar:{container:a.toolbar}});let c=t2({},ip(n),ip(l),a),h={...s,...ig(o),...ig(e)},u=e.registry;return u?e.formats&&ic.warn('Ignoring "formats" option because "registry" is specified'):u=e.formats?ia(e.formats,h.registry,ic):h.registry,{...h,registry:u,container:r,theme:i,modules:Object.entries(c).reduce((t,e)=>{let[r,i]=e;if(!i)return t;let n=iu.import(`modules/${r}`);return null==n?(ic.error(`Cannot load ${r} module. Are you sure you registered it?`),t):{...t,[r]:t2({},n.DEFAULTS||{},i)}},{}),bounds:id(h.bounds)}}(t,e),this.container=this.options.container,null==this.container)return void ic.error("Invalid Quill container",t);this.options.debug&&iu.debug(this.options.debug);let r=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",rz.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new rW;let i=ew.blotName,n=this.options.registry.query(i);if(!n||!("blotName"in n))throw Error(`Cannot initialize Quill without "${i}" blot`);if(this.scroll=new n(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new rJ(this.scroll),this.selection=new rX(this.scroll,this.emitter),this.composition=new r9(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(rW.events.EDITOR_CHANGE,t=>{t===rW.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(rW.events.SCROLL_UPDATE,(t,e)=>{let r=this.selection.lastRange,[i]=this.selection.getRange(),n=r&&i?{oldRange:r,newRange:i}:void 0;im.call(this,()=>this.editor.update(null,e,n),t)}),this.emitter.on(rW.events.SCROLL_EMBED_UPDATE,(t,e)=>{let r=this.selection.lastRange,[i]=this.selection.getRange(),n=r&&i?{oldRange:r,newRange:i}:void 0;im.call(this,()=>{let r=new e_().retain(t.offset(this)).retain({[t.statics.blotName]:e});return this.editor.update(r,[],n)},iu.sources.USER)}),r){let t=this.clipboard.convert({html:`${r}<p><br></p>`,text:"\n"});this.setContents(t)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof t){let e=t;(t=document.createElement("div")).classList.add(e)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,r){return[t,e,,r]=ib(t,e,r),im.call(this,()=>this.editor.deleteText(t,e),r,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;let e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:rW.sources.API;return im.call(this,()=>{let r=this.getSelection(!0),i=new e_;if(null==r)return i;if(this.scroll.query(t,t5.BLOCK))i=this.editor.formatLine(r.index,r.length,{[t]:e});else{if(0===r.length)return this.selection.format(t,e),i;i=this.editor.formatText(r.index,r.length,{[t]:e})}return this.setSelection(r,rW.sources.SILENT),i},r)}formatLine(t,e,r,i,n){let s;return[t,e,s,n]=ib(t,e,r,i,n),im.call(this,()=>this.editor.formatLine(t,e,s),n,t,0)}formatText(t,e,r,i,n){let s;return[t,e,s,n]=ib(t,e,r,i,n),im.call(this,()=>this.editor.formatText(t,e,s),n,t,0)}getBounds(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=null;if(!(r="number"==typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length)))return null;let i=this.container.getBoundingClientRect();return{bottom:r.bottom-i.top,height:r.height,left:r.left-i.left,right:r.right-i.left,top:r.top-i.top,width:r.width}}getContents(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t;return[t,e]=ib(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return t&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return"number"==typeof t&&(e=e??this.getLength()-t),[t,e]=ib(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return"number"==typeof t&&(e=e??this.getLength()-t),[t,e]=ib(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:iu.sources.API;return im.call(this,()=>this.editor.insertEmbed(t,e,r),i,t)}insertText(t,e,r,i,n){let s;return[t,,s,n]=ib(t,0,r,i,n),im.call(this,()=>this.editor.insertText(t,e,s),n,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,r){return[t,e,,r]=ib(t,e,r),im.call(this,()=>this.editor.removeFormat(t,e),r,t)}scrollRectIntoView(t){il(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){let t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rW.sources.API;return im.call(this,()=>{t=new e_(t);let e=this.getLength(),r=this.editor.deleteText(0,e),i=this.editor.insertContents(0,t),n=this.editor.deleteText(this.getLength()-1,1);return r.compose(i).compose(n)},e)}setSelection(t,e,r){null==t?this.selection.setRange(null,e||iu.sources.API):([t,e,,r]=ib(t,e,r),this.selection.setRange(new rG(Math.max(0,t),e),r),r!==rW.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rW.sources.API,r=new e_().insert(t);return this.setContents(r,e)}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:rW.sources.USER,e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rW.sources.API;return im.call(this,()=>(t=new e_(t),this.editor.applyDelta(t)),e,!0)}}function id(t){return"string"==typeof t?document.querySelector(t):t}function ip(t){return Object.entries(t??{}).reduce((t,e)=>{let[r,i]=e;return{...t,[r]:!0===i?{}:i}},{})}function ig(t){return Object.fromEntries(Object.entries(t).filter(t=>void 0!==t[1]))}function im(t,e,r,i){if(!this.isEnabled()&&e===rW.sources.USER&&!this.allowReadOnlyEdits)return new e_;let n=null==r?null:this.getSelection(),s=this.editor.delta,l=t();if(null!=n&&(!0===r&&(r=n.index),null==i?n=iy(n,l,e):0!==i&&(n=iy(n,r,i,e)),this.setSelection(n,rW.sources.SILENT)),l.length()>0){let t=[rW.events.TEXT_CHANGE,l,s,e];this.emitter.emit(rW.events.EDITOR_CHANGE,...t),e!==rW.sources.SILENT&&this.emitter.emit(...t)}return l}function ib(t,e,r,i,n){let s={};return"number"==typeof t.index&&"number"==typeof t.length?("number"!=typeof e&&(n=i,i=r,r=e),e=t.length,t=t.index):"number"!=typeof e&&(n=i,i=r,r=e,e=0),"object"==typeof r?(s=r,n=i):"string"==typeof r&&(null!=i?s[r]=i:n=r),[t,e,s,n=n||rW.sources.API]}function iy(t,e,r,i){let n,s,l="number"==typeof r?r:0;return null==t?null:(e&&"function"==typeof e.transformPosition?[n,s]=[t.index,t.index+t.length].map(t=>e.transformPosition(t,i!==rW.sources.USER)):[n,s]=[t.index,t.index+t.length].map(t=>t<e||t===e&&i===rW.sources.USER?t:l>=0?t+l:Math.max(e,t+l)),new rG(n,s-n))}class iv extends ev{}let ix=iv;function iN(t){return t instanceof rI||t instanceof rM}function iE(t){return"function"==typeof t.updateContent}class iA extends ew{static blotName="scroll";static className="ql-editor";static tagName="DIV";static defaultChild=rI;static allowedChildren=[rI,rM,ix];constructor(t,e,r){let{emitter:i}=r;super(t,e),this.emitter=i,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",t=>this.handleDragStart(t))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;let t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(rW.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(rW.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(rW.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){let[r,i]=this.line(t),[n]=this.line(t+e);if(super.deleteAt(t,e),null!=n&&r!==n&&i>0){if(r instanceof rM||n instanceof rM)return void this.optimize();let t=n.children.head instanceof rO?null:n.children.head;r.moveChildren(n,t),r.remove()}this.optimize()}enable(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,r,i){super.formatAt(t,e,r,i),this.optimize()}insertAt(t,e,r){if(t>=this.length())if(null==r||null==this.scroll.query(e,t5.BLOCK)){let t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t),null==r&&e.endsWith("\n")?t.insertAt(0,e.slice(0,-1),r):t.insertAt(0,e,r)}else{let t=this.scroll.create(e,r);this.appendChild(t)}else super.insertAt(t,e,r);this.optimize()}insertBefore(t,e){if(t.statics.scope===t5.INLINE_BLOT){let r=this.scroll.create(this.statics.defaultChild.blotName);r.appendChild(t),super.insertBefore(r,e)}else super.insertBefore(t,e)}insertContents(t,e){let r=this.deltaToRenderBlocks(e.concat(new e_().insert("\n"))),i=r.pop();if(null==i)return;this.batchStart();let n=r.shift();if(n){let e="block"===n.type&&(0===n.delta.length()||!this.descendant(rM,t)[0]&&t<this.length()),r="block"===n.type?n.delta:new e_().insert({[n.key]:n.value});iw(this,t,r);let i=+("block"===n.type),s=t+r.length()+i;e&&this.insertAt(s-1,"\n");let l=rD(this.line(t)[0]),o=e_.AttributeMap.diff(l,n.attributes)||{};Object.keys(o).forEach(t=>{this.formatAt(s-1,1,t,o[t])}),t=s}let[s,l]=this.children.find(t);r.length&&(s&&(s=s.split(l),l=0),r.forEach(t=>{if("block"===t.type)iw(this.createBlock(t.attributes,s||void 0),0,t.delta);else{let e=this.create(t.key,t.value);this.insertBefore(e,s||void 0),Object.keys(t.attributes).forEach(r=>{e.format(r,t.attributes[r])})}})),"block"===i.type&&i.delta.length()&&iw(this,s?s.offset(s.scroll)+l:this.length(),i.delta),this.batchEnd(),this.optimize()}isEnabled(){return"true"===this.domNode.getAttribute("contenteditable")}leaf(t){let e=this.path(t).pop();if(!e)return[null,-1];let[r,i]=e;return r instanceof ec?[r,i]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(iN,t)}lines(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE,r=(t,e,i)=>{let n=[],s=i;return t.children.forEachAt(e,i,(t,e,i)=>{iN(t)?n.push(t):t instanceof ev&&(n=n.concat(r(t,e,s))),s-=i}),n};return r(this,t,e)}optimize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!this.batch&&(super.optimize(t,e),t.length>0&&this.emitter.emit(rW.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let e=rW.sources.USER;"string"==typeof t&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),(t=t.filter(t=>{let{target:e}=t,r=this.find(e,!0);return r&&!iE(r)})).length>0&&this.emitter.emit(rW.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(rW.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,r){let[i]=this.descendant(t=>t instanceof rM,t);i&&i.statics.blotName===e&&iE(i)&&i.updateContent(r)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){let e=[],r=new e_;return t.forEach(t=>{let i=t?.insert;if(i)if("string"==typeof i){let n=i.split("\n");n.slice(0,-1).forEach(i=>{r.insert(i,t.attributes),e.push({type:"block",delta:r,attributes:t.attributes??{}}),r=new e_});let s=n[n.length-1];s&&r.insert(s,t.attributes)}else{let n=Object.keys(i)[0];if(!n)return;this.query(n,t5.INLINE)?r.push(t):(r.length()&&e.push({type:"block",delta:r,attributes:{}}),r=new e_,e.push({type:"blockEmbed",key:n,value:i[n],attributes:t.attributes??{}}))}}),r.length()&&e.push({type:"block",delta:r,attributes:{}}),e}createBlock(t,e){let r,i={};Object.entries(t).forEach(t=>{let[e,n]=t;null!=this.query(e,t5.BLOCK&t5.BLOT)?r=e:i[e]=n});let n=this.create(r||this.statics.defaultChild.blotName,r?t[r]:void 0);this.insertBefore(n,e||void 0);let s=n.length();return Object.entries(i).forEach(t=>{let[e,r]=t;n.formatAt(0,s,e,r)}),n}}function iw(t,e,r){r.reduce((e,r)=>{let i=e_.Op.length(r),n=r.attributes||{};if(null!=r.insert){if("string"==typeof r.insert){let i=r.insert;t.insertAt(e,i);let[s]=t.descendant(ec,e),l=rD(s);n=e_.AttributeMap.diff(l,n)||{}}else if("object"==typeof r.insert){let i=Object.keys(r.insert)[0];if(null==i)return e;if(t.insertAt(e,i,r.insert[i]),null!=t.scroll.query(i,t5.INLINE)){let[r]=t.descendant(ec,e),i=rD(r);n=e_.AttributeMap.diff(i,n)||{}}}}return Object.keys(n).forEach(r=>{t.formatAt(e,i,r,n[r])}),e+i},e)}let iq={scope:t5.BLOCK,whitelist:["right","center","justify"]},ik=new t4("align","align",iq),i_=new et("align","ql-align",iq),iL=new ei("align","text-align",iq);class iO extends ei{value(t){let e=super.value(t);if(!e.startsWith("rgb("))return e;let r=(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"")).split(",").map(t=>`00${parseInt(t,10).toString(16)}`.slice(-2)).join("");return`#${r}`}}let iS=new et("color","ql-color",{scope:t5.INLINE}),iT=new iO("color","color",{scope:t5.INLINE}),ij=new et("background","ql-bg",{scope:t5.INLINE}),iC=new iO("background","background-color",{scope:t5.INLINE});class iR extends ix{static create(t){let e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(t=>1>=t.length()?"":t.domNode.innerText).join("\n").slice(t,t+e)}html(t,e){return`<pre>
${rj(this.code(t,e))}
</pre>`}}class iI extends rI{static TAB="  ";static register(){iu.register(iR)}}class iM extends rR{}iM.blotName="code",iM.tagName="CODE",iI.blotName="code-block",iI.className="ql-code-block",iI.tagName="DIV",iR.blotName="code-block-container",iR.className="ql-code-block-container",iR.tagName="DIV",iR.allowedChildren=[iI],iI.allowedChildren=[rS,rO,rU],iI.requiredContainer=iR;let iB={scope:t5.BLOCK,whitelist:["rtl"]},iD=new t4("direction","dir",iB),iU=new et("direction","ql-direction",iB),iP=new ei("direction","direction",iB),iz={scope:t5.INLINE,whitelist:["serif","monospace"]},iF=new et("font","ql-font",iz);class i$ extends ei{value(t){return super.value(t).replace(/["']/g,"")}}let iH=new i$("font","font-family",iz),iV=new et("size","ql-size",{scope:t5.INLINE,whitelist:["small","large","huge"]}),iK=new ei("size","font-size",{scope:t5.INLINE,whitelist:["10px","18px","32px"]}),iW=rV("quill:keyboard"),iZ=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class iG extends r3{static match(t,e){return!["altKey","ctrlKey","metaKey","shiftKey"].some(r=>!!e[r]!==t[r]&&null!==e[r])&&(e.key===t.key||e.key===t.which)}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(t=>{this.options.bindings[t]&&this.addBinding(this.options.bindings[t])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=function(t){if("string"==typeof t||"number"==typeof t)t={key:t};else{if("object"!=typeof t)return null;t=rd(t)}return t.shortKey&&(t[iZ]=t.shortKey,delete t.shortKey),t}(t);if(null==i)return void iW.warn("Attempted to add invalid keyboard binding",i);"function"==typeof e&&(e={handler:e}),"function"==typeof r&&(r={handler:r}),(Array.isArray(i.key)?i.key:[i.key]).forEach(t=>{let n={...i,key:t,...e,...r};this.bindings[n.key]=this.bindings[n.key]||[],this.bindings[n.key].push(n)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||229===t.keyCode&&("Enter"===t.key||"Backspace"===t.key))return;let e=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(e=>iG.match(t,e));if(0===e.length)return;let r=iu.find(t.target,!0);if(r&&r.scroll!==this.quill.scroll)return;let i=this.quill.getSelection();if(null==i||!this.quill.hasFocus())return;let[n,s]=this.quill.getLine(i.index),[l,o]=this.quill.getLeaf(i.index),[a,c]=0===i.length?[l,o]:this.quill.getLeaf(i.index+i.length),h=l instanceof ek?l.value().slice(0,o):"",u=a instanceof ek?a.value().slice(c):"",d={collapsed:0===i.length,empty:0===i.length&&1>=n.length(),format:this.quill.getFormat(i),line:n,offset:s,prefix:h,suffix:u,event:t};e.some(t=>{if(null!=t.collapsed&&t.collapsed!==d.collapsed||null!=t.empty&&t.empty!==d.empty||null!=t.offset&&t.offset!==d.offset)return!1;if(Array.isArray(t.format)){if(t.format.every(t=>null==d.format[t]))return!1}else if("object"==typeof t.format&&!Object.keys(t.format).every(e=>!0===t.format[e]?null!=d.format[e]:!1===t.format[e]?null==d.format[e]:rL(t.format[e],d.format[e])))return!1;return(null==t.prefix||!!t.prefix.test(d.prefix))&&(null==t.suffix||!!t.suffix.test(d.suffix))&&!0!==t.handler.call(this,i,d,t)})&&t.preventDefault()})}handleBackspace(t,e){let r=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(0===t.index||1>=this.quill.getLength())return;let i={},[n]=this.quill.getLine(t.index),s=new e_().retain(t.index-r).delete(r);if(0===e.offset){let[e]=this.quill.getLine(t.index-1);if(e&&!("block"===e.statics.blotName&&1>=e.length())){let e=n.formats(),r=this.quill.getFormat(t.index-1,1);if(Object.keys(i=e_.AttributeMap.diff(e,r)||{}).length>0){let e=new e_().retain(t.index+n.length()-2).retain(1,i);s=s.compose(e)}}}this.quill.updateContents(s,iu.sources.USER),this.quill.focus()}handleDelete(t,e){let r=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-r)return;let i={},[n]=this.quill.getLine(t.index),s=new e_().retain(t.index).delete(r);if(e.offset>=n.length()-1){let[e]=this.quill.getLine(t.index+1);if(e){let r=n.formats(),l=this.quill.getFormat(t.index,1);Object.keys(i=e_.AttributeMap.diff(r,l)||{}).length>0&&(s=s.retain(e.length()-1).retain(1,i))}}this.quill.updateContents(s,iu.sources.USER),this.quill.focus()}handleDeleteRange(t){i1({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){let r=Object.keys(e.format).reduce((t,r)=>(this.quill.scroll.query(r,t5.BLOCK)&&!Array.isArray(e.format[r])&&(t[r]=e.format[r]),t),{}),i=new e_().retain(t.index).delete(t.length).insert("\n",r);this.quill.updateContents(i,iu.sources.USER),this.quill.setSelection(t.index+1,iu.sources.SILENT),this.quill.focus()}}function iX(t){return{key:"Tab",shiftKey:!t,format:{"code-block":!0},handler(e,r){let{event:i}=r,{TAB:n}=this.quill.scroll.query("code-block");if(0===e.length&&!i.shiftKey){this.quill.insertText(e.index,n,iu.sources.USER),this.quill.setSelection(e.index+n.length,iu.sources.SILENT);return}let s=0===e.length?this.quill.getLines(e.index,1):this.quill.getLines(e),{index:l,length:o}=e;s.forEach((e,r)=>{t?(e.insertAt(0,n),0===r?l+=n.length:o+=n.length):e.domNode.textContent.startsWith(n)&&(e.deleteAt(0,n.length),0===r?l-=n.length:o-=n.length)}),this.quill.update(iu.sources.USER),this.quill.setSelection(l,o,iu.sources.SILENT)}}}function iY(t,e){return{key:t,shiftKey:e,altKey:null,["ArrowLeft"===t?"prefix":"suffix"]:/^$/,handler(r){let{index:i}=r;"ArrowRight"===t&&(i+=r.length+1);let[n]=this.quill.getLeaf(i);return!(n instanceof eN)||("ArrowLeft"===t?e?this.quill.setSelection(r.index-1,r.length+1,iu.sources.USER):this.quill.setSelection(r.index-1,iu.sources.USER):e?this.quill.setSelection(r.index,r.length+1,iu.sources.USER):this.quill.setSelection(r.index+r.length+1,iu.sources.USER),!1)}}}function iQ(t){return{key:t[0],shortKey:!0,handler(e,r){this.quill.format(t,!r.format[t],iu.sources.USER)}}}function iJ(t){return{key:t?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(e,r){let i=t?"prev":"next",n=r.line,s=n.parent[i];if(null!=s){if("table-row"===s.statics.blotName){let t=s.children.head,e=n;for(;null!=e.prev;)e=e.prev,t=t.next;let i=t.offset(this.quill.scroll)+Math.min(r.offset,t.length()-1);this.quill.setSelection(i,0,iu.sources.USER)}}else{let e=n.table()[i];null!=e&&(t?this.quill.setSelection(e.offset(this.quill.scroll)+e.length()-1,0,iu.sources.USER):this.quill.setSelection(e.offset(this.quill.scroll),0,iu.sources.USER))}return!1}}}function i1(t){let{quill:e,range:r}=t,i=e.getLines(r),n={};if(i.length>1){let t=i[0].formats(),e=i[i.length-1].formats();n=e_.AttributeMap.diff(e,t)||{}}e.deleteText(r,iu.sources.USER),Object.keys(n).length>0&&e.formatLine(r.index,1,n,iu.sources.USER),e.setSelection(r.index,iu.sources.SILENT)}iG.DEFAULTS={bindings:{bold:iQ("bold"),italic:iQ("italic"),underline:iQ("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(t,e){return!!e.collapsed&&0!==e.offset||(this.quill.format("indent","+1",iu.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(t,e){return!!e.collapsed&&0!==e.offset||(this.quill.format("indent","-1",iu.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(t,e){null!=e.format.indent?this.quill.format("indent","-1",iu.sources.USER):null!=e.format.list&&this.quill.format("list",!1,iu.sources.USER)}},"indent code-block":iX(!0),"outdent code-block":iX(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(t){this.quill.deleteText(t.index-1,1,iu.sources.USER)}},tab:{key:"Tab",handler(t,e){if(e.format.table)return!0;this.quill.history.cutoff();let r=new e_().retain(t.index).delete(t.length).insert("	");return this.quill.updateContents(r,iu.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,iu.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,iu.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(t,e){let r={list:!1};e.format.indent&&(r.indent=!1),this.quill.formatLine(t.index,t.length,r,iu.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(t){let[e,r]=this.quill.getLine(t.index),i={...e.formats(),list:"checked"},n=new e_().retain(t.index).insert("\n",i).retain(e.length()-r-1).retain(1,{list:"unchecked"});this.quill.updateContents(n,iu.sources.USER),this.quill.setSelection(t.index+1,iu.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(t,e){let[r,i]=this.quill.getLine(t.index),n=new e_().retain(t.index).insert("\n",e.format).retain(r.length()-i-1).retain(1,{header:null});this.quill.updateContents(n,iu.sources.USER),this.quill.setSelection(t.index+1,iu.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(t){let e=this.quill.getModule("table");if(e){var r,i,n,s;let[l,o,a,c]=e.getTable(t),h=(r=0,i=o,n=a,s=c,null==i.prev&&null==i.next?null==n.prev&&null==n.next?0===s?-1:1:null==n.prev?-1:1:null==i.prev?-1:null==i.next?1:null);if(null==h)return;let u=l.offset();if(h<0){let e=new e_().retain(u).insert("\n");this.quill.updateContents(e,iu.sources.USER),this.quill.setSelection(t.index+1,t.length,iu.sources.SILENT)}else if(h>0){u+=l.length();let t=new e_().retain(u).insert("\n");this.quill.updateContents(t,iu.sources.USER),this.quill.setSelection(u,iu.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(t,e){let{event:r,line:i}=e,n=i.offset(this.quill.scroll);r.shiftKey?this.quill.setSelection(n-1,iu.sources.USER):this.quill.setSelection(n+i.length(),iu.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(t,e){let r;if(null==this.quill.scroll.query("list"))return!0;let{length:i}=e.prefix,[n,s]=this.quill.getLine(t.index);if(s>i)return!0;switch(e.prefix.trim()){case"[]":case"[ ]":r="unchecked";break;case"[x]":r="checked";break;case"-":case"*":r="bullet";break;default:r="ordered"}this.quill.insertText(t.index," ",iu.sources.USER),this.quill.history.cutoff();let l=new e_().retain(t.index-s).delete(i+1).retain(n.length()-2-s).retain(1,{list:r});return this.quill.updateContents(l,iu.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-i,iu.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(t){let[e,r]=this.quill.getLine(t.index),i=2,n=e;for(;null!=n&&1>=n.length()&&n.formats()["code-block"];)if(n=n.prev,(i-=1)<=0){let i=new e_().retain(t.index+e.length()-r-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(i,iu.sources.USER),this.quill.setSelection(t.index-1,iu.sources.SILENT),!1}return!0}},"embed left":iY("ArrowLeft",!1),"embed left shift":iY("ArrowLeft",!0),"embed right":iY("ArrowRight",!1),"embed right shift":iY("ArrowRight",!0),"table down":iJ(!1),"table up":iJ(!0)}};let i0=/font-weight:\s*normal/,i2=["P","OL","UL"],i5=t=>t&&i2.includes(t.tagName),i4=t=>{Array.from(t.querySelectorAll("br")).filter(t=>i5(t.previousElementSibling)&&i5(t.nextElementSibling)).forEach(t=>{t.parentNode?.removeChild(t)})},i3=t=>{Array.from(t.querySelectorAll('b[style*="font-weight"]')).filter(t=>t.getAttribute("style")?.match(i0)).forEach(e=>{let r=t.createDocumentFragment();r.append(...e.childNodes),e.parentNode?.replaceChild(r,e)})},i6=/\bmso-list:[^;]*ignore/i,i8=/\bmso-list:[^;]*\bl(\d+)/i,i9=/\bmso-list:[^;]*\blevel(\d+)/i,i7=(t,e)=>{let r=t.getAttribute("style"),i=r?.match(i8);if(!i)return null;let n=Number(i[1]),s=r?.match(i9),l=s?Number(s[1]):1,o=RegExp(`@list l${n}:level${l}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),a=e.match(o);return{id:n,indent:l,type:a&&"bullet"===a[1]?"bullet":"ordered",element:t}},nt=t=>{let e=Array.from(t.querySelectorAll("[style*=mso-list]")),r=[],i=[];e.forEach(t=>{(t.getAttribute("style")||"").match(i6)?r.push(t):i.push(t)}),r.forEach(t=>t.parentNode?.removeChild(t));let n=t.documentElement.innerHTML,s=i.map(t=>i7(t,n)).filter(t=>t);for(;s.length;){let t=[],e=s.shift();for(;e;)t.push(e),e=s.length&&s[0]?.element===e.element.nextElementSibling&&s[0].id===e.id?s.shift():null;let r=document.createElement("ul");t.forEach(t=>{let e=document.createElement("li");e.setAttribute("data-list",t.type),t.indent>1&&e.setAttribute("class",`ql-indent-${t.indent-1}`),e.innerHTML=t.element.innerHTML,r.appendChild(e)});let i=t[0]?.element,{parentNode:n}=i??{};i&&n?.replaceChild(r,i),t.slice(1).forEach(t=>{let{element:e}=t;n?.removeChild(e)})}},ne=[function(t){"urn:schemas-microsoft-com:office:word"===t.documentElement.getAttribute("xmlns:w")&&nt(t)},function(t){t.querySelector('[id^="docs-internal-guid-"]')&&(i3(t),i4(t))}],nr=t=>{t.documentElement&&ne.forEach(e=>{e(t)})},ni=rV("quill:clipboard"),nn=[[Node.TEXT_NODE,function(t,e,r){let i=t.data;if(t.parentElement?.tagName==="O:P")return e.insert(i.trim());if(!function t(e){return null!=e&&(nu.has(e)||("PRE"===e.tagName?nu.set(e,!0):nu.set(e,t(e.parentNode))),nu.get(e))}(t)){if(0===i.trim().length&&i.includes("\n")&&(!t.previousElementSibling||!t.nextElementSibling||nh(t.previousElementSibling,r)||nh(t.nextElementSibling,r)))return e;i=(i=i.replace(/[^\S\u00a0]/g," ")).replace(/ {2,}/g," "),(null==t.previousSibling&&null!=t.parentElement&&nh(t.parentElement,r)||t.previousSibling instanceof Element&&nh(t.previousSibling,r))&&(i=i.replace(/^ /,"")),(null==t.nextSibling&&null!=t.parentElement&&nh(t.parentElement,r)||t.nextSibling instanceof Element&&nh(t.nextSibling,r))&&(i=i.replace(/ $/,"")),i=i.replaceAll("\xa0"," ")}return e.insert(i)}],[Node.TEXT_NODE,np],["br",function(t,e){return nc(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,np],[Node.ELEMENT_NODE,function(t,e,r){let i=r.query(t);if(null==i)return e;if(i.prototype instanceof eN){let e={},n=i.value(t);if(null!=n)return e[i.blotName]=n,new e_().insert(e,i.formats(t,r))}else if(i.prototype instanceof eb&&!nc(e,"\n")&&e.insert("\n"),"blotName"in i&&"formats"in i&&"function"==typeof i.formats)return na(e,i.blotName,i.formats(t,r),r);return e}],[Node.ELEMENT_NODE,function(t,e,r){let i=t4.keys(t),n=et.keys(t),s=ei.keys(t),l={};return i.concat(n).concat(s).forEach(e=>{let i=r.query(e,t5.ATTRIBUTE);null!=i&&(l[i.attrName]=i.value(t),l[i.attrName])||(null!=(i=ns[e])&&(i.attrName===e||i.keyName===e)&&(l[i.attrName]=i.value(t)||void 0),null!=(i=nl[e])&&(i.attrName===e||i.keyName===e)&&(l[(i=nl[e]).attrName]=i.value(t)||void 0))}),Object.entries(l).reduce((t,e)=>{let[i,n]=e;return na(t,i,n,r)},e)}],[Node.ELEMENT_NODE,function(t,e,r){let i={},n=t.style||{};return("italic"===n.fontStyle&&(i.italic=!0),"underline"===n.textDecoration&&(i.underline=!0),"line-through"===n.textDecoration&&(i.strike=!0),(n.fontWeight?.startsWith("bold")||parseInt(n.fontWeight,10)>=700)&&(i.bold=!0),e=Object.entries(i).reduce((t,e)=>{let[i,n]=e;return na(t,i,n,r)},e),parseFloat(n.textIndent||0)>0)?new e_().insert("	").concat(e):e}],["li",function(t,e,r){let i=r.query(t);if(null==i||"list"!==i.blotName||!nc(e,"\n"))return e;let n=-1,s=t.parentNode;for(;null!=s;)["OL","UL"].includes(s.tagName)&&(n+=1),s=s.parentNode;return n<=0?e:e.reduce((t,e)=>e.insert?e.attributes&&"number"==typeof e.attributes.indent?t.push(e):t.insert(e.insert,{indent:n,...e.attributes||{}}):t,new e_)}],["ol, ul",function(t,e,r){let i="OL"===t.tagName?"ordered":"bullet",n=t.getAttribute("data-checked");return n&&(i="true"===n?"checked":"unchecked"),na(e,"list",i,r)}],["pre",function(t,e,r){let i=r.query("code-block");return na(e,"code-block",!i||!("formats"in i)||"function"!=typeof i.formats||i.formats(t,r),r)}],["tr",function(t,e,r){let i=t.parentElement?.tagName==="TABLE"?t.parentElement:t.parentElement?.parentElement;return null!=i?na(e,"table",Array.from(i.querySelectorAll("tr")).indexOf(t)+1,r):e}],["b",nf("bold")],["i",nf("italic")],["strike",nf("strike")],["style",function(){return new e_}]],ns=[ik,iD].reduce((t,e)=>(t[e.keyName]=e,t),{}),nl=[iL,iC,iT,iP,iH,iK].reduce((t,e)=>(t[e.keyName]=e,t),{});class no extends r3{static DEFAULTS={matchers:[]};constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",t=>this.onCaptureCopy(t,!1)),this.quill.root.addEventListener("cut",t=>this.onCaptureCopy(t,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],nn.concat(this.options.matchers??[]).forEach(t=>{let[e,r]=t;this.addMatcher(e,r)})}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:r}=t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(i[iI.blotName])return new e_().insert(r||"",{[iI.blotName]:i[iI.blotName]});if(!e)return new e_().insert(r||"",i);let n=this.convertHTML(e);return nc(n,"\n")&&(null==n.ops[n.ops.length-1].attributes||i.table)?n.compose(new e_().retain(n.length()-1).delete(1)):n}normalizeHTML(t){nr(t)}convertHTML(t){let e=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(e);let r=e.body,i=new WeakMap,[n,s]=this.prepareMatching(r,i);return nd(this.quill.scroll,r,n,s,i)}dangerouslyPasteHTML(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:iu.sources.API;if("string"==typeof t){let r=this.convert({html:t,text:""});this.quill.setContents(r,e),this.quill.setSelection(0,iu.sources.SILENT)}else{let i=this.convert({html:e,text:""});this.quill.updateContents(new e_().retain(t).concat(i),r),this.quill.setSelection(t+i.length(),iu.sources.SILENT)}}onCaptureCopy(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.defaultPrevented)return;t.preventDefault();let[r]=this.quill.selection.getRange();if(null==r)return;let{html:i,text:n}=this.onCopy(r,e);t.clipboardData?.setData("text/plain",n),t.clipboardData?.setData("text/html",i),e&&i1({range:r,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(t=>"#"!==t[0]).join("\n")}onCapturePaste(t){if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();let e=this.quill.getSelection(!0);if(null==e)return;let r=t.clipboardData?.getData("text/html"),i=t.clipboardData?.getData("text/plain");if(!r&&!i){let e=t.clipboardData?.getData("text/uri-list");e&&(i=this.normalizeURIList(e))}let n=Array.from(t.clipboardData?.files||[]);if(!r&&n.length>0)return void this.quill.uploader.upload(e,n);if(r&&n.length>0){let t=new DOMParser().parseFromString(r,"text/html");if(1===t.body.childElementCount&&t.body.firstElementChild?.tagName==="IMG")return void this.quill.uploader.upload(e,n)}this.onPaste(e,{html:r,text:i})}onCopy(t){let e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:r,html:i}=e,n=this.quill.getFormat(t.index),s=this.convert({text:r,html:i},n);ni.log("onPaste",s,{text:r,html:i});let l=new e_().retain(t.index).delete(t.length).concat(s);this.quill.updateContents(l,iu.sources.USER),this.quill.setSelection(l.length()-t.length,iu.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){let r=[],i=[];return this.matchers.forEach(n=>{let[s,l]=n;switch(s){case Node.TEXT_NODE:i.push(l);break;case Node.ELEMENT_NODE:r.push(l);break;default:Array.from(t.querySelectorAll(s)).forEach(t=>{if(e.has(t)){let r=e.get(t);r?.push(l)}else e.set(t,[l])})}}),[r,i]}}function na(t,e,r,i){return i.query(e)?t.reduce((t,i)=>i.insert?i.attributes&&i.attributes[e]?t.push(i):t.insert(i.insert,{...r?{[e]:r}:{},...i.attributes}):t,new e_):t}function nc(t,e){let r="";for(let i=t.ops.length-1;i>=0&&r.length<e.length;--i){let e=t.ops[i];if("string"!=typeof e.insert)break;r=e.insert+r}return r.slice(-1*e.length)===e}function nh(t,e){if(!(t instanceof Element))return!1;let r=e.query(t);return(!r||!(r.prototype instanceof eN))&&["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(t.tagName.toLowerCase())}let nu=new WeakMap;function nd(t,e,r,i,n){return e.nodeType===e.TEXT_NODE?i.reduce((r,i)=>i(e,r,t),new e_):e.nodeType===e.ELEMENT_NODE?Array.from(e.childNodes||[]).reduce((s,l)=>{let o=nd(t,l,r,i,n);return l.nodeType===e.ELEMENT_NODE&&(o=r.reduce((e,r)=>r(l,e,t),o),o=(n.get(l)||[]).reduce((e,r)=>r(l,e,t),o)),s.concat(o)},new e_):new e_}function nf(t){return(e,r,i)=>na(r,t,!0,i)}function np(t,e,r){if(!nc(e,"\n")){if(nh(t,r)&&(t.childNodes.length>0||t instanceof HTMLParagraphElement))return e.insert("\n");if(e.length()>0&&t.nextSibling){let i=t.nextSibling;for(;null!=i;){if(nh(i,r))return e.insert("\n");let t=r.query(i);if(t&&t.prototype instanceof rM)return e.insert("\n");i=i.firstChild}}}return e}class ng extends r3{static DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};lastRecorded=0;ignoreChange=!1;stack={undo:[],redo:[]};currentRange=null;constructor(t,e){super(t,e),this.quill.on(iu.events.EDITOR_CHANGE,(t,e,r,i)=>{t===iu.events.SELECTION_CHANGE?e&&i!==iu.sources.SILENT&&(this.currentRange=e):t===iu.events.TEXT_CHANGE&&(this.ignoreChange||(this.options.userOnly&&i!==iu.sources.USER?this.transform(e):this.record(e,r)),this.currentRange=nb(this.currentRange,e))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",t=>{"historyUndo"===t.inputType?(this.undo(),t.preventDefault()):"historyRedo"===t.inputType&&(this.redo(),t.preventDefault())})}change(t,e){if(0===this.stack[t].length)return;let r=this.stack[t].pop();if(!r)return;let i=this.quill.getContents(),n=r.delta.invert(i);this.stack[e].push({delta:n,range:nb(r.range,n)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(r.delta,iu.sources.USER),this.ignoreChange=!1,this.restoreSelection(r)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(t,e){if(0===t.ops.length)return;this.stack.redo=[];let r=t.invert(e),i=this.currentRange,n=Date.now();if(this.lastRecorded+this.options.delay>n&&this.stack.undo.length>0){let t=this.stack.undo.pop();t&&(r=r.compose(t.delta),i=t.range)}else this.lastRecorded=n;0!==r.length()&&(this.stack.undo.push({delta:r,range:i}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(t){nm(this.stack.undo,t),nm(this.stack.redo,t)}undo(){this.change("undo","redo")}restoreSelection(t){if(t.range)this.quill.setSelection(t.range,iu.sources.USER);else{let e=function(t,e){let r=e.reduce((t,e)=>t+(e.delete||0),0),i=e.length()-r;return function(t,e){let r=e.ops[e.ops.length-1];return null!=r&&(null!=r.insert?"string"==typeof r.insert&&r.insert.endsWith("\n"):null!=r.attributes&&Object.keys(r.attributes).some(e=>null!=t.query(e,t5.BLOCK)))}(t,e)&&(i-=1),i}(this.quill.scroll,t.delta);this.quill.setSelection(e,iu.sources.USER)}}}function nm(t,e){let r=e;for(let e=t.length-1;e>=0;e-=1){let i=t[e];t[e]={delta:r.transform(i.delta,!0),range:i.range&&nb(i.range,r)},r=i.delta.transform(r),0===t[e].delta.length()&&t.splice(e,1)}}function nb(t,e){if(!t)return t;let r=e.transformPosition(t.index);return{index:r,length:e.transformPosition(t.index+t.length)-r}}class ny extends r3{constructor(t,e){super(t,e),t.root.addEventListener("drop",e=>{e.preventDefault();let r=null;if(document.caretRangeFromPoint)r=document.caretRangeFromPoint(e.clientX,e.clientY);else if(document.caretPositionFromPoint){let t=document.caretPositionFromPoint(e.clientX,e.clientY);(r=document.createRange()).setStart(t.offsetNode,t.offset),r.setEnd(t.offsetNode,t.offset)}let i=r&&t.selection.normalizeNative(r);if(i){let r=t.selection.normalizedToRange(i);e.dataTransfer?.files&&this.upload(r,e.dataTransfer.files)}})}upload(t,e){let r=[];Array.from(e).forEach(t=>{t&&this.options.mimetypes?.includes(t.type)&&r.push(t)}),r.length>0&&this.options.handler.call(this,t,r)}}ny.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(t,e){this.quill.scroll.query("image")&&Promise.all(e.map(t=>new Promise(e=>{let r=new FileReader;r.onload=()=>{e(r.result)},r.readAsDataURL(t)}))).then(e=>{let r=e.reduce((t,e)=>t.insert({image:e}),new e_().retain(t.index).delete(t.length));this.quill.updateContents(r,rW.sources.USER),this.quill.setSelection(t.index+e.length,rW.sources.SILENT)})}};let nv=["insertText","insertReplacementText"];class nx extends r3{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",t=>{this.handleBeforeInput(t)}),/Android/i.test(navigator.userAgent)||t.on(iu.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){i1({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(0===t.length)return!1;if(e){let r=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new e_().retain(t.index).insert(e,r),iu.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,iu.sources.SILENT),!0}handleBeforeInput(t){var e;if(this.quill.composition.isComposing||t.defaultPrevented||!nv.includes(t.inputType))return;let r=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!r||!0===r.collapsed)return;let i="string"==typeof(e=t).data?e.data:e.dataTransfer?.types.includes("text/plain")?e.dataTransfer.getData("text/plain"):null;if(null==i)return;let n=this.quill.selection.normalizeNative(r),s=n?this.quill.selection.normalizedToRange(n):null;s&&this.replaceText(s,i)&&t.preventDefault()}handleCompositionStart(){let t=this.quill.getSelection();t&&this.replaceText(t)}}let nN=/Mac/i.test(navigator.platform),nE=t=>"ArrowLeft"===t.key||"ArrowRight"===t.key||"ArrowUp"===t.key||"ArrowDown"===t.key||"Home"===t.key||!!nN&&"a"===t.key&&!0===t.ctrlKey;class nA extends r3{isListening=!1;selectionChangeDeadline=0;constructor(t,e){super(t,e),this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(t,e){let{line:r,event:i}=e;if(!(r instanceof ef)||!r.uiNode)return!0;let n="rtl"===getComputedStyle(r.domNode).direction;return!!n&&"ArrowRight"!==i.key||!n&&"ArrowLeft"!==i.key||(this.quill.setSelection(t.index-1,t.length+ +!!i.shiftKey,iu.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",t=>{!t.defaultPrevented&&nE(t)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){this.selectionChangeDeadline=Date.now()+100,this.isListening||(this.isListening=!0,document.addEventListener("selectionchange",()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()},{once:!0}))}handleSelectionChange(){let t=document.getSelection();if(!t)return;let e=t.getRangeAt(0);if(!0!==e.collapsed||0!==e.startOffset)return;let r=this.quill.scroll.find(e.startContainer);if(!(r instanceof ef)||!r.uiNode)return;let i=document.createRange();i.setStartAfter(r.uiNode),i.setEndAfter(r.uiNode),t.removeAllRanges(),t.addRange(i)}}iu.register({"blots/block":rI,"blots/block/embed":rM,"blots/break":rO,"blots/container":ix,"blots/cursor":rU,"blots/embed":r8,"blots/inline":rR,"blots/scroll":iA,"blots/text":rS,"modules/clipboard":no,"modules/history":ng,"modules/keyboard":iG,"modules/uploader":ny,"modules/input":nx,"modules/uiNode":nA});class nw extends et{add(t,e){let r=0;if("+1"===e||"-1"===e){let i=this.value(t)||0;r="+1"===e?i+1:i-1}else"number"==typeof e&&(r=e);return 0===r?(this.remove(t),!0):super.add(t,r.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}let nq=new nw("indent","ql-indent",{scope:t5.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});class nk extends rI{static blotName="blockquote";static tagName="blockquote"}class n_ extends rI{static blotName="header";static tagName=["H1","H2","H3","H4","H5","H6"];static formats(t){return this.tagName.indexOf(t.tagName)+1}}class nL extends ix{}nL.blotName="list-container",nL.tagName="OL";class nO extends rI{static create(t){let e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){iu.register(nL)}constructor(t,e){super(t,e);let r=e.ownerDocument.createElement("span"),i=r=>{if(!t.isEnabled())return;let i=this.statics.formats(e,t);"checked"===i?(this.format("list","unchecked"),r.preventDefault()):"unchecked"===i&&(this.format("list","checked"),r.preventDefault())};r.addEventListener("mousedown",i),r.addEventListener("touchstart",i),this.attachUI(r)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}nO.blotName="list",nO.tagName="LI",nL.allowedChildren=[nO],nO.requiredContainer=nL;class nS extends rR{static blotName="bold";static tagName=["STRONG","B"];static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}let nT=nS;class nj extends nT{static blotName="italic";static tagName=["EM","I"]}class nC extends rR{static blotName="link";static tagName="A";static SANITIZED_URL="about:blank";static PROTOCOL_WHITELIST=["http","https","mailto","tel","sms"];static create(t){let e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return nR(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("href",this.constructor.sanitize(e)):super.format(t,e)}}function nR(t,e){let r=document.createElement("a");r.href=t;let i=r.href.slice(0,r.href.indexOf(":"));return e.indexOf(i)>-1}class nI extends rR{static blotName="script";static tagName=["SUB","SUP"];static create(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):super.create(t)}static formats(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}}class nM extends nT{static blotName="strike";static tagName=["S","STRIKE"]}class nB extends rR{static blotName="underline";static tagName="U"}class nD extends r8{static blotName="formula";static className="ql-formula";static tagName="SPAN";static create(t){if(null==window.katex)throw Error("Formula module requires KaTeX.");let e=super.create(t);return"string"==typeof t&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){let{formula:t}=this.value();return`<span>${t}</span>`}}let nU=["alt","height","width"];class nP extends eN{static blotName="image";static tagName="IMG";static create(t){let e=super.create(t);return"string"==typeof t&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return nU.reduce((e,r)=>(t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return nR(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){nU.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}let nz=["height","width"];class nF extends rM{static blotName="video";static className="ql-video";static tagName="IFRAME";static create(t){let e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return nz.reduce((e,r)=>(t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e),{})}static sanitize(t){return nC.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){nz.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){let{video:t}=this.value();return`<a href="${t}">${t}</a>`}}let n$=new et("code-token","hljs",{scope:t5.INLINE});class nH extends rR{static formats(t,e){for(;null!=t&&t!==e.domNode;){if(t.classList&&t.classList.contains(iI.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,r){super(t,e,r),n$.add(this.domNode,r)}format(t,e){t!==nH.blotName?super.format(t,e):e?n$.add(this.domNode,e):(n$.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),n$.value(this.domNode)||this.unwrap()}}nH.blotName="code-token",nH.className="ql-token";class nV extends iI{static create(t){let e=super.create(t);return"string"==typeof t&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),nH.blotName,!1),super.replaceWith(t,e)}}class nK extends iR{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===nV.blotName&&(this.forceNext=!0,this.children.forEach(r=>{r.format(t,e)}))}formatAt(t,e,r,i){r===nV.blotName&&(this.forceNext=!0),super.formatAt(t,e,r,i)}highlight(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==this.children.head)return;let r=Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode),i=`${r.map(t=>t.textContent).join("\n")}
`,n=nV.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==i){if(i.trim().length>0||null==this.cachedText){let e=this.children.reduce((t,e)=>t.concat(rB(e,!1)),new e_),r=t(i,n);e.diff(r).reduce((t,e)=>{let{retain:r,attributes:i}=e;return r?(i&&Object.keys(i).forEach(e=>{[nV.blotName,nH.blotName].includes(e)&&this.formatAt(t,r,e,i[e])}),t+r):t},0)}this.cachedText=i,this.forceNext=!1}}html(t,e){let[r]=this.children.find(t),i=r?nV.formats(r.domNode):"plain";return`<pre data-language="${i}">
${rj(this.code(t,e))}
</pre>`}optimize(t){if(super.optimize(t),null!=this.parent&&null!=this.children.head&&null!=this.uiNode){let t=nV.formats(this.children.head.domNode);t!==this.uiNode.value&&(this.uiNode.value=t)}}}nK.allowedChildren=[nV],nV.requiredContainer=nK,nV.allowedChildren=[nH,rU,rS,rO];let nW=(t,e,r)=>"string"==typeof t.versionString&&parseInt(t.versionString.split(".")[0],10)>=11?t.highlight(r,{language:e}).value:t.highlight(e,r).value;class nZ extends r3{static register(){iu.register(nH,!0),iu.register(nV,!0),iu.register(nK,!0)}constructor(t,e){if(super(t,e),null==this.options.hljs)throw Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((t,e)=>{let{key:r}=e;return t[r]=!0,t},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(iu.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof nK))return;let e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(t=>{let{key:r,label:i}=t,n=e.ownerDocument.createElement("option");n.textContent=i,n.setAttribute("value",r),e.appendChild(n)}),e.addEventListener("change",()=>{t.format(nV.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),null==t.uiNode&&(t.attachUI(e),t.children.head&&(e.value=nV.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(iu.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.quill.selection.composing)return;this.quill.update(iu.sources.USER);let r=this.quill.getSelection();(null==t?this.quill.scroll.descendants(nK):[t]).forEach(t=>{t.highlight(this.highlightBlot,e)}),this.quill.update(iu.sources.SILENT),null!=r&&this.quill.setSelection(r,iu.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"plain";if("plain"===(e=this.languages[e]?e:"plain"))return rj(t).split("\n").reduce((t,r,i)=>(0!==i&&t.insert("\n",{[iI.blotName]:e}),t.insert(r)),new e_);let r=this.quill.root.ownerDocument.createElement("div");return r.classList.add(iI.className),r.innerHTML=nW(this.options.hljs,e,t),nd(this.quill.scroll,r,[(t,e)=>{let r=n$.value(t);return r?e.compose(new e_().retain(e.length(),{[nH.blotName]:r})):e}],[(t,r)=>t.data.split("\n").reduce((t,r,i)=>(0!==i&&t.insert("\n",{[iI.blotName]:e}),t.insert(r)),r)],new WeakMap)}}nZ.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};class nG extends rI{static blotName="table";static tagName="TD";static create(t){let e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",nJ()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===nG.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}}class nX extends ix{static blotName="table-row";static tagName="TR";checkMerge(){if(super.checkMerge()&&null!=this.next.children.head){let t=this.children.head.formats(),e=this.children.tail.formats(),r=this.next.children.head.formats(),i=this.next.children.tail.formats();return t.table===e.table&&t.table===r.table&&t.table===i.table}return!1}optimize(t){super.optimize(t),this.children.forEach(t=>{if(null==t.next)return;let e=t.formats(),r=t.next.formats();if(e.table!==r.table){let e=this.splitAfter(t);e&&e.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}class nY extends ix{static blotName="table-body";static tagName="TBODY"}class nQ extends ix{static blotName="table-container";static tagName="TABLE";balanceCells(){let t=this.descendants(nX),e=t.reduce((t,e)=>Math.max(e.children.length,t),0);t.forEach(t=>{Array(e-t.children.length).fill(0).forEach(()=>{let e;null!=t.children.head&&(e=nG.formats(t.children.head.domNode));let r=this.scroll.create(nG.blotName,e);t.appendChild(r),r.optimize()})})}cells(t){return this.rows().map(e=>e.children.at(t))}deleteColumn(t){let[e]=this.descendant(nY);null!=e&&null!=e.children.head&&e.children.forEach(e=>{let r=e.children.at(t);null!=r&&r.remove()})}insertColumn(t){let[e]=this.descendant(nY);null!=e&&null!=e.children.head&&e.children.forEach(e=>{let r=e.children.at(t),i=nG.formats(e.children.head.domNode),n=this.scroll.create(nG.blotName,i);e.insertBefore(n,r)})}insertRow(t){let[e]=this.descendant(nY);if(null==e||null==e.children.head)return;let r=nJ(),i=this.scroll.create(nX.blotName);e.children.head.children.forEach(()=>{let t=this.scroll.create(nG.blotName,r);i.appendChild(t)});let n=e.children.at(t);e.insertBefore(i,n)}rows(){let t=this.children.head;return null==t?[]:t.children.map(t=>t)}}function nJ(){let t=Math.random().toString(36).slice(2,6);return`row-${t}`}nQ.allowedChildren=[nY],nY.requiredContainer=nQ,nY.allowedChildren=[nX],nX.requiredContainer=nY,nX.allowedChildren=[nG],nG.requiredContainer=nX;class n1 extends r3{static register(){iu.register(nG),iu.register(nX),iu.register(nY),iu.register(nQ)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(nQ).forEach(t=>{t.balanceCells()})}deleteColumn(){let[t,,e]=this.getTable();null!=e&&(t.deleteColumn(e.cellOffset()),this.quill.update(iu.sources.USER))}deleteRow(){let[,t]=this.getTable();null!=t&&(t.remove(),this.quill.update(iu.sources.USER))}deleteTable(){let[t]=this.getTable();if(null==t)return;let e=t.offset();t.remove(),this.quill.update(iu.sources.USER),this.quill.setSelection(e,iu.sources.SILENT)}getTable(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.quill.getSelection();if(null==t)return[null,null,null,-1];let[e,r]=this.quill.getLine(t.index);if(null==e||e.statics.blotName!==nG.blotName)return[null,null,null,-1];let i=e.parent;return[i.parent.parent,i,e,r]}insertColumn(t){let e=this.quill.getSelection();if(!e)return;let[r,i,n]=this.getTable(e);if(null==n)return;let s=n.cellOffset();r.insertColumn(s+t),this.quill.update(iu.sources.USER);let l=i.rowOffset();0===t&&(l+=1),this.quill.setSelection(e.index+l,e.length,iu.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){let e=this.quill.getSelection();if(!e)return;let[r,i,n]=this.getTable(e);if(null==n)return;let s=i.rowOffset();r.insertRow(s+t),this.quill.update(iu.sources.USER),t>0?this.quill.setSelection(e,iu.sources.SILENT):this.quill.setSelection(e.index+i.children.length,e.length,iu.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){let r=this.quill.getSelection();if(null==r)return;let i=Array(t).fill(0).reduce(t=>{let r=Array(e).fill("\n").join("");return t.insert(r,{table:nJ()})},new e_().retain(r.index));this.quill.updateContents(i,iu.sources.USER),this.quill.setSelection(r.index,iu.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(iu.events.SCROLL_OPTIMIZE,t=>{t.some(t=>!!["TD","TR","TBODY","TABLE"].includes(t.target.tagName)&&(this.quill.once(iu.events.TEXT_CHANGE,(t,e,r)=>{r===iu.sources.USER&&this.balanceTables()}),!0))})}}let n0=rV("quill:toolbar");class n2 extends r3{constructor(t,e){if(super(t,e),Array.isArray(this.options.container)){let e=document.createElement("div");e.setAttribute("role","toolbar"),function(t,e){Array.isArray(e[0])||(e=[e]),e.forEach(e=>{let r=document.createElement("span");r.classList.add("ql-formats"),e.forEach(t=>{if("string"==typeof t)n5(r,t);else{let e=Object.keys(t)[0],i=t[e];Array.isArray(i)?function(t,e,r){let i=document.createElement("select");i.classList.add(`ql-${e}`),r.forEach(t=>{let e=document.createElement("option");!1!==t?e.setAttribute("value",String(t)):e.setAttribute("selected","selected"),i.appendChild(e)}),t.appendChild(i)}(r,e,i):n5(r,e,i)}}),t.appendChild(r)})}(e,this.options.container),t.container?.parentNode?.insertBefore(e,t.container),this.container=e}else"string"==typeof this.options.container?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement))return void n0.error("Container required for toolbar",this.options);this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(t=>{let e=this.options.handlers?.[t];e&&this.addHandler(t,e)}),Array.from(this.container.querySelectorAll("button, select")).forEach(t=>{this.attach(t)}),this.quill.on(iu.events.EDITOR_CHANGE,()=>{let[t]=this.quill.selection.getRange();this.update(t)})}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find(t=>0===t.indexOf("ql-"));if(!e)return;if(e=e.slice(3),"BUTTON"===t.tagName&&t.setAttribute("type","button"),null==this.handlers[e]&&null==this.quill.scroll.query(e))return void n0.warn("ignoring attaching to nonexistent format",e,t);let r="SELECT"===t.tagName?"change":"click";t.addEventListener(r,r=>{let i;if("SELECT"===t.tagName){if(t.selectedIndex<0)return;let e=t.options[t.selectedIndex];i=!e.hasAttribute("selected")&&(e.value||!1)}else i=!t.classList.contains("ql-active")&&(t.value||!t.hasAttribute("value")),r.preventDefault();this.quill.focus();let[n]=this.quill.selection.getRange();if(null!=this.handlers[e])this.handlers[e].call(this,i);else if(this.quill.scroll.query(e).prototype instanceof eN){if(!(i=prompt(`Enter ${e}`)))return;this.quill.updateContents(new e_().retain(n.index).delete(n.length).insert({[e]:i}),iu.sources.USER)}else this.quill.format(e,i,iu.sources.USER);this.update(n)}),this.controls.push([e,t])}update(t){let e=null==t?{}:this.quill.getFormat(t);this.controls.forEach(r=>{let[i,n]=r;if("SELECT"===n.tagName){let r=null;if(null==t)r=null;else if(null==e[i])r=n.querySelector("option[selected]");else if(!Array.isArray(e[i])){let t=e[i];"string"==typeof t&&(t=t.replace(/"/g,'\\"')),r=n.querySelector(`option[value="${t}"]`)}null==r?(n.value="",n.selectedIndex=-1):r.selected=!0}else if(null==t)n.classList.remove("ql-active"),n.setAttribute("aria-pressed","false");else if(n.hasAttribute("value")){let t=e[i],r=t===n.getAttribute("value")||null!=t&&t.toString()===n.getAttribute("value")||null==t&&!n.getAttribute("value");n.classList.toggle("ql-active",r),n.setAttribute("aria-pressed",r.toString())}else{let t=null!=e[i];n.classList.toggle("ql-active",t),n.setAttribute("aria-pressed",t.toString())}})}}function n5(t,e,r){let i=document.createElement("button");i.setAttribute("type","button"),i.classList.add(`ql-${e}`),i.setAttribute("aria-pressed","false"),null!=r?(i.value=r,i.setAttribute("aria-label",`${e}: ${r}`)):i.setAttribute("aria-label",e),t.appendChild(i)}n2.DEFAULTS={},n2.DEFAULTS={container:null,handlers:{clean(){let t=this.quill.getSelection();null!=t&&(0===t.length?Object.keys(this.quill.getFormat()).forEach(t=>{null!=this.quill.scroll.query(t,t5.INLINE)&&this.quill.format(t,!1,iu.sources.USER)}):this.quill.removeFormat(t.index,t.length,iu.sources.USER))},direction(t){let{align:e}=this.quill.getFormat();"rtl"===t&&null==e?this.quill.format("align","right",iu.sources.USER):t||"right"!==e||this.quill.format("align",!1,iu.sources.USER),this.quill.format("direction",t,iu.sources.USER)},indent(t){let e=this.quill.getSelection(),r=this.quill.getFormat(e),i=parseInt(r.indent||0,10);if("+1"===t||"-1"===t){let e="+1"===t?1:-1;"rtl"===r.direction&&(e*=-1),this.quill.format("indent",i+e,iu.sources.USER)}},link(t){!0===t&&(t=prompt("Enter link URL:")),this.quill.format("link",t,iu.sources.USER)},list(t){let e=this.quill.getSelection(),r=this.quill.getFormat(e);"check"===t?"checked"===r.list||"unchecked"===r.list?this.quill.format("list",!1,iu.sources.USER):this.quill.format("list","unchecked",iu.sources.USER):this.quill.format("list",t,iu.sources.USER)}}};let n4='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',n3={align:{"":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',center:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',right:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',justify:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>'},background:'<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',blockquote:'<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',bold:'<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',clean:'<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',code:n4,"code-block":n4,color:'<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',direction:{"":'<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',rtl:'<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>'},formula:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',header:{1:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',2:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',3:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',4:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',5:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',6:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>'},italic:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',image:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',indent:{"+1":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',"-1":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>'},link:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',list:{bullet:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',check:'<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',ordered:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>'},script:{sub:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',super:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>'},strike:'<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',table:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',underline:'<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',video:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>'},n6=0;function n8(t,e){t.setAttribute(e,`${"true"!==t.getAttribute(e)}`)}class n9{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",t=>{switch(t.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),t.preventDefault()}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),n8(this.label,"aria-expanded"),n8(this.options,"aria-hidden")}buildItem(t){let e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");let r=t.getAttribute("value");return r&&e.setAttribute("data-value",r),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",t=>{switch(t.key){case"Enter":this.selectItem(e,!0),t.preventDefault();break;case"Escape":this.escape(),t.preventDefault()}}),e}buildLabel(){let t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>',t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){let t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${n6}`,n6+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{let r=this.buildItem(e);t.appendChild(r),!0===e.selected&&this.selectItem(r)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.container.querySelector(".ql-selected");t!==r&&(null!=r&&r.classList.remove("ql-selected"),null!=t&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){let e=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(e)}else this.selectItem(null);let e=null!=t&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}}let n7=n9;class st extends n7{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(t=>{t.classList.add("ql-primary")})}buildItem(t){let e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);let r=this.label.querySelector(".ql-color-label"),i=t&&t.getAttribute("data-value")||"";r&&("line"===r.tagName?r.style.stroke=i:r.style.fill=i)}}class se extends n7{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(t=>{t.innerHTML=e[t.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);let r=t||this.defaultItem;if(null!=r){if(this.label.innerHTML===r.innerHTML)return;this.label.innerHTML=r.innerHTML}}}let sr=t=>{let{overflowY:e}=getComputedStyle(t,null);return"visible"!==e&&"clip"!==e};class si{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,sr(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){let e=t.left+t.width/2-this.root.offsetWidth/2,r=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${r}px`,this.root.classList.remove("ql-flip");let i=this.boundsContainer.getBoundingClientRect(),n=this.root.getBoundingClientRect(),s=0;if(n.right>i.right&&(s=i.right-n.right,this.root.style.left=`${e+s}px`),n.left<i.left&&(s=i.left-n.left,this.root.style.left=`${e+s}px`),n.bottom>i.bottom){let e=n.bottom-n.top,i=t.bottom-t.top+e;this.root.style.top=`${r-i}px`,this.root.classList.add("ql-flip")}return s}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}let sn=si,ss=[!1,"center","right","justify"],sl=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],so=[!1,"serif","monospace"],sa=["1","2","3",!1],sc=["small",!1,"large","huge"];class sh extends it{constructor(t,e){super(t,e);let r=e=>{if(!document.body.contains(t.root))return void document.body.removeEventListener("click",r);null==this.tooltip||this.tooltip.root.contains(e.target)||document.activeElement===this.tooltip.textbox||this.quill.hasFocus()||this.tooltip.hide(),null!=this.pickers&&this.pickers.forEach(t=>{t.container.contains(e.target)||t.close()})};t.emitter.listenDOM("click",document.body,r)}addModule(t){let e=super.addModule(t);return"toolbar"===t&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(t=>{(t.getAttribute("class")||"").split(/\s+/).forEach(r=>{if(r.startsWith("ql-")&&null!=e[r=r.slice(3)])if("direction"===r)t.innerHTML=e[r][""]+e[r].rtl;else if("string"==typeof e[r])t.innerHTML=e[r];else{let i=t.value||"";null!=i&&e[r][i]&&(t.innerHTML=e[r][i])}})})}buildPickers(t,e){this.pickers=Array.from(t).map(t=>{if(t.classList.contains("ql-align")&&(null==t.querySelector("option")&&sd(t,ss),"object"==typeof e.align))return new se(t,e.align);if(t.classList.contains("ql-background")||t.classList.contains("ql-color")){let r=t.classList.contains("ql-background")?"background":"color";return null==t.querySelector("option")&&sd(t,sl,"background"===r?"#ffffff":"#000000"),new st(t,e[r])}return null==t.querySelector("option")&&(t.classList.contains("ql-font")?sd(t,so):t.classList.contains("ql-header")?sd(t,sa):t.classList.contains("ql-size")&&sd(t,sc)),new n7(t)}),this.quill.on(rW.events.EDITOR_CHANGE,()=>{this.pickers.forEach(t=>{t.update()})})}}sh.DEFAULTS=t2({},it.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let t=this.container.querySelector("input.ql-image[type=file]");null==t&&((t=document.createElement("input")).setAttribute("type","file"),t.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),t.classList.add("ql-image"),t.addEventListener("change",()=>{let e=this.quill.getSelection(!0);this.quill.uploader.upload(e,t.files),t.value=""}),this.container.appendChild(t)),t.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class su extends sn{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{"Enter"===t.key?(this.save(),t.preventDefault()):"Escape"===t.key&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"link",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null==this.textbox)return;null!=e?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");let r=this.quill.getBounds(this.quill.selection.savedRange);null!=r&&this.position(r),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{let{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,rW.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,rW.sources.USER)),this.quill.root.scrollTop=e;break}case"video":var e;let r;t=(r=(e=t).match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||e.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/))?`${r[1]||"https"}://www.youtube.com/embed/${r[2]}?showinfo=0`:(r=e.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${r[1]||"https"}://player.vimeo.com/video/${r[2]}/`:e;case"formula":{if(!t)break;let e=this.quill.getSelection(!0);if(null!=e){let r=e.index+e.length;this.quill.insertEmbed(r,this.root.getAttribute("data-mode"),t,rW.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(r+1," ",rW.sources.USER),this.quill.setSelection(r+2,rW.sources.USER)}}}this.textbox.value="",this.hide()}}function sd(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.forEach(e=>{let i=document.createElement("option");e===r?i.setAttribute("selected","selected"):i.setAttribute("value",String(e)),t.appendChild(i)})}let sf=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class sp extends su{static TEMPLATE='<span class="ql-tooltip-arrow"></span><div class="ql-tooltip-editor"><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-close"></a></div>';constructor(t,e){super(t,e),this.quill.on(rW.events.EDITOR_CHANGE,(t,e,r,i)=>{if(t===rW.events.SELECTION_CHANGE)if(null!=e&&e.length>0&&i===rW.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;let t=this.quill.getLines(e.index,e.length);if(1===t.length){let t=this.quill.getBounds(e);null!=t&&this.position(t)}else{let r=t[t.length-1],i=this.quill.getIndex(r),n=Math.min(r.length()-1,e.index+e.length-i),s=this.quill.getBounds(new rG(i,n));null!=s&&this.position(s)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(rW.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;let t=this.quill.getSelection();if(null!=t){let e=this.quill.getBounds(t);null!=e&&this.position(e)}},1)})}cancel(){this.show()}position(t){let e=super.position(t),r=this.root.querySelector(".ql-tooltip-arrow");return r.style.marginLeft="",0!==e&&(r.style.marginLeft=`${-1*e-r.offsetWidth/2}px`),e}}class sg extends sh{constructor(t,e){null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=sf),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new sp(this.quill,this.options.bounds),null!=t.container&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),n3),this.buildPickers(t.container.querySelectorAll("select"),n3))}}sg.DEFAULTS=t2({},sh.DEFAULTS,{modules:{toolbar:{handlers:{link(t){t?this.quill.theme.tooltip.edit():this.quill.format("link",!1,iu.sources.USER)}}}}});let sm=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class sb extends su{static TEMPLATE='<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-action"></a><a class="ql-remove"></a>';preview=this.root.querySelector("a.ql-preview");listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",t=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),t.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",t=>{if(null!=this.linkRange){let t=this.linkRange;this.restoreFocus(),this.quill.formatText(t,"link",!1,rW.sources.USER),delete this.linkRange}t.preventDefault(),this.hide()}),this.quill.on(rW.events.SELECTION_CHANGE,(t,e,r)=>{if(null!=t){if(0===t.length&&r===rW.sources.USER){let[e,r]=this.quill.scroll.descendant(nC,t.index);if(null!=e){this.linkRange=new rG(t.index-r,e.length());let i=nC.formats(e.domNode);this.preview.textContent=i,this.preview.setAttribute("href",i),this.show();let n=this.quill.getBounds(this.linkRange);null!=n&&this.position(n);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}class sy extends sh{constructor(t,e){null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=sm),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){null!=t.container&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),n3),this.buildPickers(t.container.querySelectorAll("select"),n3),this.tooltip=new sb(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,r)=>{t.handlers.link.call(t,!r.format.link)}))}}sy.DEFAULTS=t2({},sh.DEFAULTS,{modules:{toolbar:{handlers:{link(t){if(t){let t=this.quill.getSelection();if(null==t||0===t.length)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&0!==e.indexOf("mailto:")&&(e=`mailto:${e}`);let{tooltip:r}=this.quill.theme;r.edit("link",e)}else this.quill.format("link",!1,iu.sources.USER)}}}}}),iu.register({"attributors/attribute/direction":iD,"attributors/class/align":i_,"attributors/class/background":ij,"attributors/class/color":iS,"attributors/class/direction":iU,"attributors/class/font":iF,"attributors/class/size":iV,"attributors/style/align":iL,"attributors/style/background":iC,"attributors/style/color":iT,"attributors/style/direction":iP,"attributors/style/font":iH,"attributors/style/size":iK},!0),iu.register({"formats/align":i_,"formats/direction":iU,"formats/indent":nq,"formats/background":iC,"formats/color":iT,"formats/font":iF,"formats/size":iV,"formats/blockquote":nk,"formats/code-block":iI,"formats/header":n_,"formats/list":nO,"formats/bold":nT,"formats/code":iM,"formats/italic":nj,"formats/link":nC,"formats/script":nI,"formats/strike":nM,"formats/underline":nB,"formats/formula":nD,"formats/image":nP,"formats/video":nF,"modules/syntax":nZ,"modules/table":n1,"modules/toolbar":n2,"themes/bubble":sg,"themes/snow":sy,"ui/icons":n3,"ui/picker":n7,"ui/icon-picker":se,"ui/color-picker":st,"ui/tooltip":sn},!0);let sv=iu},20100:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});let i=r(55110);class n{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);let e=this.ops[this.index];if(!e)return{retain:1/0};{let r=this.offset,n=i.default.length(e);if(t>=n-r?(t=n-r,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};{let i={};return e.attributes&&(i.attributes=e.attributes),"number"==typeof e.retain?i.retain=t:"object"==typeof e.retain&&null!==e.retain?i.retain=e.retain:"string"==typeof e.insert?i.insert=e.insert.substr(r,t):i.insert=e.insert,i}}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?i.default.length(this.ops[this.index])-this.offset:1/0}peekType(){let t=this.ops[this.index];if(t){if("number"==typeof t.delete)return"delete";else if("number"!=typeof t.retain&&("object"!=typeof t.retain||null===t.retain))return"insert"}return"retain"}rest(){if(!this.hasNext())return[];{if(0===this.offset)return this.ops.slice(this.index);let t=this.offset,e=this.index,r=this.next(),i=this.ops.slice(this.index);return this.offset=t,this.index=e,[r].concat(i)}}}e.default=n},46120:(t,e,r)=>{t=r.nmd(t);var i="__lodash_hash_undefined__",n="[object Arguments]",s="[object Boolean]",l="[object Date]",o="[object Function]",a="[object GeneratorFunction]",c="[object Map]",h="[object Number]",u="[object Object]",d="[object Promise]",f="[object RegExp]",p="[object Set]",g="[object String]",m="[object Symbol]",b="[object WeakMap]",y="[object ArrayBuffer]",v="[object DataView]",x="[object Float32Array]",N="[object Float64Array]",E="[object Int8Array]",A="[object Int16Array]",w="[object Int32Array]",q="[object Uint8Array]",k="[object Uint8ClampedArray]",_="[object Uint16Array]",L="[object Uint32Array]",O=/\w*$/,S=/^\[object .+?Constructor\]$/,T=/^(?:0|[1-9]\d*)$/,j={};j[n]=j["[object Array]"]=j[y]=j[v]=j[s]=j[l]=j[x]=j[N]=j[E]=j[A]=j[w]=j[c]=j[h]=j[u]=j[f]=j[p]=j[g]=j[m]=j[q]=j[k]=j[_]=j[L]=!0,j["[object Error]"]=j[o]=j[b]=!1;var C="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,R="object"==typeof self&&self&&self.Object===Object&&self,I=C||R||Function("return this")(),M=e&&!e.nodeType&&e,B=M&&t&&!t.nodeType&&t,D=B&&B.exports===M;function U(t,e){return t.set(e[0],e[1]),t}function P(t,e){return t.add(e),t}function z(t,e,r,i){var n=-1,s=t?t.length:0;for(i&&s&&(r=t[++n]);++n<s;)r=e(r,t[n],n,t);return r}function F(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function $(t){var e=-1,r=Array(t.size);return t.forEach(function(t,i){r[++e]=[i,t]}),r}function H(t,e){return function(r){return t(e(r))}}function V(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}var K=Array.prototype,W=Function.prototype,Z=Object.prototype,G=I["__core-js_shared__"],X=function(){var t=/[^.]+$/.exec(G&&G.keys&&G.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),Y=W.toString,Q=Z.hasOwnProperty,J=Z.toString,tt=RegExp("^"+Y.call(Q).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),te=D?I.Buffer:void 0,tr=I.Symbol,ti=I.Uint8Array,tn=H(Object.getPrototypeOf,Object),ts=Object.create,tl=Z.propertyIsEnumerable,to=K.splice,ta=Object.getOwnPropertySymbols,tc=te?te.isBuffer:void 0,th=H(Object.keys,Object),tu=tC(I,"DataView"),td=tC(I,"Map"),tf=tC(I,"Promise"),tp=tC(I,"Set"),tg=tC(I,"WeakMap"),tm=tC(Object,"create"),tb=tB(tu),ty=tB(td),tv=tB(tf),tx=tB(tp),tN=tB(tg),tE=tr?tr.prototype:void 0,tA=tE?tE.valueOf:void 0;function tw(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function tq(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function tk(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function t_(t){this.__data__=new tq(t)}function tL(t,e,r){var i=t[e];Q.call(t,e)&&tD(i,r)&&(void 0!==r||e in t)||(t[e]=r)}function tO(t,e){for(var r=t.length;r--;)if(tD(t[r][0],e))return r;return -1}function tS(t){var e=new t.constructor(t.byteLength);return new ti(e).set(new ti(t)),e}function tT(t,e,r,i){r||(r={});for(var n=-1,s=e.length;++n<s;){var l=e[n],o=i?i(r[l],t[l],l,r,t):void 0;tL(r,l,void 0===o?t[l]:o)}return r}function tj(t,e){var r,i,n=t.__data__;return("string"==(i=typeof(r=e))||"number"==i||"symbol"==i||"boolean"==i?"__proto__"!==r:null===r)?n["string"==typeof e?"string":"hash"]:n.map}function tC(t,e){var r,i=null==t?void 0:t[e];return!(!t$(i)||(r=i,X&&X in r))&&(tF(i)||F(i)?tt:S).test(tB(i))?i:void 0}tw.prototype.clear=function(){this.__data__=tm?tm(null):{}},tw.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},tw.prototype.get=function(t){var e=this.__data__;if(tm){var r=e[t];return r===i?void 0:r}return Q.call(e,t)?e[t]:void 0},tw.prototype.has=function(t){var e=this.__data__;return tm?void 0!==e[t]:Q.call(e,t)},tw.prototype.set=function(t,e){return this.__data__[t]=tm&&void 0===e?i:e,this},tq.prototype.clear=function(){this.__data__=[]},tq.prototype.delete=function(t){var e=this.__data__,r=tO(e,t);return!(r<0)&&(r==e.length-1?e.pop():to.call(e,r,1),!0)},tq.prototype.get=function(t){var e=this.__data__,r=tO(e,t);return r<0?void 0:e[r][1]},tq.prototype.has=function(t){return tO(this.__data__,t)>-1},tq.prototype.set=function(t,e){var r=this.__data__,i=tO(r,t);return i<0?r.push([t,e]):r[i][1]=e,this},tk.prototype.clear=function(){this.__data__={hash:new tw,map:new(td||tq),string:new tw}},tk.prototype.delete=function(t){return tj(this,t).delete(t)},tk.prototype.get=function(t){return tj(this,t).get(t)},tk.prototype.has=function(t){return tj(this,t).has(t)},tk.prototype.set=function(t,e){return tj(this,t).set(t,e),this},t_.prototype.clear=function(){this.__data__=new tq},t_.prototype.delete=function(t){return this.__data__.delete(t)},t_.prototype.get=function(t){return this.__data__.get(t)},t_.prototype.has=function(t){return this.__data__.has(t)},t_.prototype.set=function(t,e){var r=this.__data__;if(r instanceof tq){var i=r.__data__;if(!td||i.length<199)return i.push([t,e]),this;r=this.__data__=new tk(i)}return r.set(t,e),this};var tR=ta?H(ta,Object):function(){return[]},tI=function(t){return J.call(t)};function tM(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Z)}function tB(t){if(null!=t){try{return Y.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function tD(t,e){return t===e||t!=t&&e!=e}(tu&&tI(new tu(new ArrayBuffer(1)))!=v||td&&tI(new td)!=c||tf&&tI(tf.resolve())!=d||tp&&tI(new tp)!=p||tg&&tI(new tg)!=b)&&(tI=function(t){var e=J.call(t),r=e==u?t.constructor:void 0,i=r?tB(r):void 0;if(i)switch(i){case tb:return v;case ty:return c;case tv:return d;case tx:return p;case tN:return b}return e});var tU=Array.isArray;function tP(t){var e;return null!=t&&"number"==typeof(e=t.length)&&e>-1&&e%1==0&&e<=0x1fffffffffffff&&!tF(t)}var tz=tc||function(){return!1};function tF(t){var e=t$(t)?J.call(t):"";return e==o||e==a}function t$(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function tH(t){return tP(t)?function(t,e){var r,i,s,l,o,a=tU(t)||(s=i=r=t)&&"object"==typeof s&&tP(i)&&Q.call(r,"callee")&&(!tl.call(r,"callee")||J.call(r)==n)?function(t,e){for(var r=-1,i=Array(t);++r<t;)i[r]=e(r);return i}(t.length,String):[],c=a.length,h=!!c;for(var u in t){Q.call(t,u)&&!(h&&("length"==u||(l=u,(o=null==(o=c)?0x1fffffffffffff:o)&&("number"==typeof l||T.test(l))&&l>-1&&l%1==0&&l<o)))&&a.push(u)}return a}(t):function(t){if(!tM(t))return th(t);var e=[];for(var r in Object(t))Q.call(t,r)&&"constructor"!=r&&e.push(r);return e}(t)}t.exports=function(t){return function t(e,r,i,d,b,S,T){if(d&&(C=S?d(e,b,S,T):d(e)),void 0!==C)return C;if(!t$(e))return e;var C,R=tU(e);if(R){if(M=(I=e).length,B=I.constructor(M),M&&"string"==typeof I[0]&&Q.call(I,"index")&&(B.index=I.index,B.input=I.input),C=B,!r){var I,M,B,D=e,H=C,K=-1,W=D.length;for(H||(H=Array(W));++K<W;)H[K]=D[K];return H}}else{var Z,G,X,Y,J,tt=tI(e),te=tt==o||tt==a;if(tz(e)){var tr=e,ti=r;if(ti)return tr.slice();var tl=new tr.constructor(tr.length);return tr.copy(tl),tl}if(tt==u||tt==n||te&&!S){if(F(e))return S?e:{};if(C="function"!=typeof(Z=te?{}:e).constructor||tM(Z)?{}:t$(G=tn(Z))?ts(G):{},!r){return X=e,Y=(J=C)&&tT(e,tH(e),J),tT(X,tR(X),Y)}}else{if(!j[tt])return S?e:{};C=function(t,e,r,i){var n,o,a,u=t.constructor;switch(e){case y:return tS(t);case s:case l:return new u(+t);case v:return n=i?tS(t.buffer):t.buffer,new t.constructor(n,t.byteOffset,t.byteLength);case x:case N:case E:case A:case w:case q:case k:case _:case L:return o=i?tS(t.buffer):t.buffer,new t.constructor(o,t.byteOffset,t.length);case c:return z(i?r($(t),!0):$(t),U,new t.constructor);case h:case g:return new u(t);case f:return(a=new t.constructor(t.source,O.exec(t))).lastIndex=t.lastIndex,a;case p:return z(i?r(V(t),!0):V(t),P,new t.constructor);case m:return tA?Object(tA.call(t)):{}}}(e,tt,t,r)}}T||(T=new t_);var to=T.get(e);if(to)return to;if(T.set(e,C),!R)var ta=i?function(t){var e;return e=tH(t),tU(t)?e:function(t,e){for(var r=-1,i=e.length,n=t.length;++r<i;)t[n+r]=e[r];return t}(e,tR(t))}(e):tH(e);return!function(t,e){for(var r=-1,i=t?t.length:0;++r<i&&!1!==e(t[r],r,t););}(ta||e,function(n,s){ta&&(n=e[s=n]),tL(C,s,t(n,r,i,d,s,e,T))}),C}(t,!0,!0)}},49231:(t,e,r)=>{t=r.nmd(t);var i,n,s="__lodash_hash_undefined__",l="[object Arguments]",o="[object Array]",a="[object Boolean]",c="[object Date]",h="[object Error]",u="[object Function]",d="[object Map]",f="[object Number]",p="[object Object]",g="[object Promise]",m="[object RegExp]",b="[object Set]",y="[object String]",v="[object WeakMap]",x="[object ArrayBuffer]",N="[object DataView]",E=/^\[object .+?Constructor\]$/,A=/^(?:0|[1-9]\d*)$/,w={};w["[object Float32Array]"]=w["[object Float64Array]"]=w["[object Int8Array]"]=w["[object Int16Array]"]=w["[object Int32Array]"]=w["[object Uint8Array]"]=w["[object Uint8ClampedArray]"]=w["[object Uint16Array]"]=w["[object Uint32Array]"]=!0,w[l]=w[o]=w[x]=w[a]=w[N]=w[c]=w[h]=w[u]=w[d]=w[f]=w[p]=w[m]=w[b]=w[y]=w[v]=!1;var q="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,k="object"==typeof self&&self&&self.Object===Object&&self,_=q||k||Function("return this")(),L=e&&!e.nodeType&&e,O=L&&t&&!t.nodeType&&t,S=O&&O.exports===L,T=S&&q.process,j=function(){try{return T&&T.binding&&T.binding("util")}catch(t){}}(),C=j&&j.isTypedArray;function R(t){var e=-1,r=Array(t.size);return t.forEach(function(t,i){r[++e]=[i,t]}),r}function I(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}var M=Array.prototype,B=Function.prototype,D=Object.prototype,U=_["__core-js_shared__"],P=B.toString,z=D.hasOwnProperty,F=function(){var t=/[^.]+$/.exec(U&&U.keys&&U.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),$=D.toString,H=RegExp("^"+P.call(z).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),V=S?_.Buffer:void 0,K=_.Symbol,W=_.Uint8Array,Z=D.propertyIsEnumerable,G=M.splice,X=K?K.toStringTag:void 0,Y=Object.getOwnPropertySymbols,Q=V?V.isBuffer:void 0,J=(i=Object.keys,n=Object,function(t){return i(n(t))}),tt=tw(_,"DataView"),te=tw(_,"Map"),tr=tw(_,"Promise"),ti=tw(_,"Set"),tn=tw(_,"WeakMap"),ts=tw(Object,"create"),tl=t_(tt),to=t_(te),ta=t_(tr),tc=t_(ti),th=t_(tn),tu=K?K.prototype:void 0,td=tu?tu.valueOf:void 0;function tf(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function tp(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function tg(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function tm(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new tg;++e<r;)this.add(t[e])}function tb(t){var e=this.__data__=new tp(t);this.size=e.size}function ty(t,e){for(var r=t.length;r--;)if(tL(t[r][0],e))return r;return -1}function tv(t){var e;return null==t?void 0===t?"[object Undefined]":"[object Null]":X&&X in Object(t)?function(t){var e=z.call(t,X),r=t[X];try{t[X]=void 0;var i=!0}catch(t){}var n=$.call(t);return i&&(e?t[X]=r:delete t[X]),n}(t):(e=t,$.call(e))}function tx(t){return tI(t)&&tv(t)==l}function tN(t,e,r,i,n,s){var l=1&r,o=t.length,a=e.length;if(o!=a&&!(l&&a>o))return!1;var c=s.get(t);if(c&&s.get(e))return c==e;var h=-1,u=!0,d=2&r?new tm:void 0;for(s.set(t,e),s.set(e,t);++h<o;){var f=t[h],p=e[h];if(i)var g=l?i(p,f,h,e,t,s):i(f,p,h,t,e,s);if(void 0!==g){if(g)continue;u=!1;break}if(d){if(!function(t,e){for(var r=-1,i=null==t?0:t.length;++r<i;)if(e(t[r],r,t))return!0;return!1}(e,function(t,e){if(!d.has(e)&&(f===t||n(f,t,r,i,s)))return d.push(e)})){u=!1;break}}else if(!(f===p||n(f,p,r,i,s))){u=!1;break}}return s.delete(t),s.delete(e),u}function tE(t){var e;return e=tB(t),tS(t)?e:function(t,e){for(var r=-1,i=e.length,n=t.length;++r<i;)t[n+r]=e[r];return t}(e,tq(t))}function tA(t,e){var r,i,n=t.__data__;return("string"==(i=typeof(r=e))||"number"==i||"symbol"==i||"boolean"==i?"__proto__"!==r:null===r)?n["string"==typeof e?"string":"hash"]:n.map}function tw(t,e){var r,i=null==t?void 0:t[e];return!(!tR(i)||(r=i,F&&F in r))&&(tj(i)?H:E).test(t_(i))?i:void 0}tf.prototype.clear=function(){this.__data__=ts?ts(null):{},this.size=0},tf.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e},tf.prototype.get=function(t){var e=this.__data__;if(ts){var r=e[t];return r===s?void 0:r}return z.call(e,t)?e[t]:void 0},tf.prototype.has=function(t){var e=this.__data__;return ts?void 0!==e[t]:z.call(e,t)},tf.prototype.set=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=ts&&void 0===e?s:e,this},tp.prototype.clear=function(){this.__data__=[],this.size=0},tp.prototype.delete=function(t){var e=this.__data__,r=ty(e,t);return!(r<0)&&(r==e.length-1?e.pop():G.call(e,r,1),--this.size,!0)},tp.prototype.get=function(t){var e=this.__data__,r=ty(e,t);return r<0?void 0:e[r][1]},tp.prototype.has=function(t){return ty(this.__data__,t)>-1},tp.prototype.set=function(t,e){var r=this.__data__,i=ty(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this},tg.prototype.clear=function(){this.size=0,this.__data__={hash:new tf,map:new(te||tp),string:new tf}},tg.prototype.delete=function(t){var e=tA(this,t).delete(t);return this.size-=!!e,e},tg.prototype.get=function(t){return tA(this,t).get(t)},tg.prototype.has=function(t){return tA(this,t).has(t)},tg.prototype.set=function(t,e){var r=tA(this,t),i=r.size;return r.set(t,e),this.size+=+(r.size!=i),this},tm.prototype.add=tm.prototype.push=function(t){return this.__data__.set(t,s),this},tm.prototype.has=function(t){return this.__data__.has(t)},tb.prototype.clear=function(){this.__data__=new tp,this.size=0},tb.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},tb.prototype.get=function(t){return this.__data__.get(t)},tb.prototype.has=function(t){return this.__data__.has(t)},tb.prototype.set=function(t,e){var r=this.__data__;if(r instanceof tp){var i=r.__data__;if(!te||i.length<199)return i.push([t,e]),this.size=++r.size,this;r=this.__data__=new tg(i)}return r.set(t,e),this.size=r.size,this};var tq=Y?function(t){return null==t?[]:function(t,e){for(var r=-1,i=null==t?0:t.length,n=0,s=[];++r<i;){var l=t[r];e(l,r,t)&&(s[n++]=l)}return s}(Y(t=Object(t)),function(e){return Z.call(t,e)})}:function(){return[]},tk=tv;function t_(t){if(null!=t){try{return P.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function tL(t,e){return t===e||t!=t&&e!=e}(tt&&tk(new tt(new ArrayBuffer(1)))!=N||te&&tk(new te)!=d||tr&&tk(tr.resolve())!=g||ti&&tk(new ti)!=b||tn&&tk(new tn)!=v)&&(tk=function(t){var e=tv(t),r=e==p?t.constructor:void 0,i=r?t_(r):"";if(i)switch(i){case tl:return N;case to:return d;case ta:return g;case tc:return b;case th:return v}return e});var tO=tx(function(){return arguments}())?tx:function(t){return tI(t)&&z.call(t,"callee")&&!Z.call(t,"callee")},tS=Array.isArray,tT=Q||function(){return!1};function tj(t){if(!tR(t))return!1;var e=tv(t);return e==u||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}function tC(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}function tR(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function tI(t){return null!=t&&"object"==typeof t}var tM=C?function(t){return C(t)}:function(t){return tI(t)&&tC(t.length)&&!!w[tv(t)]};function tB(t){return null!=t&&tC(t.length)&&!tj(t)?function(t,e){var r,i,n=tS(t),s=!n&&tO(t),l=!n&&!s&&tT(t),o=!n&&!s&&!l&&tM(t),a=n||s||l||o,c=a?function(t,e){for(var r=-1,i=Array(t);++r<t;)i[r]=e(r);return i}(t.length,String):[],h=c.length;for(var u in t){z.call(t,u)&&!(a&&("length"==u||l&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||(r=u,(i=null==(i=h)?0x1fffffffffffff:i)&&("number"==typeof r||A.test(r))&&r>-1&&r%1==0&&r<i)))&&c.push(u)}return c}(t):function(t){if(r=(e=t)&&e.constructor,e!==("function"==typeof r&&r.prototype||D))return J(t);var e,r,i=[];for(var n in Object(t))z.call(t,n)&&"constructor"!=n&&i.push(n);return i}(t)}t.exports=function(t,e){return function t(e,r,i,n,s){return e===r||(null!=e&&null!=r&&(tI(e)||tI(r))?function(t,e,r,i,n,s){var u=tS(t),g=tS(e),v=u?o:tk(t),E=g?o:tk(e);v=v==l?p:v,E=E==l?p:E;var A=v==p,w=E==p,q=v==E;if(q&&tT(t)){if(!tT(e))return!1;u=!0,A=!1}if(q&&!A)return s||(s=new tb),u||tM(t)?tN(t,e,r,i,n,s):function(t,e,r,i,n,s,l){switch(r){case N:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case x:if(t.byteLength!=e.byteLength||!s(new W(t),new W(e)))break;return!0;case a:case c:case f:return tL(+t,+e);case h:return t.name==e.name&&t.message==e.message;case m:case y:return t==e+"";case d:var o=R;case b:var u=1&i;if(o||(o=I),t.size!=e.size&&!u)break;var p=l.get(t);if(p)return p==e;i|=2,l.set(t,e);var g=tN(o(t),o(e),i,n,s,l);return l.delete(t),g;case"[object Symbol]":if(td)return td.call(t)==td.call(e)}return!1}(t,e,v,r,i,n,s);if(!(1&r)){var k=A&&z.call(t,"__wrapped__"),_=w&&z.call(e,"__wrapped__");if(k||_){var L=k?t.value():t,O=_?e.value():e;return s||(s=new tb),n(L,O,r,i,s)}}return!!q&&(s||(s=new tb),function(t,e,r,i,n,s){var l=1&r,o=tE(t),a=o.length;if(a!=tE(e).length&&!l)return!1;for(var c=a;c--;){var h=o[c];if(!(l?h in e:z.call(e,h)))return!1}var u=s.get(t);if(u&&s.get(e))return u==e;var d=!0;s.set(t,e),s.set(e,t);for(var f=l;++c<a;){var p=t[h=o[c]],g=e[h];if(i)var m=l?i(g,p,h,e,t,s):i(p,g,h,t,e,s);if(!(void 0===m?p===g||n(p,g,r,i,s):m)){d=!1;break}f||(f="constructor"==h)}if(d&&!f){var b=t.constructor,y=e.constructor;b!=y&&"constructor"in t&&"constructor"in e&&!("function"==typeof b&&b instanceof b&&"function"==typeof y&&y instanceof y)&&(d=!1)}return s.delete(t),s.delete(e),d}(t,e,r,i,n,s))}(e,r,i,n,t,s):e!=e&&r!=r)}(t,e)}},49882:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AttributeMap=e.OpIterator=e.Op=void 0;let i=r(87311),n=r(46120),s=r(49231),l=r(1763);e.AttributeMap=l.default;let o=r(55110);e.Op=o.default;let a=r(20100);e.OpIterator=a.default;let c=(t,e)=>{if("object"!=typeof t||null===t)throw Error(`cannot retain a ${typeof t}`);if("object"!=typeof e||null===e)throw Error(`cannot retain a ${typeof e}`);let r=Object.keys(t)[0];if(!r||r!==Object.keys(e)[0])throw Error(`embed types not matched: ${r} != ${Object.keys(e)[0]}`);return[r,t[r],e[r]]};class h{constructor(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}static registerEmbed(t,e){this.handlers[t]=e}static unregisterEmbed(t){delete this.handlers[t]}static getHandler(t){let e=this.handlers[t];if(!e)throw Error(`no handlers for embed type "${t}"`);return e}insert(t,e){let r={};return"string"==typeof t&&0===t.length?this:(r.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(r.attributes=e),this.push(r))}delete(t){return t<=0?this:this.push({delete:t})}retain(t,e){if("number"==typeof t&&t<=0)return this;let r={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(r.attributes=e),this.push(r)}push(t){let e=this.ops.length,r=this.ops[e-1];if(t=n(t),"object"==typeof r){if("number"==typeof t.delete&&"number"==typeof r.delete)return this.ops[e-1]={delete:r.delete+t.delete},this;if("number"==typeof r.delete&&null!=t.insert&&(e-=1,"object"!=typeof(r=this.ops[e-1])))return this.ops.unshift(t),this;if(s(t.attributes,r.attributes)){if("string"==typeof t.insert&&"string"==typeof r.insert)return this.ops[e-1]={insert:r.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;else if("number"==typeof t.retain&&"number"==typeof r.retain)return this.ops[e-1]={retain:r.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this}chop(){let t=this.ops[this.ops.length-1];return t&&"number"==typeof t.retain&&!t.attributes&&this.ops.pop(),this}filter(t){return this.ops.filter(t)}forEach(t){this.ops.forEach(t)}map(t){return this.ops.map(t)}partition(t){let e=[],r=[];return this.forEach(i=>{(t(i)?e:r).push(i)}),[e,r]}reduce(t,e){return this.ops.reduce(t,e)}changeLength(){return this.reduce((t,e)=>e.insert?t+o.default.length(e):e.delete?t-e.delete:t,0)}length(){return this.reduce((t,e)=>t+o.default.length(e),0)}slice(t=0,e=1/0){let r=[],i=new a.default(this.ops),n=0;for(;n<e&&i.hasNext();){let s;n<t?s=i.next(t-n):(s=i.next(e-n),r.push(s)),n+=o.default.length(s)}return new h(r)}compose(t){let e=new a.default(this.ops),r=new a.default(t.ops),i=[],n=r.peek();if(null!=n&&"number"==typeof n.retain&&null==n.attributes){let t=n.retain;for(;"insert"===e.peekType()&&e.peekLength()<=t;)t-=e.peekLength(),i.push(e.next());n.retain-t>0&&r.next(n.retain-t)}let o=new h(i);for(;e.hasNext()||r.hasNext();)if("insert"===r.peekType())o.push(r.next());else if("delete"===e.peekType())o.push(e.next());else{let t=Math.min(e.peekLength(),r.peekLength()),i=e.next(t),n=r.next(t);if(n.retain){let a={};if("number"==typeof i.retain)a.retain="number"==typeof n.retain?t:n.retain;else if("number"==typeof n.retain)null==i.retain?a.insert=i.insert:a.retain=i.retain;else{let t=null==i.retain?"insert":"retain",[e,r,s]=c(i[t],n.retain),l=h.getHandler(e);a[t]={[e]:l.compose(r,s,"retain"===t)}}let u=l.default.compose(i.attributes,n.attributes,"number"==typeof i.retain);if(u&&(a.attributes=u),o.push(a),!r.hasNext()&&s(o.ops[o.ops.length-1],a)){let t=new h(e.rest());return o.concat(t).chop()}}else"number"==typeof n.delete&&("number"==typeof i.retain||"object"==typeof i.retain&&null!==i.retain)&&o.push(n)}return o.chop()}concat(t){let e=new h(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e}diff(t,e){if(this.ops===t.ops)return new h;let r=[this,t].map(e=>e.map(r=>{if(null!=r.insert)return"string"==typeof r.insert?r.insert:"\0";throw Error("diff() called "+(e===t?"on":"with")+" non-document")}).join("")),n=new h,o=i(r[0],r[1],e,!0),c=new a.default(this.ops),u=new a.default(t.ops);return o.forEach(t=>{let e=t[1].length;for(;e>0;){let r=0;switch(t[0]){case i.INSERT:r=Math.min(u.peekLength(),e),n.push(u.next(r));break;case i.DELETE:r=Math.min(e,c.peekLength()),c.next(r),n.delete(r);break;case i.EQUAL:r=Math.min(c.peekLength(),u.peekLength(),e);let o=c.next(r),a=u.next(r);s(o.insert,a.insert)?n.retain(r,l.default.diff(o.attributes,a.attributes)):n.push(a).delete(r)}e-=r}}),n.chop()}eachLine(t,e="\n"){let r=new a.default(this.ops),i=new h,n=0;for(;r.hasNext();){if("insert"!==r.peekType())return;let s=r.peek(),l=o.default.length(s)-r.peekLength(),a="string"==typeof s.insert?s.insert.indexOf(e,l)-l:-1;if(a<0)i.push(r.next());else if(a>0)i.push(r.next(a));else{if(!1===t(i,r.next(1).attributes||{},n))return;n+=1,i=new h}}i.length()>0&&t(i,{},n)}invert(t){let e=new h;return this.reduce((r,i)=>{if(i.insert)e.delete(o.default.length(i));else if("number"==typeof i.retain&&null==i.attributes)return e.retain(i.retain),r+i.retain;else if(i.delete||"number"==typeof i.retain){let n=i.delete||i.retain;return t.slice(r,r+n).forEach(t=>{i.delete?e.push(t):i.retain&&i.attributes&&e.retain(o.default.length(t),l.default.invert(i.attributes,t.attributes))}),r+n}else if("object"==typeof i.retain&&null!==i.retain){let n=t.slice(r,r+1),s=new a.default(n.ops).next(),[o,u,d]=c(i.retain,s.insert),f=h.getHandler(o);return e.retain({[o]:f.invert(u,d)},l.default.invert(i.attributes,s.attributes)),r+1}return r},0),e.chop()}transform(t,e=!1){if(e=!!e,"number"==typeof t)return this.transformPosition(t,e);let r=new a.default(this.ops),i=new a.default(t.ops),n=new h;for(;r.hasNext()||i.hasNext();)if("insert"===r.peekType()&&(e||"insert"!==i.peekType()))n.retain(o.default.length(r.next()));else if("insert"===i.peekType())n.push(i.next());else{let t=Math.min(r.peekLength(),i.peekLength()),s=r.next(t),o=i.next(t);if(s.delete)continue;if(o.delete)n.push(o);else{let r=s.retain,i=o.retain,a="object"==typeof i&&null!==i?i:t;if("object"==typeof r&&null!==r&&"object"==typeof i&&null!==i){let t=Object.keys(r)[0];if(t===Object.keys(i)[0]){let n=h.getHandler(t);n&&(a={[t]:n.transform(r[t],i[t],e)})}}n.retain(a,l.default.transform(s.attributes,o.attributes,e))}}return n.chop()}transformPosition(t,e=!1){e=!!e;let r=new a.default(this.ops),i=0;for(;r.hasNext()&&i<=t;){let n=r.peekLength(),s=r.peekType();if(r.next(),"delete"===s){t-=Math.min(n,t-i);continue}"insert"===s&&(i<t||!e)&&(t+=n),i+=n}return t}}h.Op=o.default,h.OpIterator=a.default,h.AttributeMap=l.default,h.handlers={},e.default=h,t.exports=h,t.exports.default=h},55110:(t,e)=>{"use strict";var r;Object.defineProperty(e,"__esModule",{value:!0}),(r||(r={})).length=function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"object"==typeof t.retain&&null!==t.retain?1:"string"==typeof t.insert?t.insert.length:1},e.default=r},82661:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function i(){}function n(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function s(t,e,i,s,l){if("function"!=typeof i)throw TypeError("The listener must be a function");var o=new n(i,s||t,l),a=r?r+e:e;return t._events[a]?t._events[a].fn?t._events[a]=[t._events[a],o]:t._events[a].push(o):(t._events[a]=o,t._eventsCount++),t}function l(t,e){0==--t._eventsCount?t._events=new i:delete t._events[e]}function o(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(r=!1)),o.prototype.eventNames=function(){var t,i,n=[];if(0===this._eventsCount)return n;for(i in t=this._events)e.call(t,i)&&n.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(t)):n},o.prototype.listeners=function(t){var e=r?r+t:t,i=this._events[e];if(!i)return[];if(i.fn)return[i.fn];for(var n=0,s=i.length,l=Array(s);n<s;n++)l[n]=i[n].fn;return l},o.prototype.listenerCount=function(t){var e=r?r+t:t,i=this._events[e];return i?i.fn?1:i.length:0},o.prototype.emit=function(t,e,i,n,s,l){var o=r?r+t:t;if(!this._events[o])return!1;var a,c,h=this._events[o],u=arguments.length;if(h.fn){switch(h.once&&this.removeListener(t,h.fn,void 0,!0),u){case 1:return h.fn.call(h.context),!0;case 2:return h.fn.call(h.context,e),!0;case 3:return h.fn.call(h.context,e,i),!0;case 4:return h.fn.call(h.context,e,i,n),!0;case 5:return h.fn.call(h.context,e,i,n,s),!0;case 6:return h.fn.call(h.context,e,i,n,s,l),!0}for(c=1,a=Array(u-1);c<u;c++)a[c-1]=arguments[c];h.fn.apply(h.context,a)}else{var d,f=h.length;for(c=0;c<f;c++)switch(h[c].once&&this.removeListener(t,h[c].fn,void 0,!0),u){case 1:h[c].fn.call(h[c].context);break;case 2:h[c].fn.call(h[c].context,e);break;case 3:h[c].fn.call(h[c].context,e,i);break;case 4:h[c].fn.call(h[c].context,e,i,n);break;default:if(!a)for(d=1,a=Array(u-1);d<u;d++)a[d-1]=arguments[d];h[c].fn.apply(h[c].context,a)}}return!0},o.prototype.on=function(t,e,r){return s(this,t,e,r,!1)},o.prototype.once=function(t,e,r){return s(this,t,e,r,!0)},o.prototype.removeListener=function(t,e,i,n){var s=r?r+t:t;if(!this._events[s])return this;if(!e)return l(this,s),this;var o=this._events[s];if(o.fn)o.fn!==e||n&&!o.once||i&&o.context!==i||l(this,s);else{for(var a=0,c=[],h=o.length;a<h;a++)(o[a].fn!==e||n&&!o[a].once||i&&o[a].context!==i)&&c.push(o[a]);c.length?this._events[s]=1===c.length?c[0]:c:l(this,s)}return this},o.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&l(this,e)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,t.exports=o},87311:t=>{function e(t,d,f,p,g){if(t===d)return t?[[0,t]]:[];if(null!=f){var b=function(t,e,r){var i="number"==typeof r?{index:r,length:0}:r.oldRange,n="number"==typeof r?null:r.newRange,s=t.length,l=e.length;if(0===i.length&&(null===n||0===n.length)){var o=i.index,a=t.slice(0,o),c=t.slice(o),h=n?n.index:null;t:{var u=o+l-s;if(null!==h&&h!==u||u<0||u>l)break t;var d=e.slice(0,u),f=e.slice(u);if(f!==c)break t;var p=Math.min(o,u),g=a.slice(0,p),b=d.slice(0,p);if(g!==b)break t;var y=a.slice(p),v=d.slice(p);return m(g,y,v,c)}e:if(null===h||h===o){var d=e.slice(0,o),f=e.slice(o);if(d!==a)break e;var x=Math.min(s-o,l-o),N=c.slice(c.length-x),E=f.slice(f.length-x);if(N!==E)break e;var y=c.slice(0,c.length-x),v=f.slice(0,f.length-x);return m(a,y,v,N)}}if(i.length>0&&n&&0===n.length)r:{var g=t.slice(0,i.index),N=t.slice(i.index+i.length),p=g.length,x=N.length;if(l<p+x)break r;var b=e.slice(0,p),E=e.slice(l-x);if(g!==b||N!==E)break r;var y=t.slice(p,s-x),v=e.slice(p,l-x);return m(g,y,v,N)}return null}(t,d,f);if(b)return b}var y=i(t,d),v=t.substring(0,y);y=s(t=t.substring(y),d=d.substring(y));var x=t.substring(t.length-y),N=function(t,n){if(!t)return[[1,n]];if(!n)return[[-1,t]];var l,o=t.length>n.length?t:n,a=t.length>n.length?n:t,c=o.indexOf(a);if(-1!==c)return l=[[1,o.substring(0,c)],[0,a],[1,o.substring(c+a.length)]],t.length>n.length&&(l[0][0]=l[2][0]=-1),l;if(1===a.length)return[[-1,t],[1,n]];var h=function(t,e){var r,n,l,o,a,c=t.length>e.length?t:e,h=t.length>e.length?e:t;if(c.length<4||2*h.length<c.length)return null;function u(t,e,r){for(var n,l,o,a,c=t.substring(r,r+Math.floor(t.length/4)),h=-1,u="";-1!==(h=e.indexOf(c,h+1));){var d=i(t.substring(r),e.substring(h)),f=s(t.substring(0,r),e.substring(0,h));u.length<f+d&&(u=e.substring(h-f,h)+e.substring(h,h+d),n=t.substring(0,r-f),l=t.substring(r+d),o=e.substring(0,h-f),a=e.substring(h+d))}return 2*u.length>=t.length?[n,l,o,a,u]:null}var d=u(c,h,Math.ceil(c.length/4)),f=u(c,h,Math.ceil(c.length/2));return d||f?(r=f?d&&d[4].length>f[4].length?d:f:d,t.length>e.length?(n=r[0],l=r[1],o=r[2],a=r[3]):(o=r[0],a=r[1],n=r[2],l=r[3]),[n,l,o,a,r[4]]):null}(t,n);if(h){var u=h[0],d=h[1],f=h[2],p=h[3],g=h[4],m=e(u,f),b=e(d,p);return m.concat([[0,g]],b)}return function(t,e){for(var i=t.length,n=e.length,s=Math.ceil((i+n)/2),l=2*s,o=Array(l),a=Array(l),c=0;c<l;c++)o[c]=-1,a[c]=-1;o[s+1]=0,a[s+1]=0;for(var h=i-n,u=h%2!=0,d=0,f=0,p=0,g=0,m=0;m<s;m++){for(var b=-m+d;b<=m-f;b+=2){for(var y,v=s+b,x=(y=b===-m||b!==m&&o[v-1]<o[v+1]?o[v+1]:o[v-1]+1)-b;y<i&&x<n&&t.charAt(y)===e.charAt(x);)y++,x++;if(o[v]=y,y>i)f+=2;else if(x>n)d+=2;else if(u){var N=s+h-b;if(N>=0&&N<l&&-1!==a[N]){var E=i-a[N];if(y>=E)return r(t,e,y,x)}}}for(var A=-m+p;A<=m-g;A+=2){for(var E,N=s+A,w=(E=A===-m||A!==m&&a[N-1]<a[N+1]?a[N+1]:a[N-1]+1)-A;E<i&&w<n&&t.charAt(i-E-1)===e.charAt(n-w-1);)E++,w++;if(a[N]=E,E>i)g+=2;else if(w>n)p+=2;else if(!u){var v=s+h-A;if(v>=0&&v<l&&-1!==o[v]){var y=o[v],x=s+y-v;if(y>=(E=i-E))return r(t,e,y,x)}}}}return[[-1,t],[1,e]]}(t,n)}(t=t.substring(0,t.length-y),d=d.substring(0,d.length-y));return v&&N.unshift([0,v]),x&&N.push([0,x]),u(N,g),p&&function(t){for(var e=!1,r=[],i=0,d=null,f=0,p=0,g=0,m=0,b=0;f<t.length;)0==t[f][0]?(r[i++]=f,p=m,g=b,m=0,b=0,d=t[f][1]):(1==t[f][0]?m+=t[f][1].length:b+=t[f][1].length,d&&d.length<=Math.max(p,g)&&d.length<=Math.max(m,b)&&(t.splice(r[i-1],0,[-1,d]),t[r[i-1]+1][0]=1,i--,f=--i>0?r[i-1]:-1,p=0,g=0,m=0,b=0,d=null,e=!0)),f++;for(e&&u(t),function(t){function e(t,e){if(!t||!e)return 6;var r=t.charAt(t.length-1),i=e.charAt(0),n=r.match(l),s=i.match(l),u=n&&r.match(o),d=s&&i.match(o),f=u&&r.match(a),p=d&&i.match(a),g=f&&t.match(c),m=p&&e.match(h);if(g||m)return 5;if(f||p)return 4;if(n&&!u&&d)return 3;if(u||d)return 2;if(n||s)return 1;return 0}for(var r=1;r<t.length-1;){if(0==t[r-1][0]&&0==t[r+1][0]){var i=t[r-1][1],n=t[r][1],u=t[r+1][1],d=s(i,n);if(d){var f=n.substring(n.length-d);i=i.substring(0,i.length-d),n=f+n.substring(0,n.length-d),u=f+u}for(var p=i,g=n,m=u,b=e(i,n)+e(n,u);n.charAt(0)===u.charAt(0);){i+=n.charAt(0),n=n.substring(1)+u.charAt(0),u=u.substring(1);var y=e(i,n)+e(n,u);y>=b&&(b=y,p=i,g=n,m=u)}t[r-1][1]!=p&&(p?t[r-1][1]=p:(t.splice(r-1,1),r--),t[r][1]=g,m?t[r+1][1]=m:(t.splice(r+1,1),r--))}r++}}(t),f=1;f<t.length;){if(-1==t[f-1][0]&&1==t[f][0]){var y=t[f-1][1],v=t[f][1],x=n(y,v),N=n(v,y);x>=N?(x>=y.length/2||x>=v.length/2)&&(t.splice(f,0,[0,v.substring(0,x)]),t[f-1][1]=y.substring(0,y.length-x),t[f+1][1]=v.substring(x),f++):(N>=y.length/2||N>=v.length/2)&&(t.splice(f,0,[0,y.substring(0,N)]),t[f-1][0]=1,t[f-1][1]=v.substring(0,v.length-N),t[f+1][0]=-1,t[f+1][1]=y.substring(N),f++),f++}f++}}(N),N}function r(t,r,i,n){var s=t.substring(0,i),l=r.substring(0,n),o=t.substring(i),a=r.substring(n),c=e(s,l),h=e(o,a);return c.concat(h)}function i(t,e){if(!t||!e||t.charAt(0)!==e.charAt(0))return 0;for(var r=0,i=Math.min(t.length,e.length),n=i,s=0;r<n;)t.substring(s,n)==e.substring(s,n)?s=r=n:i=n,n=Math.floor((i-r)/2+r);return d(t.charCodeAt(n-1))&&n--,n}function n(t,e){var r=t.length,i=e.length;if(0==r||0==i)return 0;r>i?t=t.substring(r-i):r<i&&(e=e.substring(0,r));var n=Math.min(r,i);if(t==e)return n;for(var s=0,l=1;;){var o=t.substring(n-l),a=e.indexOf(o);if(-1==a)return s;l+=a,(0==a||t.substring(n-l)==e.substring(0,l))&&(s=l,l++)}}function s(t,e){if(!t||!e||t.slice(-1)!==e.slice(-1))return 0;for(var r=0,i=Math.min(t.length,e.length),n=i,s=0;r<n;)t.substring(t.length-n,t.length-s)==e.substring(e.length-n,e.length-s)?s=r=n:i=n,n=Math.floor((i-r)/2+r);return f(t.charCodeAt(t.length-n))&&n--,n}var l=/[^a-zA-Z0-9]/,o=/\s/,a=/[\r\n]/,c=/\n\r?\n$/,h=/^\r?\n\r?\n/;function u(t,e){t.push([0,""]);for(var r,n=0,l=0,o=0,a="",c="";n<t.length;){if(n<t.length-1&&!t[n][1]){t.splice(n,1);continue}switch(t[n][0]){case 1:o++,c+=t[n][1],n++;break;case -1:l++,a+=t[n][1],n++;break;case 0:var h=n-o-l-1;if(e){if(h>=0&&g(t[h][1])){var d=t[h][1].slice(-1);if(t[h][1]=t[h][1].slice(0,-1),a=d+a,c=d+c,!t[h][1]){t.splice(h,1),n--;var f=h-1;t[f]&&1===t[f][0]&&(o++,c=t[f][1]+c,f--),t[f]&&-1===t[f][0]&&(l++,a=t[f][1]+a,f--),h=f}}if(p(t[n][1])){var d=t[n][1].charAt(0);t[n][1]=t[n][1].slice(1),a+=d,c+=d}}if(n<t.length-1&&!t[n][1]){t.splice(n,1);break}if(a.length>0||c.length>0){a.length>0&&c.length>0&&(0!==(r=i(c,a))&&(h>=0?t[h][1]+=c.substring(0,r):(t.splice(0,0,[0,c.substring(0,r)]),n++),c=c.substring(r),a=a.substring(r)),0!==(r=s(c,a))&&(t[n][1]=c.substring(c.length-r)+t[n][1],c=c.substring(0,c.length-r),a=a.substring(0,a.length-r)));var m=o+l;0===a.length&&0===c.length?(t.splice(n-m,m),n-=m):0===a.length?(t.splice(n-m,m,[1,c]),n=n-m+1):0===c.length?(t.splice(n-m,m,[-1,a]),n=n-m+1):(t.splice(n-m,m,[-1,a],[1,c]),n=n-m+2)}0!==n&&0===t[n-1][0]?(t[n-1][1]+=t[n][1],t.splice(n,1)):n++,o=0,l=0,a="",c=""}}""===t[t.length-1][1]&&t.pop();var b=!1;for(n=1;n<t.length-1;)0===t[n-1][0]&&0===t[n+1][0]&&(t[n][1].substring(t[n][1].length-t[n-1][1].length)===t[n-1][1]?(t[n][1]=t[n-1][1]+t[n][1].substring(0,t[n][1].length-t[n-1][1].length),t[n+1][1]=t[n-1][1]+t[n+1][1],t.splice(n-1,1),b=!0):t[n][1].substring(0,t[n+1][1].length)==t[n+1][1]&&(t[n-1][1]+=t[n+1][1],t[n][1]=t[n][1].substring(t[n+1][1].length)+t[n+1][1],t.splice(n+1,1),b=!0)),n++;b&&u(t,e)}function d(t){return t>=55296&&t<=56319}function f(t){return t>=56320&&t<=57343}function p(t){return f(t.charCodeAt(0))}function g(t){return d(t.charCodeAt(t.length-1))}function m(t,e,r,i){if(g(t)||p(i))return null;for(var n=[[0,t],[-1,e],[1,r],[0,i]],s=[],l=0;l<n.length;l++)n[l][1].length>0&&s.push(n[l]);return s}function b(t,r,i,n){return e(t,r,i,n,!0)}b.INSERT=1,b.DELETE=-1,b.EQUAL=0,t.exports=b}}]);