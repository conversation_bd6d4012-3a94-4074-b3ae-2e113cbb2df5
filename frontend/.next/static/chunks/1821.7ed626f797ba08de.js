"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1821],{15452:(e,t,r)=>{r.d(t,{G$:()=>J,Hs:()=>D,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>X});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),d=r(19178),c=r(25519),u=r(34378),p=r(28905),f=r(63655),h=r(92293),y=r(93795),g=r(38168),v=r(99708),m=r(95155),x="Dialog",[k,D]=(0,l.A)(x),[b,w]=k(x),A=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:x});return(0,m.jsx)(b,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};A.displayName=x;var j="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=w(j,r),i=(0,a.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":U(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});C.displayName=j;var R="DialogPortal",[M,I]=k(R,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=w(R,t);return(0,m.jsx)(M,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,m.jsx)(p.C,{present:r||l.open,children:(0,m.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};N.displayName=R;var O="DialogOverlay",_=n.forwardRef((e,t)=>{let r=I(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=w(O,e.__scopeDialog);return a.modal?(0,m.jsx)(p.C,{present:n||a.open,children:(0,m.jsx)(F,{...o,ref:t})}):null});_.displayName=O;var E=(0,v.TL)("DialogOverlay.RemoveScroll"),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(O,r);return(0,m.jsx)(y.A,{as:E,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",q=n.forwardRef((e,t)=>{let r=I(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=w(P,e.__scopeDialog);return(0,m.jsx)(p.C,{present:n||a.open,children:a.modal?(0,m.jsx)(G,{...o,ref:t}):(0,m.jsx)(T,{...o,ref:t})})});q.displayName=P;var G=n.forwardRef((e,t)=>{let r=w(P,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,m.jsx)(B,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=w(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,m.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=w(P,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,m.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":U(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(Y,{titleId:u.titleId}),(0,m.jsx)($,{contentRef:p,descriptionId:u.descriptionId})]})]})}),H="DialogTitle",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(H,r);return(0,m.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});L.displayName=H;var W="DialogDescription",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(W,r);return(0,m.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});z.displayName=W;var S="DialogClose",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=w(S,r);return(0,m.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function U(e){return e?"open":"closed"}Z.displayName=S;var V="DialogTitleWarning",[J,K]=(0,l.q)(V,{contentName:P,titleName:H,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,r=K(V),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:r}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},Q=A,X=C,ee=N,et=_,er=q,en=L,eo=z,ea=Z},27213:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},29869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(12115),o=r(63655),a=r(95155),l=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},43332:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},48136:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},71007:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}}]);