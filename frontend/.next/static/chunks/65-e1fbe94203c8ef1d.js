"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[65],{9793:(e,r,t)=>{t.d(r,{IQScoreProvider:()=>s,m:()=>c});var a=t(95155),n=t(12115),o=t(84037);let i=(0,n.createContext)(void 0);function s(e){let{children:r}=e,[t,s]=(0,n.useState)({}),c=(0,n.useRef)({}),l=(0,n.useCallback)(e=>t[e]||null,[t]),u=(0,n.useCallback)(async e=>{if(!e||isNaN(e)||e<=0)return console.warn("Invalid userId provided to getUserIQ:",e),null;if(t[e])return t[e];if(e in c.current&&c.current[e])try{return await c.current[e]}catch(r){return console.error("Error awaiting pending request for user ".concat(e,":"),r),null}let r=async()=>{try{let r=await o.f.getUserIQ(e);if((null==r?void 0:r.success)&&r.data)return s(t=>({...t,[e]:r.data})),r.data;return null}catch(r){return console.error("Error fetching IQ score for user ".concat(e,":"),r),null}finally{setTimeout(()=>{delete c.current[e]},100)}};try{return c.current[e]=r(),await c.current[e]}catch(r){return console.error("Critical error in getUserIQ for user ".concat(e,":"),r),delete c.current[e],null}},[t]),d=(0,n.useCallback)(async e=>{let r={},a=[];if(e.forEach(e=>{t[e]?r[e]=t[e]:a.push(e)}),a.length>0){let e=[];for(let r=0;r<a.length;r+=5)e.push(a.slice(r,r+5));for(let t of e){let e=t.map(e=>u(e));(await Promise.all(e)).forEach((e,a)=>{e&&void 0!==t[a]&&(r[t[a]]=e)})}}return r},[t,u]),f=(0,n.useCallback)(()=>{s({}),c.current={}},[]);return(0,a.jsx)(i.Provider,{value:{getUserIQ:u,batchGetUserIQs:d,clearCache:f,getCachedUserIQ:l},children:r})}function c(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useIQScore must be used within an IQScoreProvider");return e}},30285:(e,r,t)=>{t.d(r,{$:()=>c,r:()=>s});var a=t(95155);t(12115);var n=t(99708),o=t(74466),i=t(59434);let s=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:r,variant:t,size:o,asChild:c=!1,...l}=e,u=c?n.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,i.cn)(s({variant:t,size:o,className:r})),...l})}},59434:(e,r,t)=>{t.d(r,{cn:()=>o});var a=t(52596),n=t(39688);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}},59949:(e,r,t)=>{t.d(r,{UserAvatarWithRank:()=>c});var a=t(95155),n=t(12115),o=t(91394),i=t(9793),s=t(66766);function c(e){var r,t,c,l,u;let d,{userId:f,avatarUrl:h,displayName:v,size:g="h-10 w-10",containerSize:p="w-12 h-12",showRankBadge:m=!0,userRoles:b=[],iqScoreData:w}=e,{getUserIQ:y,getCachedUserIQ:x}=(0,i.m)(),[k,_]=(0,n.useState)(null),[j,E]=(0,n.useState)(!1),N=(0,n.useCallback)(async()=>{if(w)return void _({score:w.score,rank:{name:w.rank},rank_data:w.rank_data,total_activities:0,activities:[]});if(!j)try{E(!0);let e=await y(f);e&&_({score:e.iq_score||0,rank:e.rank?{name:"string"==typeof e.rank?e.rank:e.rank.name}:void 0,rank_data:void 0,total_activities:0,activities:[]})}catch(e){console.error("Error fetching user rank:",e)}finally{E(!1)}},[j,y,f,w]),C=(0,n.useCallback)(async()=>{},[]);(0,n.useEffect)(()=>{if(w)return void _({score:w.score,rank:{name:w.rank},rank_data:w.rank_data,total_activities:0,activities:[]});let e=x(f);if(e)return void _({score:e.iq_score||0,rank:e.rank?{name:"string"==typeof e.rank?e.rank:e.rank.name}:void 0,rank_data:void 0,total_activities:0,activities:[]});m&&f&&N(),C()},[f,m,x,N,w,C]);let I=(()=>{let e=p.match(/w-(?:\[(\d+)px\]|(\d+))/),r=48;e&&(e[1]?r=parseInt(e[1]):e[2]&&(r=4*parseInt(e[2])));let t=Math.max(1.1*r,56);return{width:t,height:t}})(),T=(d=[],(b&&(Array.isArray(b)?b.length>0:Object.keys(b).length>0)?Array.isArray(b)?d=b:"object"==typeof b&&(d=Object.values(b)):d=[],d.includes("administrator")||d.includes("admin"))?"admin":d.includes("founder")?"founder":"member");return(0,a.jsxs)("div",{className:"relative flex items-center justify-center ".concat(p),children:[m&&"admin"===T&&(0,a.jsx)(s.default,{src:"/images/icons/".concat(T,".svg"),alt:"".concat(T," badge"),width:I.width,height:I.height,className:"absolute inset-0 object-contain"}),m&&("member"===T||"founder"===T)&&k&&(0,a.jsx)(s.default,{src:function(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"/images/icons/novice".concat(r?"-fc":"",".svg");let t=e.toLowerCase();return"/images/icons/".concat(t).concat(r?"-fc":"",".svg")}((null==(r=k.rank)?void 0:r.name)||(null==(t=k.rank_data)?void 0:t.name),"founder"===T),alt:"".concat((null==(c=k.rank)?void 0:c.name)||(null==(l=k.rank_data)?void 0:l.name)||"Member"," rank badge"),width:I.width,height:I.height,className:"absolute inset-0 object-contain"}),(0,a.jsxs)(o.eu,{className:"".concat(g," relative z-10 border ").concat(g.includes("150px")?"border-4 border-white bg-white":""),children:[(0,a.jsx)(o.BK,{src:h,alt:v,className:g.includes("150px")?"object-cover":""}),(0,a.jsx)(o.q5,{className:"bg-gray-100 ".concat(g.includes("150px")?"text-xl":""),children:g.includes("150px")?v.split(" ").map(e=>e[0]).join("").toUpperCase():"".concat(v.charAt(0)).concat((null==(u=v.split(" ")[1])?void 0:u.charAt(0))||"")})]})]})}},84037:(e,r,t)=>{t.d(r,{f:()=>n});class a{async getLeaderboard(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let t=r?"&force_refresh=1&_=".concat(Date.now()):"&_=".concat(Date.now()),a=await fetch("/api/wp-proxy/iq-score/leaderboard?limit=".concat(e).concat(t),{credentials:"include",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("HTTP error! status: ".concat(a.status));return await a.json()}catch(e){throw console.error("Error fetching leaderboard:",e),e}}async getCurrentUserIQ(){try{let e=await fetch("/api/wp-proxy/iq-score/me",{credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));return await e.json()}catch(e){throw console.error("Error fetching current user IQ score:",e),e}}async getUserIQ(e){try{if(!e||isNaN(e)||e<=0)throw Error("Invalid user ID: ".concat(e));let r=await fetch("/api/wp-proxy/iq-score/".concat(e),{credentials:"include",headers:{"Content-Type":"application/json"},signal:AbortSignal.timeout(1e4)});if(!r.ok){let e="";try{let t=await r.json();e=t.error||t.message||""}catch(e){}throw Error("HTTP error! status: ".concat(r.status).concat(e?" - ".concat(e):""))}let t=await r.json();if(!t||"object"!=typeof t)throw Error("Invalid response format");return t}catch(r){if(r instanceof Error&&"AbortError"===r.name)throw console.error("Timeout fetching IQ score for user ".concat(e)),Error("Request timeout - please try again");throw console.error("Error fetching IQ score for user ".concat(e,":"),r),r}}async getRanks(){try{let e=await fetch("/api/wp-proxy/iq-score/ranks",{credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));return await e.json()}catch(e){throw console.error("Error fetching ranks:",e),e}}async awardPoints(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"manual";try{let a=await fetch("".concat(this.baseUrl,"/wp-json/tourismiq/v1/iq-score/award"),{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:e,points:r,activity_type:t})});if(!a.ok)throw Error("HTTP error! status: ".concat(a.status));return await a.json()}catch(e){throw console.error("Error awarding points:",e),e}}getBadgeForUser(e,r){return r?e.founder_badge:e.member_badge}formatScore(e){return e.toLocaleString()}getRankColor(e){let r={Novice:"#94a3b8",Contributor:"#06b6d4",Engager:"#10b981",Influencer:"#f59e0b",Expert:"#f97316",Master:"#dc2626"};return r[e]||r.Novice||"#94a3b8"}getNextRankInfo(e,r){let t=r.find(r=>e>=r.min_points&&e<=r.max_points);if(!t)return{nextRank:null,pointsNeeded:0,progress:0};let a=r.find(e=>e.min_points>t.max_points);if(!a)return{nextRank:null,pointsNeeded:0,progress:100};let n=a.min_points-e,o=t.max_points-t.min_points+1;return{nextRank:a,pointsNeeded:n,progress:Math.min((e-t.min_points)/o*100,100)}}constructor(){this.baseUrl="http://tourismiq-headless.local"}}let n=new a},91394:(e,r,t)=>{t.d(r,{BK:()=>c,eu:()=>s,q5:()=>l});var a=t(95155),n=t(12115),o=t(54011),i=t(59434);function s(e){let{className:r,...t}=e;return(0,a.jsx)(o.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",r),...t})}function c(e){let{className:r,src:t,alt:s,onError:c,...l}=e,[u,d]=n.useState(!1),[f,h]=n.useState("string"==typeof t?t:void 0);return(n.useEffect(()=>{if("string"==typeof t&&""!==t.trim()){let e=t;e.startsWith("http")||e.startsWith("/")||(e="/".concat(e.replace(/^\/+/,""))),h(e),d(!1)}else h(void 0)},[t]),u||!f)?null:(0,a.jsx)(o._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full object-cover",r),src:f,alt:s||"User avatar",onError:e=>{console.error("Failed to load avatar:",t),d(!0),c&&c(e)},...l})}function l(e){let{className:r,...t}=e;return(0,a.jsx)(o.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",r),...t})}}}]);