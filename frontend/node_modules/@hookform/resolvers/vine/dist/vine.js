var e=require("@hookform/resolvers"),r=require("@vinejs/vine"),t=require("react-hook-form");function s(e,r){for(var s={};e.length;){var o=e[0],i=o.field;if(i in s||(s[i]={message:o.message,type:o.rule}),r){var n=s[i].types,a=n&&n[o.rule];s[i]=t.appendErrors(i,r,s,o.rule,a?[].concat(a,[o.message]):o.message)}e.shift()}return s}exports.vineResolver=function(t,o,i){return void 0===i&&(i={}),function(n,a,u){try{return Promise.resolve(function(r,s){try{var a=Promise.resolve(t.validate(n,o)).then(function(r){return u.shouldUseNativeValidation&&e.validateFieldsNatively({},u),{errors:{},values:i.raw?Object.assign({},n):r}})}catch(e){return s(e)}return a&&a.then?a.then(void 0,s):a}(0,function(t){if(t instanceof r.errors.E_VALIDATION_ERROR)return{values:{},errors:e.toNestErrors(s(t.messages,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw t}))}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=vine.js.map
