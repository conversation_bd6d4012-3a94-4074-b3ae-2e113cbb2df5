import{validateFieldsNatively as e,toNestErrors as t}from"@hookform/resolvers";import{appendErrors as o}from"react-hook-form";function a(a,r,s={}){return async(n,c,i)=>{try{null!=r&&r.context&&"development"===process.env.NODE_ENV&&console.warn("You should not used the yup options context. Please, use the 'useForm' context object instead");const t=await a["sync"===s.mode?"validateSync":"validate"](n,Object.assign({abortEarly:!1},r,{context:c}));return i.shouldUseNativeValidation&&e({},i),{values:s.raw?Object.assign({},n):t,errors:{}}}catch(e){if(!e.inner)throw e;return{values:{},errors:t((l=e,p=!i.shouldUseNativeValidation&&"all"===i.criteriaMode,(l.inner||[]).reduce((e,t)=>{if(e[t.path]||(e[t.path]={message:t.message,type:t.type}),p){const a=e[t.path].types,r=a&&a[t.type];e[t.path]=o(t.path,p,e,t.type,r?[].concat(r,t.message):t.message)}return e},{})),i)}}var l,p}}export{a as yupResolver};
//# sourceMappingURL=yup.modern.mjs.map
