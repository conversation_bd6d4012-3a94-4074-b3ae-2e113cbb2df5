"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[629],{17313:(e,t,s)=>{s.d(t,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>l});var a=s(95155),r=s(12115),n=s(60704),i=s(59434);let l=n.bL,o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});o.displayName=n.B8.displayName;let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});d.displayName=n.l9.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});c.displayName=n.UC.displayName},26126:(e,t,s)=>{s.d(t,{E:()=>l});var a=s(95155);s(12115);var r=s(74466),n=s(59434);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:s}),t),...r})}},31807:(e,t,s)=>{s.d(t,{MessagingProvider:()=>h,useMessaging:()=>g});var a=s(95155),r=s(12115),n=s(84405),i=s(77650),l=s(27600),o=s(77470),d=s(48140);let c={enabled:!1,level:"info"},m={debug:0,info:1,warn:2,error:3},u=e=>!!c.enabled&&m[e]>=m[c.level],x={error:function(e){for(var t=arguments.length,s=Array(t>1?t-1:0),a=1;a<t;a++)s[a-1]=arguments[a];u("error")&&console.error("❌ [ERROR] ".concat(e),...s)}},f=(0,r.createContext)(void 0),g=()=>{let e=(0,r.useContext)(f);if(!e)throw Error("useMessaging must be used within a MessagingProvider");return e};function h(e){let{children:t}=e,{user:s}=(0,l.A)(),{socket:c}=(0,o.E)(),[m,u]=(0,r.useState)(!1),[g,h]=(0,r.useState)([]),[p,v]=(0,r.useState)(!1),[j,y]=(0,r.useState)(0),N=(0,r.useCallback)(e=>({bottom:0,right:8+328*e}),[]);(0,r.useEffect)(()=>{s||(h([]),u(!1))},[s]),(0,r.useEffect)(()=>{let e,t;if(!(null==s?void 0:s.id))return y(0),v(!1),()=>{};let a=!0,r=async()=>{if(a)try{let{unreadCount:e}=await d.J.getUnreadCount();a&&(y(e),v(e>0))}catch(e){a&&(x.error("Failed to fetch unread count",e),e instanceof Error&&e.message.includes("Sorry, you are not allowed")&&(y(0),v(!1)))}};return t=setTimeout(r,2e3),e=setInterval(()=>{a&&r()},6e5),()=>{a=!1,clearInterval(e),clearTimeout(t)}},[null==s?void 0:s.id]);let b=(0,r.useCallback)(()=>{u(e=>e)},[]),w=(0,r.useCallback)(()=>{u(!0)},[]),C=(0,r.useCallback)(()=>{u(!1)},[]);(0,r.useEffect)(()=>{if(!c)return()=>{};let e=e=>{y(e=>e+1),v(!0);let t=e.senderId;-1===g.findIndex(e=>{var s;return(null==(s=e.conversation.participants[0])?void 0:s.id)===t})&&b()},t=()=>{setTimeout(()=>{d.J.getUnreadCount().then(e=>{let{unreadCount:t}=e;y(t),v(t>0)}).catch(e=>{x.error("Failed to refresh unread count after message read",e)})},5e3)};return c.on("new_message",e),c.on("messages_read",t),()=>{c.off("new_message",e),c.off("messages_read",t)}},[c,g,b]);let _=(0,r.useCallback)(e=>{h(t=>{var s,a;let r=null==(a=e.participants[0])||null==(s=a.id)?void 0:s.toString(),n=t.findIndex(e=>{var t,s;return(null==(s=e.conversation.participants[0])||null==(t=s.id)?void 0:t.toString())===r});if(-1!==n)return t.map((e,t)=>t===n?{...e,isMinimized:!1}:e);let i={conversation:e,position:N(t.length),isMinimized:!1,isMaximized:!1};return[...t,i]}),u(!1);let t=e.participants[0];if(t){let e=parseInt(t.id);d.J.markMessagesAsRead(e).catch(console.error)}},[N]),k=e=>{let t=g.findIndex(t=>t.conversation.id===e);-1!==t&&h(s=>s.filter(t=>t.conversation.id!==e).map((e,s)=>s>=t?{...e,position:N(s)}:e))},S=e=>{h(t=>t.map(t=>t.conversation.id===e?{...t,isMinimized:!t.isMinimized,isMaximized:!1}:t))},M=e=>{h(t=>t.map(t=>t.conversation.id===e?{...t,isMaximized:!t.isMaximized,isMinimized:!1}:{...t,isMaximized:!1}))},E=(0,r.useCallback)(e=>{let t=e.toString();return g.some(e=>{var s,a;return(null==(a=e.conversation.participants[0])||null==(s=a.id)?void 0:s.toString())===t})},[g]);return(0,a.jsxs)(f.Provider,{value:{openMessageList:w,closeMessageList:C,openChat:_,closeChat:k,minimizeChat:S,maximizeChat:M,isMessageListOpen:m,hasUnreadMessages:p,totalUnreadCount:j,refreshConversations:b,isChatOpen:E},children:[t,(0,a.jsx)(n.MessageList,{isOpen:m,onClose:C,onOpenChat:_}),g.map(e=>(0,a.jsx)(i.ChatWindow,{conversation:e.conversation,position:e.position,isMinimized:e.isMinimized,isMaximized:e.isMaximized,onClose:()=>k(e.conversation.id),onMinimize:()=>S(e.conversation.id),onMaximize:()=>M(e.conversation.id)},e.conversation.id))]})}},38382:(e,t,s)=>{s.d(t,{CG:()=>d,Fm:()=>g,Qs:()=>p,cj:()=>o,h:()=>f,kN:()=>c,qp:()=>h});var a=s(95155),r=s(12115),n=s(15452),i=s(74466),l=s(59434);let o=n.bL,d=n.l9,c=n.bm,m=e=>{let{...t}=e;return(0,a.jsx)(n.ZL,{...t})};m.displayName=n.ZL.displayName;let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.hJ,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r,ref:t})});u.displayName=n.hJ.displayName;let x=(0,i.F)("fixed z-50 gap-4 bg-background p-6 transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b shadow-lg data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t shadow-lg data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r shadow-[0_32px_65px_-15px_rgba(0,0,0,0.9)] data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l shadow-[0_32px_65px_-15px_rgba(0,0,0,0.9)] data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),f=r.forwardRef((e,t)=>{let{side:s="right",className:r,children:i,hideOverlay:o=!1,...d}=e;return(0,a.jsxs)(m,{children:[!o&&(0,a.jsx)(u,{}),(0,a.jsx)(n.UC,{ref:t,className:(0,l.cn)(x({side:s}),r),...d,children:i})]})});f.displayName=n.UC.displayName;let g=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...s})};g.displayName="SheetHeader";let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold text-foreground",s),...r})});h.displayName=n.hE.displayName;let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});p.displayName=n.VY.displayName},48140:(e,t,s)=>{s.d(t,{J:()=>a});let a={async sendMessage(e){let t=await fetch("/api/wp-proxy/messages/send",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to send message");return t.json()},async getConversations(){let e=await fetch("/api/wp-proxy/messages/conversations",{method:"GET",credentials:"include"});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch conversations");return e.json()},async getConversationMessages(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:50,a=await fetch("/api/wp-proxy/messages/conversation/".concat(e,"?page=").concat(t,"&per_page=").concat(s),{method:"GET",credentials:"include"});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch messages");return a.json()},async markMessagesAsRead(e){let t=await fetch("/api/wp-proxy/messages/mark-read",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({conversation_with:e})});if(!t.ok)throw Error((await t.json()).message||"Failed to mark messages as read")},async getUnreadCount(){let e=await fetch("/api/wp-proxy/messages/unread-count",{method:"GET",credentials:"include"});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch unread count");return e.json()},async canMessageUser(e){let t=await fetch("/api/wp-proxy/messages/can-message/".concat(e),{method:"GET",credentials:"include"});if(!t.ok)throw Error((await t.json()).message||"Failed to check messaging permissions");return t.json()}}},62523:(e,t,s)=>{s.d(t,{p:()=>n});var a=s(95155);s(12115);var r=s(59434);function n(e){let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},77650:(e,t,s)=>{s.d(t,{ChatWindow:()=>p});var a=s(95155),r=s(12115),n=s(54416),i=s(87712),l=s(91981),o=s(74311),d=s(12486),c=s(91394),m=s(30285),u=s(88539),x=s(59434),f=s(48140),g=s(77470),h=s(27600);function p(e){var t,s,p;let{conversation:v,position:j={bottom:0,right:340},isMinimized:y,isMaximized:N,onClose:b,onMinimize:w,onMaximize:C}=e,[_,k]=(0,r.useState)([]),[S,M]=(0,r.useState)(""),[E,I]=(0,r.useState)(!1),[z,A]=(0,r.useState)(!1),[D,T]=(0,r.useState)(!1),[F,L]=(0,r.useState)(384),[R,U]=(0,r.useState)(!1),O=(0,r.useRef)(null),J=(0,r.useRef)(null),{socket:Y}=(0,g.E)(),{isAuthenticated:$}=(0,h.A)(),q=(0,r.useCallback)(e=>{e.preventDefault(),U(!0);let t=e.clientY,s=e=>{L(Math.max(200,Math.min(800,F+(t-e.clientY))))},a=()=>{U(!1),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",a)};document.addEventListener("mousemove",s),document.addEventListener("mouseup",a)},[F]),B=(0,r.useMemo)(()=>v.participants[0],[v.participants]),K=(0,r.useMemo)(()=>null==B?void 0:B.id,[null==B?void 0:B.id]);(0,r.useEffect)(()=>{$||b()},[$,b]),(0,r.useEffect)(()=>{K&&(async()=>{try{I(!0);let e=await f.J.getConversationMessages(parseInt(K));k(e),await f.J.markMessagesAsRead(parseInt(K))}catch(e){console.error("Failed to load messages:",e)}finally{I(!1)}})()},[v.id,K]),(0,r.useEffect)(()=>{if(!Y||!K)return()=>{};Y.emit("get_user_status",{userId:K});let e=e=>{e.userId===K&&T(e.isOnline)};return Y.on("user_status_response",e),()=>{Y.off("user_status_response",e)}},[Y,K]),(0,r.useEffect)(()=>{if(!Y||!K)return()=>{};let e=e=>{let t=String(K);if(String(e.senderId)===t){let t={id:String(e.id),conversationId:v.id,senderId:String(e.senderId),content:e.content,timestamp:e.timestamp,isRead:!1};k(e=>[...e,t]),y||f.J.markMessagesAsRead(parseInt(K))}},t=e=>{e.userId===K&&A(!0)},s=e=>{e.userId===K&&A(!1)},a=e=>{e.userId===K&&T(e.isOnline)};return Y.on("new_message",e),Y.on("typing_start",t),Y.on("typing_stop",s),Y.on("user_status_changed",a),()=>{Y.off("new_message",e),Y.off("typing_start",t),Y.off("typing_stop",s),Y.off("user_status_changed",a)}},[Y,v.id,y,K]);let P=(0,r.useCallback)(async()=>{if(""!==S.trim()&&K)try{let e=await f.J.sendMessage({recipient_id:parseInt(K),content:S.trim()});k(t=>[...t,e.message]),M(""),Y&&Y.emit("typing_stop",{receiverId:K})}catch(e){console.error("Failed to send message:",e)}},[S,K,Y]),G=(0,r.useCallback)(()=>{Y&&K&&(Y.emit("typing_start",{receiverId:K}),setTimeout(()=>{Y.emit("typing_stop",{receiverId:K})},3e3))},[Y,K]);if((0,r.useEffect)(()=>{O.current&&!y&&(O.current.scrollTop=O.current.scrollHeight)},[_,y,N]),!B)return null;let V=()=>{var e;return(null==B?void 0:B.firstName)&&(null==B?void 0:B.lastName)?"".concat(B.firstName.charAt(0)).concat(B.lastName.charAt(0)):(null!=(e=null==B?void 0:B.username)?e:"User").substring(0,2).toUpperCase()},X=()=>{var e;return(null==B?void 0:B.firstName)&&(null==B?void 0:B.lastName)?"".concat(B.firstName," ").concat(B.lastName):null!=(e=null==B?void 0:B.username)?e:"User"},H=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),W=e=>{let t=new Date(e),s=new Date,a=new Date(s);return(a.setDate(a.getDate()-1),t.toDateString()===s.toDateString())?"Today":t.toDateString()===a.toDateString()?"Yesterday":t.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})};return y?(0,a.jsx)("div",{ref:J,className:"fixed bg-white border border-gray-200 rounded-t-lg shadow-lg z-50 w-48",style:{bottom:"".concat(j.bottom,"px"),right:"".concat(j.right,"px")},children:(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-primary text-primary-foreground rounded-t-lg cursor-pointer",onClick:w,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 min-w-0",children:[(0,a.jsxs)(c.eu,{className:"h-6 w-6",children:[(null==(p=B.avatar)?void 0:p.url)&&(0,a.jsx)(c.BK,{src:B.avatar.url,alt:X()}),(0,a.jsx)(c.q5,{className:"text-xs",children:V()})]}),(0,a.jsx)("span",{className:"text-sm font-medium truncate",children:X()}),v.unreadCount>0&&(0,a.jsx)("span",{className:"bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center",children:v.unreadCount})]}),(0,a.jsx)(m.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 text-primary-foreground hover:bg-white/15",onClick:e=>{e.stopPropagation(),b()},children:(0,a.jsx)(n.A,{className:"h-3 w-3"})})]})}):(0,a.jsxs)("div",{ref:J,className:(0,x.cn)("fixed bg-white border border-gray-200 rounded-t-lg shadow-lg z-50 select-none",N?"inset-4":"w-80",R&&"cursor-ns-resize"),style:N?{}:{bottom:"".concat(j.bottom,"px"),right:"".concat(j.right,"px"),height:"".concat(F,"px")},children:[!N&&(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1 cursor-ns-resize hover:bg-primary/20 transition-colors",onMouseDown:q}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-[#3d405b] text-white rounded-t-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 min-w-0",children:[(0,a.jsxs)(c.eu,{className:"h-8 w-8",children:[(null==(t=B.avatar)?void 0:t.url)&&(0,a.jsx)(c.BK,{src:B.avatar.url,alt:X()}),(0,a.jsx)(c.q5,{children:V()})]}),(0,a.jsxs)("div",{className:"min-w-0",children:[(0,a.jsx)("h3",{className:"font-medium truncate text-white",children:X()}),(0,a.jsx)("p",{className:"text-xs text-white/80 truncate",children:D?"Active now":"Offline"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(m.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]",onClick:w,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}),N?(0,a.jsx)(m.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]",onClick:w,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}):(0,a.jsx)(m.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]",onClick:C,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})}),(0,a.jsx)(m.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]",onClick:b,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{ref:O,className:"flex-1 overflow-y-auto p-4 space-y-4",style:{height:"".concat(F-140,"px")},children:[E?(0,a.jsx)("div",{className:"flex justify-center items-center h-full",children:(0,a.jsx)("div",{className:"text-gray-500",children:"Loading messages..."})}):0===_.length?(0,a.jsx)("div",{className:"flex justify-center items-center h-full",children:(0,a.jsxs)("div",{className:"text-gray-500 text-center",children:[(0,a.jsx)("p",{children:"No messages yet"}),(0,a.jsxs)("p",{className:"text-sm",children:["Start a conversation with ",X()]})]})}):(()=>{let e={};return _.forEach(t=>{let s=new Date(t.timestamp).toDateString();e[s]||(e[s]=[]),e[s].push(t)}),Object.entries(e).map(e=>{let[t,s]=e;return{date:W(new Date(t).toISOString()),messages:s}}).sort((e,t)=>{var s,a;return"Today"===e.date?1:"Today"===t.date?-1:"Yesterday"===e.date?1:"Yesterday"===t.date?-1:new Date((null==(s=e.messages[0])?void 0:s.timestamp)||0).getTime()-new Date((null==(a=t.messages[0])?void 0:a.timestamp)||0).getTime()})})().map((e,t)=>(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("span",{className:"bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full",children:e.date})}),e.messages.map((t,s)=>{var r,n,i,l;let o=t.senderId!==K,d=!o&&(s===e.messages.length-1||(null==(r=e.messages[s+1])?void 0:r.senderId)!==t.senderId),m=0===s||(null==(n=e.messages[s-1])?void 0:n.senderId)!==t.senderId,u=s===e.messages.length-1||(null==(i=e.messages[s+1])?void 0:i.senderId)!==t.senderId;return(0,a.jsxs)("div",{className:(0,x.cn)("flex items-end space-x-2",o?"justify-end":"justify-start",!u&&"mb-2"),children:[!o&&(0,a.jsxs)(c.eu,{className:(0,x.cn)("h-6 w-6",d?"opacity-100":"opacity-0"),children:[(null==(l=B.avatar)?void 0:l.url)&&(0,a.jsx)(c.BK,{src:B.avatar.url,alt:X()}),(0,a.jsx)(c.q5,{className:"text-xs",children:V()})]}),(0,a.jsxs)("div",{className:(0,x.cn)("max-w-xs px-3 py-2 relative",o?m||u?"rounded-t-lg rounded-bl-lg":"rounded-t-lg":m||u?"rounded-t-lg rounded-br-lg":"rounded-t-lg",o?"bg-primary/30 text-primary-foreground":"bg-gray-100 text-dark-text"),children:[(0,a.jsx)("p",{className:"text-sm",children:t.content}),(0,a.jsx)("p",{className:(0,x.cn)("text-xs mt-1",o?"text-primary-foreground/80":"text-gray-500"),children:H(t.timestamp)})]})]},t.id)})]},t)),z&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(c.eu,{className:"h-6 w-6",children:[(null==(s=B.avatar)?void 0:s.url)&&(0,a.jsx)(c.BK,{src:B.avatar.url,alt:X()}),(0,a.jsx)(c.q5,{className:"text-xs",children:V()})]}),(0,a.jsx)("div",{className:"bg-gray-100 px-3 py-2 rounded-lg",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 p-3",children:(0,a.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(u.T,{value:S,onChange:e=>M(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey?G():(e.preventDefault(),P())},placeholder:"Message ".concat(X(),"..."),className:"min-h-[36px] max-h-24 resize-none rounded-full border-gray-300 px-4 py-2 focus:border-primary focus:ring-primary",rows:1})}),(0,a.jsx)(m.$,{onClick:P,disabled:""===S.trim(),size:"sm",className:"h-9 w-9 p-0 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 border-2 border-[#5cc8ff] rounded-full",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})]})})]})}},84405:(e,t,s)=>{s.d(t,{MessageList:()=>y});var a=s(95155),r=s(12115),n=s(13052),i=s(84616),l=s(54416),o=s(47924),d=s(9428),c=s(66766),m=s(91394),u=s(30285),x=s(62523),f=s(38382),g=s(26126),h=s(17313),p=s(54165),v=s(48140),j=s(93945);function y(e){let{isOpen:t,onClose:s,onOpenChat:y}=e,[N,b]=(0,r.useState)([]),[w,C]=(0,r.useState)(""),[_,k]=(0,r.useState)(!1),[S,M]=(0,r.useState)(null),[E,I]=(0,r.useState)(!1),{connections:z,loading:A}=(0,j.n5)(),D=e=>e.firstName&&e.lastName?"".concat(e.firstName," ").concat(e.lastName):e.username,T=e=>e.firstName&&e.lastName?"".concat(e.firstName.charAt(0)).concat(e.lastName.charAt(0)):e.username.substring(0,2).toUpperCase(),F=e=>{let t=new Date(e),s=(new Date().getTime()-t.getTime())/36e5;if(s<1){let e=Math.floor(60*s);return e<=1?"Just now":"".concat(e,"m ago")}{if(s<24)return"".concat(Math.floor(s),"h ago");if(s<48)return"Yesterday";let e=Math.floor(s/24);return"".concat(e,"d ago")}};(0,r.useEffect)(()=>{t&&L()},[t]);let L=async()=>{k(!0),M(null);try{let e=await v.J.getConversations();b(e)}catch(e){M(e instanceof Error?e.message:"Failed to fetch conversations"),console.error("Failed to fetch conversations:",e)}finally{k(!1)}},R=N.filter(e=>{let t=e.participants[0];return!!t&&D(t).toLowerCase().includes(w.toLowerCase())}),U=R.filter(e=>e.unreadCount>0),O=async e=>{let t=N.find(t=>t.id===e);if(t)try{let s=t.participants[0];if(!s)return;let a=parseInt(s.id);await v.J.markMessagesAsRead(a),b(t=>t.map(t=>t.id===e?{...t,unreadCount:0}:t))}catch(e){console.error("Failed to mark messages as read:",e)}},J=e=>{O(e.id),y(e)},Y=e=>{let t=N.find(t=>t.participants.some(t=>t.id===e.user_id.toString()));if(t)J(t);else{var s,a;y({id:e.user_id.toString(),participants:[{id:e.user_id.toString(),username:e.username||e.slug||"user-".concat(e.user_id),firstName:(null==(s=e.name)?void 0:s.split(" ")[0])||null,lastName:(null==(a=e.name)?void 0:a.split(" ").slice(1).join(" "))||null,avatar:e.profile_picture?"string"==typeof e.profile_picture?{url:e.profile_picture}:e.profile_picture.url?{url:e.profile_picture.url}:null:e.avatar?{url:e.avatar}:null,isOnline:!1}],unreadCount:0})}I(!1)},$=e=>{var t;let s=e.participants[0];if(!s)return null;let r=D(s),i=T(s),l=e.lastMessage;return(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0",onClick:()=>J(e),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(m.eu,{className:"h-12 w-12",children:[(null==(t=s.avatar)?void 0:t.url)&&(0,a.jsx)(m.BK,{src:s.avatar.url,alt:r}),(0,a.jsx)(m.q5,{className:"bg-blue-100 text-blue-600 font-medium",children:i})]}),s.isOnline&&(0,a.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 border-2 border-white rounded-full"})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("h3",{className:"font-medium text-dark-text truncate",children:r}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[l&&(0,a.jsx)("span",{className:"text-xs text-gray-500",children:F(l.timestamp)}),e.unreadCount>0&&(0,a.jsx)(g.E,{variant:"destructive",className:"h-5 min-w-[20px] text-xs",children:e.unreadCount>9?"9+":e.unreadCount})]})]}),l&&(0,a.jsx)("p",{className:"text-sm text-gray-600 truncate",children:l.content})]}),(0,a.jsx)(n.A,{className:"h-4 w-4 text-gray-400"})]},e.id)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.cj,{open:t,onOpenChange:s,children:(0,a.jsxs)(f.h,{side:"right",className:"w-full sm:w-96 p-0",hideOverlay:!0,children:[(0,a.jsx)(f.Fm,{className:"p-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(f.qp,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.default,{src:"/images/icons/icon-messages.svg",alt:"Messages",width:20,height:20,className:"h-5 w-5"}),"Messages"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>I(!0),title:"Start new conversation",children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}),(0,a.jsx)(f.kN,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})})]})]})}),(0,a.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(x.p,{placeholder:"Search conversations...",value:w,onChange:e=>C(e.target.value),className:"pl-10"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-hidden",children:_?(0,a.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Loading conversations..."})}):S?(0,a.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,a.jsx)("div",{className:"text-sm text-red-500",children:S})}):0===R.length?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-32 text-center p-4",children:[(0,a.jsx)(c.default,{src:"/images/icons/icon-messages.svg",alt:"Messages",width:32,height:32,className:"h-8 w-8 text-gray-400 mb-2"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:w?"No conversations found":"No messages yet"}),!w&&(0,a.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Start a conversation with someone!"})]}):(0,a.jsxs)(h.tU,{defaultValue:"all",className:"h-full",children:[(0,a.jsxs)(h.j7,{className:"grid w-full grid-cols-2 mx-4 mt-2",children:[(0,a.jsxs)(h.Xi,{value:"all",className:"text-xs",children:["All (",R.length,")"]}),(0,a.jsxs)(h.Xi,{value:"unread",className:"text-xs",children:["Unread (",U.length,")"]})]}),(0,a.jsx)(h.av,{value:"all",className:"mt-2 h-full overflow-y-auto",children:(0,a.jsx)("div",{className:"space-y-0",children:R.map($)})}),(0,a.jsx)(h.av,{value:"unread",className:"mt-2 h-full overflow-y-auto",children:0===U.length?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-32 text-center p-4",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"No unread messages"}),(0,a.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"You're all caught up!"})]}):(0,a.jsx)("div",{className:"space-y-0",children:U.map($)})})]})})]})}),(0,a.jsx)(p.lG,{open:E,onOpenChange:I,children:(0,a.jsxs)(p.Cf,{className:"sm:max-w-md",children:[(0,a.jsx)(p.c7,{children:(0,a.jsx)(p.L3,{children:"Start a conversation"})}),(0,a.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:A?(0,a.jsx)("div",{className:"py-8 text-center text-sm text-gray-500",children:"Loading connections..."}):0===z.length?(0,a.jsx)("div",{className:"py-8 text-center text-sm text-gray-500",children:"No connections yet. Connect with other members to start messaging."}):z.map(e=>{var t;let s=e.name||e.display_name||e.username||"Unknown",r=("string"==typeof e.profile_picture?e.profile_picture:null==(t=e.profile_picture)?void 0:t.url)||e.avatar||"/images/avatar-placeholder.svg";return(0,a.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left",onClick:()=>Y(e),children:[(0,a.jsxs)(m.eu,{className:"h-10 w-10",children:[(0,a.jsx)(m.BK,{src:r,alt:s}),(0,a.jsx)(m.q5,{children:s.substring(0,2).toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"font-medium text-sm truncate",children:s}),(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.job_title||"Member"})]}),(0,a.jsx)(n.A,{className:"h-4 w-4 text-gray-400"})]},e.user_id)})})]})})]})}},90629:(e,t,s)=>{s.d(t,{I$:()=>a.useMessaging}),s(84405),s(77650);var a=s(31807)}}]);