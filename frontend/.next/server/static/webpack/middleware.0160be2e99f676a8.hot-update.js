"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/api/server.js\");\n\nfunction middleware(request) {\n    // Force HTTPS redirect (except for localhost development)\n    const isLocalhost = request.nextUrl.hostname === 'localhost' || request.nextUrl.hostname === '127.0.0.1' || request.nextUrl.hostname.endsWith('.local');\n    if (!isLocalhost && request.nextUrl.protocol === 'http:') {\n        const httpsUrl = new URL(request.url);\n        httpsUrl.protocol = 'https:';\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(httpsUrl, 301); // Permanent redirect\n    }\n    // Check for WordPress authentication cookies\n    const wordpressLoggedInCookie = Array.from(request.cookies.getAll()).find((cookie)=>cookie.name.startsWith('wordpress_logged_in_'));\n    // Check if user is authenticated by WordPress cookie\n    const isAuthenticated = !!wordpressLoggedInCookie;\n    const { pathname } = request.nextUrl;\n    // Define protected paths that require authentication\n    // NOTE: We're using exact path for /profile to only protect the user's own profile page\n    const exactProtectedPaths = [\n        '/profile'\n    ]; // Exact matches\n    const protectedPathPrefixes = [\n        '/settings',\n        '/dashboard'\n    ]; // Prefix matches\n    // Check if accessing the exact /profile path (user's own profile)\n    const isExactProtectedPath = exactProtectedPaths.includes(pathname);\n    // Check if accessing other protected paths that use prefix matching\n    const isPrefixProtectedPath = protectedPathPrefixes.some((path)=>pathname.startsWith(path));\n    // Combined check for any protected path\n    const isAccessingProtectedPath = isExactProtectedPath || isPrefixProtectedPath;\n    // Only redirect if trying to access a protected path AND not authenticated\n    if (isAccessingProtectedPath && !isAuthenticated) {\n        // If not authenticated and accessing a protected path, redirect to login\n        const loginUrl = new URL('/login', request.url);\n        // Add a callback URL to return after login\n        loginUrl.searchParams.set('callbackUrl', pathname + request.nextUrl.search);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(loginUrl);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\n// Define which paths the middleware should run on\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public assets (e.g., /images, /fonts)\n     */ '/((?!api|_next/static|_next/image|favicon.ico|images|fonts|svg|manifest.json|robots.txt|sw.js|workbox-.*\\\\.js).*)'\n    ]\n}; // Example placeholder for token validation function (implement as needed)\n // async function validateToken(token: string): Promise<boolean> {\n //   try {\n //     const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;\n //     if (!WORDPRESS_API_URL) return false;\n //\n //     const response = await fetch(`${WORDPRESS_API_URL}/wp-json/jwt-auth/v1/token/validate`, {\n //       method: 'POST',\n //       headers: {\n //         'Authorization': `Bearer ${token}`,\n //       },\n //     });\n //     return response.ok; // Or check for specific data in response.json()\n //   } catch (error) {\n //     console.error('Token validation error:', error);\n //     return false;\n //   }\n // }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});