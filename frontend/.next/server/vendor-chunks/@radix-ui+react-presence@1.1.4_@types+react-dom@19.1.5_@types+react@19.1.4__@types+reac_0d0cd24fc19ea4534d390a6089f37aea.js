"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-presence@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.4__@types+reac_0d0cd24fc19ea4534d390a6089f37aea";
exports.ids = ["vendor-chunks/@radix-ui+react-presence@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.4__@types+reac_0d0cd24fc19ea4534d390a6089f37aea"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.4__@types+reac_0d0cd24fc19ea4534d390a6089f37aea/node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.4__@types+reac_0d0cd24fc19ea4534d390a6089f37aea/node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePresence.useEffect\": ()=>{\n            const currentAnimationName = getAnimationName(stylesRef.current);\n            prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n        }\n    }[\"usePresence.useEffect\"], [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            const styles = stylesRef.current;\n            const wasPresent = prevPresentRef.current;\n            const hasPresentChanged = wasPresent !== present;\n            if (hasPresentChanged) {\n                const prevAnimationName = prevAnimationNameRef.current;\n                const currentAnimationName = getAnimationName(styles);\n                if (present) {\n                    send(\"MOUNT\");\n                } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                    send(\"UNMOUNT\");\n                } else {\n                    const isAnimating = prevAnimationName !== currentAnimationName;\n                    if (wasPresent && isAnimating) {\n                        send(\"ANIMATION_OUT\");\n                    } else {\n                        send(\"UNMOUNT\");\n                    }\n                }\n                prevPresentRef.current = present;\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            if (node) {\n                let timeoutId;\n                const ownerWindow = node.ownerDocument.defaultView ?? window;\n                const handleAnimationEnd = {\n                    \"usePresence.useLayoutEffect.handleAnimationEnd\": (event)=>{\n                        const currentAnimationName = getAnimationName(stylesRef.current);\n                        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                        if (event.target === node && isCurrentAnimation) {\n                            send(\"ANIMATION_END\");\n                            if (!prevPresentRef.current) {\n                                const currentFillMode = node.style.animationFillMode;\n                                node.style.animationFillMode = \"forwards\";\n                                timeoutId = ownerWindow.setTimeout({\n                                    \"usePresence.useLayoutEffect.handleAnimationEnd\": ()=>{\n                                        if (node.style.animationFillMode === \"forwards\") {\n                                            node.style.animationFillMode = currentFillMode;\n                                        }\n                                    }\n                                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"]);\n                            }\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"];\n                const handleAnimationStart = {\n                    \"usePresence.useLayoutEffect.handleAnimationStart\": (event)=>{\n                        if (event.target === node) {\n                            prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationStart\"];\n                node.addEventListener(\"animationstart\", handleAnimationStart);\n                node.addEventListener(\"animationcancel\", handleAnimationEnd);\n                node.addEventListener(\"animationend\", handleAnimationEnd);\n                return ({\n                    \"usePresence.useLayoutEffect\": ()=>{\n                        ownerWindow.clearTimeout(timeoutId);\n                        node.removeEventListener(\"animationstart\", handleAnimationStart);\n                        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                        node.removeEventListener(\"animationend\", handleAnimationEnd);\n                    }\n                })[\"usePresence.useLayoutEffect\"];\n            } else {\n                send(\"ANIMATION_END\");\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"usePresence.useCallback\": (node2)=>{\n                stylesRef.current = node2 ? getComputedStyle(node2) : null;\n                setNode(node2);\n            }\n        }[\"usePresence.useCallback\"], [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.4__@types+reac_0d0cd24fc19ea4534d390a6089f37aea/node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ })

};
;