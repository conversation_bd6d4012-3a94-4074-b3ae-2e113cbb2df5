"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3259],{4229:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5845:(e,t,r)=>{r.d(t,{i:()=>u});var n,o=r(12115),i=r(52712),l=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function u({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,u,a]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),i=o.useRef(r),u=o.useRef(t);return l(()=>{u.current=t},[t]),o.useEffect(()=>{i.current!==r&&(u.current?.(r),i.current=r)},[r,i]),[r,n,u]}({defaultProp:t,onChange:r}),s=void 0!==e,c=s?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,n])}return[c,o.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else u(t)},[s,e,u,a])]}Symbol("RADIX:SYNC_STATE")},6101:(e,t,r)=>{r.d(t,{s:()=>l,t:()=>i});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},19946:(e,t,r)=>{r.d(t,{A:()=>f});var n=r(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},u=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},a=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:c="",children:f,iconNode:d,...m}=e;return(0,n.createElement)("svg",{ref:t,...s,width:o,height:o,stroke:r,strokeWidth:l?24*Number(i)/Number(o):i,className:u("lucide",c),...!f&&!a(m)&&{"aria-hidden":"true"},...m},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(f)?f:[f]])}),f=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:a,...s}=r;return(0,n.createElement)(c,{ref:i,iconNode:t,className:u("lucide-".concat(o(l(e))),"lucide-".concat(e),a),...s})});return r.displayName=l(e),r}},28905:(e,t,r)=>{r.d(t,{C:()=>l});var n=r(12115),o=r(6101),i=r(52712),l=e=>{let{present:t,children:r}=e,l=function(e){var t,r;let[o,l]=n.useState(),a=n.useRef(null),s=n.useRef(e),c=n.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=u(a.current);c.current="mounted"===f?e:"none"},[f]),(0,i.N)(()=>{let t=a.current,r=s.current;if(r!==e){let n=c.current,o=u(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),(0,i.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=u(a.current).includes(e.animationName);if(e.target===o&&n&&(d("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=u(a.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:n.useCallback(e=>{a.current=e?getComputedStyle(e):null,l(e)},[])}}(t),a="function"==typeof r?r({present:l.isPresent}):n.Children.only(r),s=(0,o.s)(l.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||l.isPresent?n.cloneElement(a,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},39033:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(12115);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,r)=>{r.d(t,{A:()=>l,q:()=>i});var n=r(12115),o=r(95155);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,l=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(r.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let l=n.createContext(i),u=r.length;r=[...r,i];let a=t=>{let{scope:r,children:i,...a}=t,s=r?.[e]?.[u]||l,c=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(s.Provider,{value:c,children:i})};return a.displayName=t+"Provider",[a,function(r,o){let a=o?.[e]?.[u]||l,s=n.useContext(a);if(s)return s;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},51154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52712:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(12115),o=globalThis?.document?n.useLayoutEffect:()=>{}},61285:(e,t,r)=>{r.d(t,{B:()=>a});var n,o=r(12115),i=r(52712),l=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,r]=o.useState(l());return(0,i.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,r)=>{r.d(t,{hO:()=>a,sG:()=>u});var n=r(12115),o=r(47650),i=r(99708),l=r(95155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},89196:(e,t,r)=>{r.d(t,{RG:()=>b,bL:()=>M,q7:()=>_});var n=r(12115),o=r(85185),i=r(37328),l=r(6101),u=r(46081),a=r(61285),s=r(63655),c=r(39033),f=r(5845),d=r(94315),m=r(95155),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,y,g]=(0,i.N)(h),[N,b]=(0,u.A)(h,[g]),[E,A]=N(h),R=n.forwardRef((e,t)=>(0,m.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(x,{...e,ref:t})})}));R.displayName=h;var x=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:u=!1,dir:a,currentTabStopId:w,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:N,onEntryFocus:b,preventScrollOnEntryFocus:A=!1,...R}=e,x=n.useRef(null),C=(0,l.s)(t,x),T=(0,d.jH)(a),[S,M]=(0,f.i)({prop:w,defaultProp:null!=g?g:null,onChange:N,caller:h}),[_,O]=n.useState(!1),j=(0,c.c)(b),k=y(r),D=n.useRef(!1),[F,L]=n.useState(0);return n.useEffect(()=>{let e=x.current;if(e)return e.addEventListener(p,j),()=>e.removeEventListener(p,j)},[j]),(0,m.jsx)(E,{scope:r,orientation:i,dir:T,loop:u,currentTabStopId:S,onItemFocus:n.useCallback(e=>M(e),[M]),onItemShiftTab:n.useCallback(()=>O(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,m.jsx)(s.sG.div,{tabIndex:_||0===F?-1:0,"data-orientation":i,...R,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!_){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),A)}}D.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>O(!1))})})}),C="RovingFocusGroupItem",T=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:l=!1,tabStopId:u,children:c,...f}=e,d=(0,a.B)(),p=u||d,v=A(C,r),h=v.currentTabStopId===p,g=y(r),{onFocusableItemAdd:N,onFocusableItemRemove:b,currentTabStopId:E}=v;return n.useEffect(()=>{if(i)return N(),()=>b()},[i,N,b]),(0,m.jsx)(w.ItemSlot,{scope:r,id:p,focusable:i,active:l,children:(0,m.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return S[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>I(r))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=E}):c})})});T.displayName=C;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var M=R,_=T},99708:(e,t,r)=>{r.d(t,{DX:()=>u,Dc:()=>s,TL:()=>l});var n=r(12115),o=r(6101),i=r(95155);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,u,a=(l=r,(u=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(u=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,o.t)(t,a):a),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,u=n.Children.toArray(o),a=u.find(c);if(a){let e=a.props.children,o=u.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var u=l("Slot"),a=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}}]);