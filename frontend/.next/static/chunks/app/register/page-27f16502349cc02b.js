(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{35695:(e,s,a)=>{"use strict";var t=a(18999);a.o(t,"notFound")&&a.d(s,{notFound:function(){return t.notFound}}),a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},48856:(e,s,a)=>{Promise.resolve().then(a.bind(a,86616))},86616:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var t=a(95155),r=a(12115),o=a(35695);function n(){let[e,s]=(0,r.useState)(""),[a,n]=(0,r.useState)(""),[i,l]=(0,r.useState)(""),[d,u]=(0,r.useState)(""),[c,m]=(0,r.useState)(""),[g,f]=(0,r.useState)(!0),[h,p]=(0,r.useState)(null),[x,b]=(0,r.useState)(null),[y,N]=(0,r.useState)(!1),w=(0,o.useRouter)(),j=async t=>{if(t.preventDefault(),N(!0),p(null),b(null),!e||!a||!i||!d||!c){p("All fields are required."),N(!1);return}try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:e,lastName:a,username:i,email:d,password:c,newsletterOptIn:g})}),r=await t.json();if(t.ok){b(r.message||"Registration successful! Logging you in...");try{await new Promise(e=>setTimeout(e,1500));let e=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:i,password:c})}),a=await e.json();console.log("Auto-login response:",e.status,a),e.ok?(b("Registration successful! Welcome to TourismIQ!"),setTimeout(()=>{window.location.href="/"},1e3)):(console.error("Auto-login failed:",a),b("Account created successfully! Please log in manually."),s(""),n(""),l(""),u(""),m(""),setTimeout(()=>{w.push("/login")},2e3))}catch(e){console.error("Auto-login error:",e),b("Account created successfully! Please log in manually."),setTimeout(()=>{w.push("/login")},2e3)}}else p(r.message||"Registration failed. Please try again.")}catch(e){console.error("Registration submission error:",e),p("An unexpected error occurred. Please try again.")}finally{N(!1)}};return(0,t.jsx)("div",{className:"container mx-auto pt-12 pb-8",children:(0,t.jsxs)("div",{className:"w-full max-w-md mx-auto space-y-6 p-8 border bg-white rounded-lg shadow-sm",children:[(0,t.jsxs)("div",{className:"space-y-2 text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Create your account"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Enter your details to get started"})]}),h&&(0,t.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 text-red-700 rounded-md text-sm",children:h}),x&&(0,t.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 text-green-700 rounded-md text-sm",children:x}),(0,t.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"text-sm font-medium",children:"First Name"}),(0,t.jsx)("input",{id:"firstName",name:"firstName",type:"text",autoComplete:"given-name",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",value:e,onChange:e=>s(e.target.value),disabled:y})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"text-sm font-medium",children:"Last Name"}),(0,t.jsx)("input",{id:"lastName",name:"lastName",type:"text",autoComplete:"family-name",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",value:a,onChange:e=>n(e.target.value),disabled:y})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"username",className:"text-sm font-medium",children:"Username"}),(0,t.jsx)("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",value:i,onChange:e=>l(e.target.value),disabled:y})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"email",className:"text-sm font-medium",children:"Email address"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",value:d,onChange:e=>u(e.target.value),disabled:y})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"password",className:"text-sm font-medium",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",value:c,onChange:e=>m(e.target.value),disabled:y})]}),(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("input",{id:"newsletter",name:"newsletter",type:"checkbox",className:"mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",checked:g,onChange:e=>f(e.target.checked),disabled:y}),(0,t.jsx)("label",{htmlFor:"newsletter",className:"ml-2 block text-sm text-gray-700",children:"Stay in the loop with job alerts, industry insights, and new tools. Want occasional email updates from us?"})]})}),(0,t.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border-2 border-[#5cc8ff] rounded-md shadow-sm text-sm font-extrabold text-[#3d405b] bg-[#5cc8ff] hover:bg-[#5cc8ff]/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#5cc8ff] disabled:opacity-50",disabled:y,children:y?"Registering...":"Register"})]}),(0,t.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Already have an account?"," ",(0,t.jsx)("a",{href:"/login",className:"text-indigo-600 hover:text-indigo-500",children:"Sign in"})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(48856)),_N_E=e.O()}]);