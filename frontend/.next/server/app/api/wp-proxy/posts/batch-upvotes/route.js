/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wp-proxy/posts/batch-upvotes/route";
exports.ids = ["app/api/wp-proxy/posts/batch-upvotes/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute&page=%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute&page=%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_posts_batch_upvotes_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wp-proxy/posts/batch-upvotes/route.ts */ \"(rsc)/./src/app/api/wp-proxy/posts/batch-upvotes/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wp-proxy/posts/batch-upvotes/route\",\n        pathname: \"/api/wp-proxy/posts/batch-upvotes\",\n        filename: \"route\",\n        bundlePath: \"app/api/wp-proxy/posts/batch-upvotes/route\"\n    },\n    resolvedPagePath: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/api/wp-proxy/posts/batch-upvotes/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_posts_batch_upvotes_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute&page=%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wp-proxy/posts/batch-upvotes/route.ts":
/*!***********************************************************!*\
  !*** ./src/app/api/wp-proxy/posts/batch-upvotes/route.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/headers.js\");\n\n\nasync function POST(request) {\n    try {\n        // Parse the request body to get the post IDs\n        const { postIds } = await request.json();\n        if (!Array.isArray(postIds) || postIds.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request. Expected an array of post IDs'\n            }, {\n                status: 400\n            });\n        }\n        // Limit the number of posts that can be fetched at once\n        const limitedPostIds = postIds.slice(0, 50);\n        // Get cookies for authentication\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        const allCookies = cookieStore.getAll();\n        // Build cookie header string\n        const cookieHeader = allCookies.map((cookie)=>`${cookie.name}=${cookie.value}`).join('; ');\n        // Get WordPress URL from env or use default\n        const WORDPRESS_API_URL = \"http://mytourismiq.wpenginepowered.com/\" || 0;\n        // Prepare headers for WordPress request\n        const headers = {\n            Cookie: cookieHeader,\n            'Content-Type': 'application/json'\n        };\n        // Forward request to WordPress REST API\n        // Note: We need to create a corresponding WordPress endpoint that accepts batch requests\n        const response = await fetch(`${WORDPRESS_API_URL}/wp-json/tourismiq/v1/posts/batch-upvotes`, {\n            method: 'POST',\n            headers,\n            body: JSON.stringify({\n                postIds: limitedPostIds\n            })\n        });\n        // Handle response errors\n        if (!response.ok) {\n            // If the WordPress endpoint doesn't exist yet, fall back to individual requests\n            if (response.status === 404) {\n                const results = await Promise.all(limitedPostIds.map(async (postId)=>{\n                    try {\n                        const res = await fetch(`${WORDPRESS_API_URL}/wp-json/tourismiq/v1/posts/${postId}/upvotes`, {\n                            headers\n                        });\n                        if (res.ok) {\n                            const data = await res.json();\n                            return {\n                                postId,\n                                ...data\n                            };\n                        }\n                        return {\n                            postId,\n                            upvoted: false,\n                            count: 0,\n                            error: `Status ${res.status}`\n                        };\n                    } catch (err) {\n                        return {\n                            postId,\n                            upvoted: false,\n                            count: 0,\n                            error: String(err)\n                        };\n                    }\n                }));\n                // Transform results into an object keyed by post ID\n                const upvotesData = results.reduce((acc, result)=>{\n                    acc[result.postId] = {\n                        upvoted: result.upvoted,\n                        count: result.count\n                    };\n                    return acc;\n                }, {});\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(upvotesData);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `WordPress API returned ${response.status}`\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        // Return the response data\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error('Batch Upvotes API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch batch upvote status',\n            details: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wp-proxy/posts/batch-upvotes/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute&page=%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fposts%2Fbatch-upvotes%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();