/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wp-proxy/iq-score/[userId]/route";
exports.ids = ["app/api/wp-proxy/iq-score/[userId]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute&page=%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute&page=%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_iq_score_userId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wp-proxy/iq-score/[userId]/route.ts */ \"(rsc)/./src/app/api/wp-proxy/iq-score/[userId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wp-proxy/iq-score/[userId]/route\",\n        pathname: \"/api/wp-proxy/iq-score/[userId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/wp-proxy/iq-score/[userId]/route\"\n    },\n    resolvedPagePath: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/api/wp-proxy/iq-score/[userId]/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_iq_score_userId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute&page=%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wp-proxy/iq-score/[userId]/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/wp-proxy/iq-score/[userId]/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/headers.js\");\n\n\n// Add timeout utility\nconst fetchWithTimeout = async (url, options, timeout = 10000)=>{\n    const controller = new AbortController();\n    const id = setTimeout(()=>controller.abort(), timeout);\n    try {\n        const response = await fetch(url, {\n            ...options,\n            signal: controller.signal\n        });\n        clearTimeout(id);\n        return response;\n    } catch (error) {\n        clearTimeout(id);\n        throw error;\n    }\n};\nasync function GET(request, { params }) {\n    const startTime = Date.now();\n    let resolvedParams;\n    try {\n        // Await params in Next.js 15 with error handling\n        try {\n            resolvedParams = await params;\n        } catch (paramError) {\n            console.error('Error resolving params:', paramError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request parameters'\n            }, {\n                status: 400\n            });\n        }\n        const { userId } = resolvedParams;\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate userId is a number\n        const userIdNum = parseInt(userId, 10);\n        if (isNaN(userIdNum) || userIdNum <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid user ID format'\n            }, {\n                status: 400\n            });\n        }\n        // Get all cookies for WordPress authentication\n        let cookieHeader = '';\n        try {\n            const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n            const allCookies = cookieStore.getAll();\n            cookieHeader = allCookies.map((cookie)=>`${cookie.name}=${cookie.value}`).join('; ');\n        } catch (cookieError) {\n            console.error('Error reading cookies:', cookieError);\n        // Continue without cookies - API might still work for public data\n        }\n        // Get WordPress API URL\n        const wpApiUrl = \"http://mytourismiq.wpenginepowered.com/\" || 0;\n        const apiUrl = `${wpApiUrl}/wp-json/tourismiq/v1/iq-score/${userIdNum}`;\n        // Forward the request to WordPress with timeout\n        let response;\n        try {\n            response = await fetchWithTimeout(apiUrl, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    Cookie: cookieHeader,\n                    'User-Agent': request.headers.get('user-agent') || '',\n                    'X-Forwarded-For': request.headers.get('x-forwarded-for') || '',\n                    'X-Real-IP': request.headers.get('x-real-ip') || ''\n                }\n            }, 8000); // 8 second timeout\n        } catch (fetchError) {\n            if (fetchError instanceof Error && fetchError.name === 'AbortError') {\n                console.error('WordPress API timeout after 8s:', apiUrl);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Request timeout - WordPress API did not respond in time'\n                }, {\n                    status: 504\n                });\n            }\n            console.error('WordPress API fetch error:', fetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to connect to WordPress API',\n                details: fetchError instanceof Error ? fetchError.message : 'Unknown error'\n            }, {\n                status: 503\n            });\n        }\n        // Try to parse response\n        let data;\n        const responseText = await response.text();\n        try {\n            data = JSON.parse(responseText);\n        } catch  {\n            console.error('Failed to parse WordPress response:', responseText.substring(0, 200));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid response from WordPress API',\n                details: 'Response was not valid JSON'\n            }, {\n                status: 502\n            });\n        }\n        if (!response.ok) {\n            console.error('WordPress API error:', response.status, data);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch user IQ score',\n                details: data\n            }, {\n                status: response.status\n            });\n        }\n        // Add cache headers for successful responses\n        const headers = new Headers();\n        headers.set('Cache-Control', 'private, max-age=60'); // Cache for 1 minute\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            headers\n        });\n    } catch (error) {\n        const duration = Date.now() - startTime;\n        console.error('Unexpected error in IQ score proxy after', duration, 'ms:', {\n            error: error instanceof Error ? error.message : 'Unknown error',\n            stack: error instanceof Error ? error.stack : undefined,\n            userId: resolvedParams?.userId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            message: error instanceof Error ? error.message : 'Unknown error',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wp-proxy/iq-score/[userId]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute&page=%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fiq-score%2F%5BuserId%5D%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();