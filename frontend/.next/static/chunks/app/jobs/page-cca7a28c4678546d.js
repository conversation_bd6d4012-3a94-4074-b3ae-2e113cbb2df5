(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5531],{2557:(e,a,s)=>{Promise.resolve().then(s.bind(s,10953))},10953:(e,a,s)=>{"use strict";s.d(a,{JobsHub:()=>D});var t=s(95155),r=s(12115),l=s(66932),n=s(69074),c=s(85213),i=s(95488),o=s(54653),d=s(15968);async function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;Object.entries(e).forEach(e=>{let[s,t]=e;null!=t&&""!==t&&a.append(s,t.toString())});let s="/api/wp-proxy/jobs/search?".concat(a.toString());try{let a=await fetch(s,{headers:{"Content-Type":"application/json"},credentials:"include"});if(!a.ok){if(404===a.status)return await h(e);throw Error("Failed to search jobs: ".concat(a.status," ").concat(a.statusText))}return a.json()}catch(a){try{return await h(e)}catch(e){throw a}}}async function h(e){let a=new URLSearchParams;a.append("per_page",(e.per_page||20).toString()),a.append("page",(e.page||1).toString()),a.append("orderby",e.orderby||"date"),a.append("order",e.order||"desc"),a.append("_embed","true"),e.search&&a.append("search",e.search);let s="/api/wp-proxy/jobs?".concat(a.toString()),t=await fetch(s,{headers:{"Content-Type":"application/json"},credentials:"include"});if(!t.ok)throw Error("Fallback failed - Failed to fetch jobs: ".concat(t.status," ").concat(t.statusText));let r=await t.json();return{jobs:Array.isArray(r)?r:[],total:r.length||0,pages:1,page:e.page||1,per_page:e.per_page||20}}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;a.append("per_page",(e.per_page||20).toString()),a.append("page",(e.page||1).toString()),e._embed&&a.append("_embed","true"),e.status&&a.append("status",e.status);let s="/api/wp-proxy/jobs?".concat(a.toString()),t=await fetch(s,{headers:{"Content-Type":"application/json"},credentials:"include"});if(!t.ok)throw Error("Failed to fetch jobs: ".concat(t.status));return t.json()}async function g(){let e=await m({per_page:100}),a=[];try{let e=await fetch("/api/wp-proxy/jobs/categories?per_page=100",{headers:{"Content-Type":"application/json"},credentials:"include"});e.ok&&(a=(await e.json()).map(e=>({value:e.slug,label:e.name})))}catch(e){console.error("Failed to fetch job categories:",e),a=[]}let s=[];try{let e=await fetch("/api/wp-proxy/jobs/states?per_page=100",{headers:{"Content-Type":"application/json"},credentials:"include"});e.ok&&(s=(await e.json()).map(e=>e.name).sort())}catch(e){console.error("Failed to fetch job states:",e),s=[]}let t=[];try{let e=await fetch("/api/wp-proxy/jobs/countries?per_page=100",{headers:{"Content-Type":"application/json"},credentials:"include"});e.ok&&(t=(await e.json()).map(e=>e.name).sort())}catch(e){console.error("Failed to fetch job countries:",e),t=[]}return{categories:a,statuses:[{value:"NEW",label:"New"},{value:"ACTIVE",label:"Active"},{value:"EXPIRED",label:"Expired"},{value:"FILLED",label:"Filled"},{value:"DRAFT",label:"Draft"}],countries:t,states:s,organizations:Array.from(new Set(e.map(e=>{var a;return null==(a=e.job_meta)?void 0:a.job_organization}).filter(Boolean))).sort()}}function p(e){if(!e)return"";let a=new Date(e),s=new Date,t=Math.ceil((a.getTime()-s.getTime())/864e5);return 0===t?"Today":1===t?"Tomorrow":t>0&&t<=7?"".concat(t," days left"):a.toLocaleDateString()}s(77281);function b(e){return({NEW:"New",ACTIVE:"Active",EXPIRED:"Expired",FILLED:"Filled",DRAFT:"Draft"})[e]||e}var u=s(23227),j=s(4516),f=s(14186),v=s(33786),N=s(26126),y=s(30285),w=s(66695);function _(e){let{job:a,showFullDescription:s=!1}=e,{title:r,content:l,excerpt:c,date:i,job_meta:o}=a,{job_status:d="",job_organization:x="",job_source_url:h="",job_deadline:m=""}=o||{},g=a.job_categories&&a.job_categories.length>0?a.job_categories[0]:"",_=[a.job_states&&a.job_states.length>0?a.job_states[0]:"",a.job_countries&&a.job_countries.length>0?a.job_countries[0]:""].filter(Boolean).join(", "),k=new Date(i).toLocaleDateString(),E=p(m),C="EXPIRED"===d||"Expired"===E,A=E.includes("day")&&!C,S=()=>{let e=[r.rendered.replace(/<[^>]*>/g,""),x,_,"job"].filter(Boolean).join(" "),a="https://www.google.com/search?q=".concat(encodeURIComponent(e));window.open(a,"_blank")};return(0,t.jsxs)(w.Zp,{className:"group relative flex flex-col h-full transition-all duration-200 hover:shadow-md cursor-pointer ".concat(C?"opacity-75":""),onClick:S,role:"button",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),S())},"aria-label":"Search Google for job: ".concat(r.rendered.replace(/<[^>]*>/g,"")),children:[(0,t.jsx)(w.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"text-xl font-semibold text-gray-900 line-clamp-2 block",dangerouslySetInnerHTML:{__html:r.rendered}}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-sm text-gray-600",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 flex-shrink-0"}),(0,t.jsx)("span",{className:"font-medium",children:x})]}),_&&(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-1 text-sm text-gray-500",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 flex-shrink-0"}),(0,t.jsx)("span",{children:_})]}),g&&(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)(N.E,{variant:"outline",className:"".concat({hospitality:"bg-purple-100 text-purple-800 border-purple-200",travel_agent:"bg-blue-100 text-blue-800 border-blue-200",tour_operator:"bg-indigo-100 text-indigo-800 border-indigo-200",marketing:"bg-pink-100 text-pink-800 border-pink-200",sales:"bg-orange-100 text-orange-800 border-orange-200",technology:"bg-cyan-100 text-cyan-800 border-cyan-200",management:"bg-slate-100 text-slate-800 border-slate-200",finance:"bg-emerald-100 text-emerald-800 border-emerald-200"}[g]||"bg-gray-100 text-gray-800 border-gray-200"," text-sm"),children:g})})]}),(0,t.jsx)("div",{className:"flex flex-col gap-2 items-end",children:d&&(0,t.jsx)(N.E,{className:"".concat((e=>{switch(e){case"NEW":return"bg-[#22c55e] text-white border-[#22c55e]";case"ACTIVE":case"FILLED":return"bg-blue-100 text-blue-800 border-blue-200";case"EXPIRED":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(d)," text-sm"),children:b(d)})})]})}),(0,t.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none z-20",children:[(0,t.jsx)("div",{className:"absolute inset-0 border-2 border-[#5cc8ff] rounded-lg"}),(0,t.jsx)("div",{className:"absolute inset-0 rounded-lg",style:{background:"linear-gradient(135deg, transparent 0%, rgba(92, 200, 255, 0.12) 20%, rgba(92, 200, 255, 0.23) 40%, rgba(92, 200, 255, 0.35) 60%, rgba(92, 200, 255, 0.47) 80%, rgba(92, 200, 255, 0.59) 100%)"}}),(0,t.jsx)("div",{className:"absolute bottom-3 right-3 pointer-events-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-full px-3 py-1.5 flex items-center gap-1.5",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-[#5cc8ff]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})}),(0,t.jsx)("span",{className:"text-xs text-gray-700 font-medium",children:"Search for this job on Google"})]})})]}),(0,t.jsx)(w.Wu,{className:"py-3 flex-1",children:(0,t.jsx)("div",{className:"text-sm text-gray-700 ".concat(s?"":"line-clamp-3"),dangerouslySetInnerHTML:{__html:s?l.rendered:c.rendered}})}),(0,t.jsx)(w.wL,{className:"pt-3 border-t",style:{backgroundColor:"rgba(92, 200, 255, 0.05)"},children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(n.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:["Posted ",k]})]}),m&&(0,t.jsxs)("div",{className:"flex items-center gap-1 ".concat(A?"text-orange-600 font-medium":""),children:[(0,t.jsx)(f.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:E})]})]}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:h&&!C&&(0,t.jsx)(y.$,{size:"sm",asChild:!0,children:(0,t.jsxs)("a",{href:h,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1",children:["Apply Now",(0,t.jsx)(v.A,{className:"h-3 w-3"})]})})})]})})]})}function k(e){let{job:a}=e,{title:s,date:r,job_meta:l}=a,{job_status:c="",job_organization:i="",job_source_url:o="",job_deadline:d=""}=l||{},x=a.job_categories&&a.job_categories.length>0?a.job_categories[0]:"",h=[a.job_states&&a.job_states.length>0?a.job_states[0]:"",a.job_countries&&a.job_countries.length>0?a.job_countries[0]:""].filter(Boolean).join(", "),m=new Date(r).toLocaleDateString(),g=p(d),w="EXPIRED"===c,_=g.includes("day"),k=()=>{let e=[s.rendered.replace(/<[^>]*>/g,""),i,h,"job"].filter(Boolean).join(" "),a="https://www.google.com/search?q=".concat(encodeURIComponent(e));window.open(a,"_blank")};return(0,t.jsxs)("div",{className:"group relative flex flex-col w-full p-4 bg-white border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer ".concat(w?"opacity-75":""),onClick:k,role:"button",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),k())},"aria-label":"Search Google for job: ".concat(s.rendered.replace(/<[^>]*>/g,"")),children:[(0,t.jsxs)("div",{className:"absolute top-3 right-3 z-10 flex gap-1",children:[x&&(0,t.jsx)(N.E,{variant:"outline",className:"".concat({hospitality:"bg-purple-100 text-purple-800 border-purple-200",travel_agent:"bg-blue-100 text-blue-800 border-blue-200",tour_operator:"bg-indigo-100 text-indigo-800 border-indigo-200",marketing:"bg-pink-100 text-pink-800 border-pink-200",sales:"bg-orange-100 text-orange-800 border-orange-200",technology:"bg-cyan-100 text-cyan-800 border-cyan-200",management:"bg-slate-100 text-slate-800 border-slate-200",finance:"bg-teal-100 text-teal-800 border-teal-200"}[x]||"bg-gray-100 text-gray-800 border-gray-200"," text-sm py-0.5 px-2 h-5"),children:x}),c&&"NEW"!==c&&(0,t.jsx)(N.E,{className:"".concat((e=>{switch(e){case"NEW":return"bg-[#22c55e] text-white border-[#22c55e]";case"ACTIVE":case"FILLED":return"bg-blue-100 text-blue-800 border-blue-200";case"EXPIRED":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(c)," text-sm py-0.5 px-2 h-5"),variant:"outline",children:b(c)}),"NEW"===c&&(0,t.jsx)(N.E,{className:"bg-[#22c55e] text-white text-sm py-0.5 px-2 h-5 border-[#22c55e]",children:"New"})]}),(0,t.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none z-20",children:[(0,t.jsx)("div",{className:"absolute inset-0 border-2 border-[#5cc8ff] rounded-lg"}),(0,t.jsx)("div",{className:"absolute inset-0 rounded-lg",style:{background:"linear-gradient(135deg, transparent 0%, rgba(92, 200, 255, 0.12) 20%, rgba(92, 200, 255, 0.23) 40%, rgba(92, 200, 255, 0.35) 60%, rgba(92, 200, 255, 0.47) 80%, rgba(92, 200, 255, 0.59) 100%)"}}),(0,t.jsx)("div",{className:"absolute bottom-3 right-3 pointer-events-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-full px-3 py-1.5 flex items-center gap-1.5",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-[#5cc8ff]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})}),(0,t.jsx)("span",{className:"text-xs text-gray-700 font-medium",children:"Search for this job on Google"})]})})]}),(0,t.jsx)("div",{className:"flex items-start gap-4 flex-1 min-w-0",children:(0,t.jsx)("div",{className:"min-w-0 pl-1 flex-shrink-0",style:{width:"66.67%",maxWidth:"66.67%"},children:(0,t.jsx)("div",{className:"text-base font-semibold text-gray-900 break-words mb-3",dangerouslySetInnerHTML:{__html:s.rendered}})})}),(0,t.jsxs)("div",{className:"flex items-start gap-4 flex-1 min-w-0 pl-1",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-2 text-xs text-gray-500 flex-1",children:[i&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(u.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"break-words",children:i})]}),h&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(j.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"break-words",children:h})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-2 text-xs text-gray-500 flex-shrink-0 pr-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(n.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsxs)("span",{className:"whitespace-nowrap",children:["Posted ",m]})]}),d&&g&&(0,t.jsxs)("div",{className:"flex items-center gap-1 ".concat(_?"text-orange-600 font-medium":""),children:[(0,t.jsx)(f.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsx)("span",{className:"whitespace-nowrap",children:g})]})]})]}),o&&!w&&(0,t.jsx)("div",{className:"flex justify-end mt-3",children:(0,t.jsx)(y.$,{size:"sm",asChild:!0,children:(0,t.jsxs)("a",{href:o,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1",children:["Apply Now",(0,t.jsx)(v.A,{className:"h-3 w-3"})]})})})]})}var E=s(59409),C=s(68856),A=s(10130),S=s(59434);function D(e){let{initialJobs:a=[],initialTotal:s=0}=e,[h,m]=(0,r.useState)(a),[p,b]=(0,r.useState)(s),[u,j]=(0,r.useState)(!1),[f,v]=(0,r.useState)(null),[D,T]=(0,r.useState)(""),[z,I]=(0,r.useState)({per_page:2e3,orderby:"date",order:"desc"}),[L,F]=(0,r.useState)({categories:[],statuses:[],countries:[],states:[],organizations:[]}),[R,P]=(0,r.useState)(0),[V,W]=(0,r.useState)("compact"),[q,B]=(0,r.useState)(!1),O=(0,r.useCallback)(e=>{T(e)},[]);(0,r.useEffect)(()=>{(async()=>{try{let e=await g();F(e)}catch(e){console.error("Failed to load filter options:",e)}})()},[]),(0,r.useEffect)(()=>{let e=Object.entries(z).filter(e=>{let[a,s]=e;return"per_page"!==a&&"orderby"!==a&&"order"!==a&&"page"!==a&&null!=s&&""!==s}).length;D?P(e+1):P(e)},[z,D]);let $=(0,r.useCallback)(async()=>{j(!0),v(null);try{let e={...z,page:1,...D&&{search:D}},a=await x(e);m(a.jobs),b(a.total)}catch(e){v(e instanceof Error?e.message:"Failed to load jobs"),console.error("Error searching jobs:",e)}finally{j(!1)}},[z,D]);(0,r.useEffect)(()=>{$()},[$]);let M=()=>{T(""),I({per_page:2e3,orderby:"status_then_date",order:"desc"})},X=(e,a)=>{I(s=>({...s,[e]:a||void 0}))},Z=(e,a)=>{I(s=>({...s,orderby:e,order:a}))};return(0,t.jsxs)("div",{className:"min-h-screen bg-[#eff1f4]",children:[(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("div",{className:"max-w-[1360px] mx-auto py-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Jobs Hub"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Discover tourism and travel industry opportunities"})]}),(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[p," job",1!==p?"s":""," found"]})})]})})}),(0,t.jsxs)("div",{className:"max-w-[1300px] mx-auto py-6",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(A.A,{onSearch:O,placeholder:"Search by title, organization...",defaultValue:D,inputClassName:"bg-white rounded-full h-12 border border-gray-200 px-6 text-base"})}),(0,t.jsxs)(y.$,{variant:"outline",onClick:()=>B(!q),className:"rounded-full bg-white border-2 border-[#5cc8ff] text-[#3d405b] h-12 px-6 font-semibold flex items-center gap-2 shadow-none",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 text-[#3d405b]"}),"Advanced Search",R>0&&(0,t.jsx)(N.E,{variant:"secondary",className:"text-xs ml-1",children:R})]})]})}),q&&(0,t.jsxs)(w.Zp,{className:"mb-6",children:[(0,t.jsx)(w.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(w.ZB,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"h-5 w-5"}),"Advanced Filters"]}),R>0&&(0,t.jsx)(y.$,{variant:"ghost",size:"sm",onClick:M,className:"text-xs h-auto p-1",children:"Clear All"})]})}),(0,t.jsxs)(w.Wu,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Status"}),(0,t.jsxs)(E.l6,{value:z.status||"",onValueChange:e=>X("status","all"===e?void 0:e),children:[(0,t.jsx)(E.bq,{className:"w-full",children:(0,t.jsx)(E.yv,{placeholder:"All"})}),(0,t.jsxs)(E.gC,{children:[(0,t.jsx)(E.eb,{value:"all",children:"All Statuses"}),L.statuses.map(e=>(0,t.jsx)(E.eb,{value:e.value,children:e.label},e.value))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Category"}),(0,t.jsxs)(E.l6,{value:z.category||"",onValueChange:e=>X("category","all"===e?void 0:e),children:[(0,t.jsx)(E.bq,{className:"w-full",children:(0,t.jsx)(E.yv,{placeholder:"All"})}),(0,t.jsxs)(E.gC,{children:[(0,t.jsx)(E.eb,{value:"all",children:"All Categories"}),L.categories.map(e=>(0,t.jsx)(E.eb,{value:e.value,children:e.label},e.value))]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Country"}),(0,t.jsxs)(E.l6,{value:z.country||"",onValueChange:e=>X("country","all"===e?void 0:e),children:[(0,t.jsx)(E.bq,{className:"w-full",children:(0,t.jsx)(E.yv,{placeholder:"All countries"})}),(0,t.jsxs)(E.gC,{children:[(0,t.jsx)(E.eb,{value:"all",children:"All Countries"}),L.countries.map(e=>(0,t.jsx)(E.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"State/Province"}),(0,t.jsxs)(E.l6,{value:z.state||"",onValueChange:e=>X("state","all"===e?void 0:e),children:[(0,t.jsx)(E.bq,{className:"w-full",children:(0,t.jsx)(E.yv,{placeholder:"All states"})}),(0,t.jsxs)(E.gC,{className:"max-h-[200px] overflow-y-auto",children:[(0,t.jsx)(E.eb,{value:"all",children:"All States"}),L.states.map(e=>(0,t.jsx)(E.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Organization"}),(0,t.jsxs)(E.l6,{value:z.organization||"",onValueChange:e=>X("organization","all"===e?void 0:e),children:[(0,t.jsx)(E.bq,{className:"w-full",children:(0,t.jsx)(E.yv,{placeholder:"All organizations"})}),(0,t.jsxs)(E.gC,{children:[(0,t.jsx)(E.eb,{value:"all",children:"All Organizations"}),L.organizations.map(e=>(0,t.jsx)(E.eb,{value:e,children:e},e))]})]})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Sort by:"}),(0,t.jsxs)(E.l6,{value:"".concat(z.orderby,"-").concat(z.order),onValueChange:e=>{let[a,s]=e.split("-");Z(a||"date",s||"desc")},children:[(0,t.jsx)(E.bq,{className:"w-[180px] bg-white rounded-full border h-12 px-6 font-semibold",children:(0,t.jsx)(E.yv,{})}),(0,t.jsxs)(E.gC,{children:[(0,t.jsx)(E.eb,{value:"date-desc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Newest First"})]})}),(0,t.jsx)(E.eb,{value:"date-asc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Oldest First"})]})}),(0,t.jsx)(E.eb,{value:"status_then_date-desc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Status"})]})}),(0,t.jsx)(E.eb,{value:"title-asc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Title A-Z"})]})}),(0,t.jsx)(E.eb,{value:"title-desc",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Title Z-A"})]})})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center border rounded-lg overflow-hidden",children:[(0,t.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>W("card"),className:(0,S.cn)("rounded-none h-8 px-3 bg-white transition-colors","card"===V&&"bg-[#5cc8ff] !shadow-none","card"===V?"hover:bg-[#5cc8ff]":"hover:bg-[rgba(92,200,255,0.15)]"),children:(0,t.jsx)(o.A,{className:"h-4 w-4 text-[#3d405b]"})}),(0,t.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>W("compact"),className:(0,S.cn)("rounded-none h-8 px-3 bg-white transition-colors","compact"===V&&"bg-[#5cc8ff] !shadow-none","compact"===V?"hover:bg-[#5cc8ff]":"hover:bg-[rgba(92,200,255,0.15)]"),children:(0,t.jsx)(d.A,{className:"h-4 w-4 text-[#3d405b]"})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[f&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:f}),u?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-lg text-gray-600 mb-4",children:"Loading jobs..."}),(0,t.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"})]}),Array.from({length:3}).map((e,a)=>(0,t.jsxs)(w.Zp,{className:"opacity-50",children:[(0,t.jsxs)(w.aR,{children:[(0,t.jsx)(C.E,{className:"h-6 w-3/4"}),(0,t.jsx)(C.E,{className:"h-4 w-1/2"})]}),(0,t.jsxs)(w.Wu,{children:[(0,t.jsx)(C.E,{className:"h-4 w-full mb-2"}),(0,t.jsx)(C.E,{className:"h-4 w-3/4"})]})]},a))]}):0===h.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-500 text-lg mb-2",children:"No jobs found"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search criteria or filters"}),R>0&&(0,t.jsx)(y.$,{variant:"outline",onClick:M,className:"mt-4",children:"Clear all filters"})]}):(0,t.jsxs)("div",{className:"card"===V?"grid grid-cols-1 md:grid-cols-2 gap-6":"space-y-2",children:[h.map(e=>"card"===V?(0,t.jsx)(_,{job:e},e.id):(0,t.jsx)(k,{job:e},e.id)),h.length>0&&(0,t.jsxs)("div",{className:"text-center py-6 text-gray-500 text-sm",children:["Showing all ",h.length," job",1!==h.length?"s":""]})]})]})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[5003,409,8034,870,7884,875,8441,1684,7358],()=>a(2557)),_N_E=e.O()}]);