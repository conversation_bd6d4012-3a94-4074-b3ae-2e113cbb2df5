"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[875],{4516:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},10130:(e,t,r)=>{r.d(t,{A:()=>c});var a=r(95155),s=r(12115),n=r(47924),i=r(54416),l=r(62523),d=r(30285),o=r(59434);function c(e){let{onSearch:t,placeholder:r="Search questions...",defaultValue:c="",inputClassName:p=""}=e,[u,h]=(0,s.useState)(c),[f,g]=(0,s.useState)(!1),v=(0,s.useRef)(!0);return(0,s.useEffect)(()=>{g(!0)},[]),(0,s.useEffect)(()=>{if(!f)return;if(v.current){v.current=!1;return}let e=setTimeout(()=>{t(u)},300);return()=>clearTimeout(e)},[u,t,f]),(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),t(u)},className:"relative",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,a.jsx)(l.p,{type:"text",placeholder:r,value:u,onChange:e=>h(e.target.value),className:(0,o.cn)("!pl-10 pr-10",p)}),u&&(0,a.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>{h("")},className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100",children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})]})})}},14186:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},15968:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},23227:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},26126:(e,t,r)=>{r.d(t,{E:()=>l});var a=r(95155);r(12115);var s=r(74466),n=r(59434);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...s})}},30285:(e,t,r)=>{r.d(t,{$:()=>d,r:()=>l});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...o}=e,c=d?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:n,className:t})),...o})}},33786:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},54653:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},59409:(e,t,r)=>{r.d(t,{bq:()=>p,eb:()=>h,gC:()=>u,l6:()=>o,yv:()=>c});var a=r(95155);r(12115);var s=r(93081),n=r(66474),i=r(5196),l=r(47863),d=r(59434);function o(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,a.jsx)(s.WT,{"data-slot":"select-value",...t})}function p(e){let{className:t,size:r="default",children:i,...l}=e;return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[i,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 text-[#3d405b]"})})]})}function u(e){let{className:t,children:r,position:n="popper",...i}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-[20px] border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,a.jsx)(f,{}),(0,a.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,a.jsx)(g,{})]})})}function h(e){let{className:t,children:r,...n}=e;return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-[rgba(92,200,255,0.15)] focus:text-accent-foreground data-[state=checked]:font-bold [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-full py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:r})]})}function f(e){let{className:t,...r}=e;return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(l.A,{className:"size-4"})})}function g(e){let{className:t,...r}=e;return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"size-4"})})}},59434:(e,t,r)=>{r.d(t,{cn:()=>n});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},62523:(e,t,r)=>{r.d(t,{p:()=>n});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,r)=>{r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>l,wL:()=>p});var a=r(95155),s=r(12115),n=r(59434);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});l.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});d.displayName="CardTitle";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6",r),...s})});c.displayName="CardContent";let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6",r),...s})});p.displayName="CardFooter"},66932:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68856:(e,t,r)=>{r.d(t,{E:()=>n});var a=r(95155),s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-muted",t),...r})}},69074:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},77281:(e,t,r)=>{r.d(t,{QZ:()=>i,iT:()=>l,oc:()=>s,q5:()=>n,tE:()=>a}),r(49509);let a="/api",s=!0;function n(){return"include"}function i(e){return e.replace(/<[^>]*>?/gm,"").replace(/\[.*?\]/g,"").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#0?39;/g,"'").replace(/&#0?38;/g,"&").replace(/&#8217;/g,"'").replace(/&#8211;/g,"–").replace(/&#8212;/g,"—").replace(/&#8216;/g,"'").replace(/&#8220;/g,'"').replace(/&#8221;/g,'"').replace(/&#8230;/g,"…").trim()}function l(e){return e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#0?39;/g,"'").replace(/&#0?38;/g,"&").replace(/&#8217;/g,"'").replace(/&#8211;/g,"–").replace(/&#8212;/g,"—").replace(/&#8216;/g,"'").replace(/&#8220;/g,'"').replace(/&#8221;/g,'"').replace(/&#8230;/g,"…")}},85213:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-up-narrow-wide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]])},95488:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-down-wide-narrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]])}}]);