/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wp-proxy/cache/invalidations/route";
exports.ids = ["app/api/wp-proxy/cache/invalidations/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute&page=%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute&page=%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_cache_invalidations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wp-proxy/cache/invalidations/route.ts */ \"(rsc)/./src/app/api/wp-proxy/cache/invalidations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wp-proxy/cache/invalidations/route\",\n        pathname: \"/api/wp-proxy/cache/invalidations\",\n        filename: \"route\",\n        bundlePath: \"app/api/wp-proxy/cache/invalidations/route\"\n    },\n    resolvedPagePath: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/api/wp-proxy/cache/invalidations/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_cache_invalidations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute&page=%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wp-proxy/cache/invalidations/route.ts":
/*!***********************************************************!*\
  !*** ./src/app/api/wp-proxy/cache/invalidations/route.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n\n/**\n * Proxy handler for cache invalidations\n * Forwards requests to WordPress cache invalidation endpoint\n */ async function GET(request) {\n    try {\n        const wpBaseUrl = \"http://tourismiq-headless.local\" || 0;\n        const apiUrl = `${wpBaseUrl}/wp-json/tourismiq/v1`;\n        // Get query parameters\n        const searchParams = request.nextUrl.searchParams;\n        const since = searchParams.get('since') || '0';\n        // Construct WordPress API URL\n        const wpUrl = `${apiUrl}/cache/invalidations?since=${since}`;\n        // Fetch from WordPress\n        const response = await fetch(wpUrl, {\n            headers: {\n                'Content-Type': 'application/json',\n                Cookie: request.headers.get('cookie') || ''\n            },\n            credentials: 'include'\n        });\n        if (!response.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch invalidations'\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        // Return with no-cache headers to ensure fresh data\n        const nextResponse = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');\n        nextResponse.headers.set('Pragma', 'no-cache');\n        nextResponse.headers.set('Expires', '0');\n        return nextResponse;\n    } catch (error) {\n        console.error('Error in cache invalidations proxy:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wp-proxy/cache/invalidations/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute&page=%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fcache%2Finvalidations%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();