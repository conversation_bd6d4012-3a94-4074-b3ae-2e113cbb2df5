(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3616],{30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,r:()=>o});var r=s(95155);s(12115);var n=s(99708),a=s(74466),i=s(59434);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:a,asChild:l=!1,...d}=e,c=l?n.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:a,className:t})),...d})}},33570:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var r=s(95155),n=s(12115),a=s(30285),i=s(54165),o=s(1243);function l(e){let{isOpen:t,onClose:s,onConfirm:l,title:d,message:c,confirmText:u="Confirm",cancelText:x="Cancel",variant:h="default",loading:f=!1}=e,[m,g]=(0,n.useState)(!1);(0,n.useEffect)(()=>{t||g(!1)},[t]);let p=async()=>{g(!0);try{await l()}finally{g(!1)}},v=f||m;return(0,r.jsx)(i.lG,{open:t,onOpenChange:s,children:(0,r.jsxs)(i.Cf,{className:"sm:max-w-md",children:[(0,r.jsxs)(i.c7,{children:[(0,r.jsxs)(i.L3,{className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-orange-500"}),d]}),(0,r.jsx)(i.rr,{className:"text-left",children:c})]}),(0,r.jsxs)(i.Es,{className:"flex flex-col sm:flex-row gap-2",children:[(0,r.jsx)(a.$,{variant:"outline",onClick:s,disabled:v,className:"w-full sm:w-auto border-2 border-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold",children:x}),(0,r.jsx)(a.$,{onClick:p,disabled:v,className:"w-full sm:w-auto font-extrabold ".concat("destructive"===h?"bg-red-600 hover:bg-red-700 text-white":"bg-[#5cc8ff] hover:bg-[#5cc8ff]/80 text-[#3d405b]"),children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"}),u,"..."]}):u})]})]})})}function d(){let[e,t]=(0,n.useState)({isOpen:!1,title:"",message:"",onConfirm:()=>{}});return{confirm:function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Confirm Action",r=arguments.length>2?arguments[2]:void 0;return new Promise(n=>{t({isOpen:!0,title:s,message:e,confirmText:(null==r?void 0:r.confirmText)||"Confirm",cancelText:(null==r?void 0:r.cancelText)||"Cancel",variant:(null==r?void 0:r.variant)||"default",onConfirm:()=>{t(e=>({...e,isOpen:!1})),n(!0)},onCancel:()=>{t(e=>({...e,isOpen:!1})),n(!1)}})})},ConfirmationDialogComponent:()=>(0,r.jsx)(l,{isOpen:e.isOpen,onClose:()=>{var s;t(e=>({...e,isOpen:!1})),null==(s=e.onCancel)||s.call(e)},onConfirm:e.onConfirm,title:e.title,message:e.message,confirmText:e.confirmText,cancelText:e.cancelText,variant:e.variant})}}},38195:(e,t,s)=>{"use strict";s.d(t,{ErrorBoundary:()=>a});var r=s(95155),n=s(12115);class a extends n.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.props.onError&&this.props.onError(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,r.jsx)("div",{className:"min-h-[200px] flex items-center justify-center p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-dark-text mb-2",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"We're sorry, but something unexpected happened. Please try refreshing the page."}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"w-1/3 px-3 py-2 text-sm font-semibold bg-[#5cc8ff] text-[#3d405b] rounded-full hover:bg-[#5cc8ff]/80 transition-colors",children:"Refresh Page"})}),!1]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}},39638:(e,t,s)=>{Promise.resolve().then(s.bind(s,91602))},54165:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>h,L3:()=>f,c7:()=>x,lG:()=>o,rr:()=>m,zM:()=>l});var r=s(95155);s(12115);var n=s(15452),a=s(54416),i=s(59434);function o(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"dialog",...t})}function l(e){let{...t}=e;return(0,r.jsx)(n.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,r.jsx)(n.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...s}=e;return(0,r.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",t),...s})}function u(e){let{className:t,children:s,...o}=e;return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-[0_32px_65px_-15px_rgba(0,0,0,0.9)] duration-200 sm:max-w-lg",t),...o,children:[s,(0,r.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(a.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...s})}function h(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...s})}function f(e){let{className:t,...s}=e;return(0,r.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...s})}function m(e){let{className:t,...s}=e;return(0,r.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...s})}},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a});var r=s(52596),n=s(39688);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,r.$)(t))}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var r=s(95155),n=s(12115),a=s(59434);let i=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...n})});i.displayName="Card";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...n})});o.displayName="CardHeader";let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});l.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",s),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6",s),...n})});c.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6",s),...n})});u.displayName="CardFooter"},91602:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95155),n=s(12115),a=s(30285),i=s(66695),o=s(38195),l=s(33570);function d(){let[e,t]=(0,n.useState)(!1);if(e)throw Error("This is a test error to demonstrate the error page styling!");return(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"mb-4",children:"Click the button below to trigger an error and see the styled error page:"}),(0,r.jsx)(a.$,{onClick:()=>t(!0),className:"bg-red-600 hover:bg-red-700 text-white",children:"Trigger Error"})]})}function c(){let{confirm:e,ConfirmationDialogComponent:t}=(0,l.Z)(),[s,c]=(0,n.useState)(""),u=async()=>{c(await e("This is a basic confirmation dialog. Do you want to proceed?","Basic Confirmation")?"Confirmed":"Cancelled")},x=async()=>{c(await e("This is a destructive action that cannot be undone. Are you absolutely sure?","Destructive Action",{confirmText:"Delete Permanently",cancelText:"Keep Safe",variant:"destructive"})?"Destructive action confirmed":"Action cancelled")},h=async()=>{c(await e("Would you like to save your changes before continuing?","Save Changes",{confirmText:"Save & Continue",cancelText:"Continue Without Saving"})?"Changes saved":"Continued without saving")};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Error Page & Dialog Styling Test"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Error Page Styling Test"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)(o.ErrorBoundary,{children:(0,r.jsx)(d,{})})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Custom Confirmation Dialogs"})}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Test different types of confirmation dialogs:"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(a.$,{onClick:u,variant:"outline",className:"w-full",children:"Basic Confirmation"}),(0,r.jsx)(a.$,{onClick:x,variant:"outline",className:"w-full border-red-200 text-red-700 hover:bg-red-50",children:"Destructive Confirmation"}),(0,r.jsx)(a.$,{onClick:h,variant:"outline",className:"w-full",children:"Custom Text Confirmation"})]})]}),s&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,r.jsx)("strong",{children:"Last Result:"})," ",s]})})]})]})]}),(0,r.jsxs)(i.Zp,{className:"mt-8",children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"How to Test"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Error Page Styling:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:[(0,r.jsx)("li",{children:'Click "Trigger Error" to see the styled error page'}),(0,r.jsx)("li",{children:'The "Refresh Page" button should match the newsletter signup button styling'}),(0,r.jsx)("li",{children:"Notice the rounded-full design, proper colors, and hover states"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Custom Confirmation Dialogs:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:[(0,r.jsx)("li",{children:"Click any of the confirmation buttons to see custom dialogs"}),(0,r.jsx)("li",{children:"These replace the system's native window.confirm dialogs"}),(0,r.jsx)("li",{children:"Notice the consistent styling with your site's design system"}),(0,r.jsx)("li",{children:"Different variants: default, destructive, and custom text"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Styling Features:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:[(0,r.jsx)("li",{children:"Rounded-full buttons matching your design system"}),(0,r.jsx)("li",{children:"Proper color scheme: #5cc8ff background, #3d405b text"}),(0,r.jsx)("li",{children:"Consistent hover states and transitions"}),(0,r.jsx)("li",{children:"Loading states with spinners"}),(0,r.jsx)("li",{children:"Responsive design that works on all screen sizes"})]})]})]})})]}),(0,r.jsx)(t,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,409,1414,8441,1684,7358],()=>t(39638)),_N_E=e.O()}]);