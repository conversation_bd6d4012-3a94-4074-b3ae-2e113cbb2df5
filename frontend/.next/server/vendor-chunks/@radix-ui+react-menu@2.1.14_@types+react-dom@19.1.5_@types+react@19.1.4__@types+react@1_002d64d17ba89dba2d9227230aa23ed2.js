"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-menu@2.1.14_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@1_002d64d17ba89dba2d9227230aa23ed2";
exports.ids = ["vendor-chunks/@radix-ui+react-menu@2.1.14_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@1_002d64d17ba89dba2d9227230aa23ed2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.14_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@1_002d64d17ba89dba2d9227230aa23ed2/node_modules/@radix-ui/react-menu/dist/index.mjs":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-menu@2.1.14_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@1_002d64d17ba89dba2d9227230aa23ed2/node_modules/@radix-ui/react-menu/dist/index.mjs ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   MenuAnchor: () => (/* binding */ MenuAnchor),\n/* harmony export */   MenuArrow: () => (/* binding */ MenuArrow),\n/* harmony export */   MenuCheckboxItem: () => (/* binding */ MenuCheckboxItem),\n/* harmony export */   MenuContent: () => (/* binding */ MenuContent),\n/* harmony export */   MenuGroup: () => (/* binding */ MenuGroup),\n/* harmony export */   MenuItem: () => (/* binding */ MenuItem),\n/* harmony export */   MenuItemIndicator: () => (/* binding */ MenuItemIndicator),\n/* harmony export */   MenuLabel: () => (/* binding */ MenuLabel),\n/* harmony export */   MenuPortal: () => (/* binding */ MenuPortal),\n/* harmony export */   MenuRadioGroup: () => (/* binding */ MenuRadioGroup),\n/* harmony export */   MenuRadioItem: () => (/* binding */ MenuRadioItem),\n/* harmony export */   MenuSeparator: () => (/* binding */ MenuSeparator),\n/* harmony export */   MenuSub: () => (/* binding */ MenuSub),\n/* harmony export */   MenuSubContent: () => (/* binding */ MenuSubContent),\n/* harmony export */   MenuSubTrigger: () => (/* binding */ MenuSubTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   SubContent: () => (/* binding */ SubContent),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger),\n/* harmony export */   createMenuScope: () => (/* binding */ createMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.6_@types+react-dom@19.1.5_@types+react@19.1.4__@types+re_0a81deda95bc01001437e69e14e149d2/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@t_6fa579b84aec5e61a5b40d10bb671609/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.6_@types+react-dom@19.1.5_@types+react@19.1.4__@types+r_9046d94f65ef3c964b2f2739c4ebef1d/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.6_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_d5459adb46993a50615f22c9197e0a71/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.8_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@_e792b184a2ca1dccf9b400aadd0fc7f8/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.4__@types+reac_0d0cd24fc19ea4534d390a6089f37aea/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.2_@types+react-dom@19.1.5_@types+react@19.1.4__@types+rea_7b933c94c4e5e4ade0d694d5b36d9593/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.9_@types+react-dom@19.1.5_@types+react@19.1.4__@types+_38f7531683053c648ebb14c8749eabb2/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.5/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.0_@types+react@19.1.4_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,CheckboxItem,Content,Group,Item,ItemIndicator,Label,Menu,MenuAnchor,MenuArrow,MenuCheckboxItem,MenuContent,MenuGroup,MenuItem,MenuItemIndicator,MenuLabel,MenuPortal,MenuRadioGroup,MenuRadioItem,MenuSeparator,MenuSub,MenuSubContent,MenuSubTrigger,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,createMenuScope auto */ // src/menu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar SELECTION_KEYS = [\n    \"Enter\",\n    \" \"\n];\nvar FIRST_KEYS = [\n    \"ArrowDown\",\n    \"PageUp\",\n    \"Home\"\n];\nvar LAST_KEYS = [\n    \"ArrowUp\",\n    \"PageDown\",\n    \"End\"\n];\nvar FIRST_LAST_KEYS = [\n    ...FIRST_KEYS,\n    ...LAST_KEYS\n];\nvar SUB_OPEN_KEYS = {\n    ltr: [\n        ...SELECTION_KEYS,\n        \"ArrowRight\"\n    ],\n    rtl: [\n        ...SELECTION_KEYS,\n        \"ArrowLeft\"\n    ]\n};\nvar SUB_CLOSE_KEYS = {\n    ltr: [\n        \"ArrowLeft\"\n    ],\n    rtl: [\n        \"ArrowRight\"\n    ]\n};\nvar MENU_NAME = \"Menu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(MENU_NAME);\nvar [createMenuContext, createMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(MENU_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope,\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope)();\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope)();\nvar [MenuProvider, useMenuContext] = createMenuContext(MENU_NAME);\nvar [MenuRootProvider, useMenuRootContext] = createMenuContext(MENU_NAME);\nvar Menu = (props)=>{\n    const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isUsingKeyboardRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Menu.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Menu.useEffect.handleKeyDown\": ()=>{\n                    isUsingKeyboardRef.current = true;\n                    document.addEventListener(\"pointerdown\", handlePointer, {\n                        capture: true,\n                        once: true\n                    });\n                    document.addEventListener(\"pointermove\", handlePointer, {\n                        capture: true,\n                        once: true\n                    });\n                }\n            }[\"Menu.useEffect.handleKeyDown\"];\n            const handlePointer = {\n                \"Menu.useEffect.handlePointer\": ()=>isUsingKeyboardRef.current = false\n            }[\"Menu.useEffect.handlePointer\"];\n            document.addEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n            return ({\n                \"Menu.useEffect\": ()=>{\n                    document.removeEventListener(\"keydown\", handleKeyDown, {\n                        capture: true\n                    });\n                    document.removeEventListener(\"pointerdown\", handlePointer, {\n                        capture: true\n                    });\n                    document.removeEventListener(\"pointermove\", handlePointer, {\n                        capture: true\n                    });\n                }\n            })[\"Menu.useEffect\"];\n        }\n    }[\"Menu.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootProvider, {\n                scope: __scopeMenu,\n                onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                    \"Menu.useCallback\": ()=>handleOpenChange(false)\n                }[\"Menu.useCallback\"], [\n                    handleOpenChange\n                ]),\n                isUsingKeyboardRef,\n                dir: direction,\n                modal,\n                children\n            })\n        })\n    });\n};\nMenu.displayName = MENU_NAME;\nvar ANCHOR_NAME = \"MenuAnchor\";\nvar MenuAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nMenuAnchor.displayName = ANCHOR_NAME;\nvar PORTAL_NAME = \"MenuPortal\";\nvar [PortalProvider, usePortalContext] = createMenuContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar MenuPortal = (props)=>{\n    const { __scopeMenu, forceMount, children, container } = props;\n    const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeMenu,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"MenuContent\";\nvar [MenuContentProvider, useMenuContentContext] = createMenuContext(CONTENT_NAME);\nvar MenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: rootContext.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentNonModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                })\n            })\n        })\n    });\n});\nvar MenuRootContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuRootContentModal.useEffect\": ()=>{\n            const content = ref.current;\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_11__.hideOthers)(content);\n        }\n    }[\"MenuRootContentModal.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: context.open,\n        disableOutsideScroll: true,\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n            checkForDefaultPrevented: false\n        }),\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar MenuRootContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        disableOutsideScroll: false,\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__.createSlot)(\"MenuContent.ScrollLock\");\nvar MenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, loop = false, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEntryFocus, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, disableOutsideScroll, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const pointerGraceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerGraceIntentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerDirRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"right\");\n    const lastPointerXRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const ScrollLockWrapper = disableOutsideScroll ? react_remove_scroll__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll ? {\n        as: Slot,\n        allowPinchZoom: true\n    } : void 0;\n    const handleTypeaheadSearch = (key)=>{\n        const search = searchRef.current + key;\n        const items = getItems().filter((item)=>!item.disabled);\n        const currentItem = document.activeElement;\n        const currentMatch = items.find((item)=>item.ref.current === currentItem)?.textValue;\n        const values = items.map((item)=>item.textValue);\n        const nextMatch = getNextMatch(values, search, currentMatch);\n        const newItem = items.find((item)=>item.textValue === nextMatch)?.ref.current;\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n        if (newItem) {\n            setTimeout(()=>newItem.focus());\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuContentImpl.useEffect\": ()=>{\n            return ({\n                \"MenuContentImpl.useEffect\": ()=>window.clearTimeout(timerRef.current)\n            })[\"MenuContentImpl.useEffect\"];\n        }\n    }[\"MenuContentImpl.useEffect\"], []);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const isPointerMovingToSubmenu = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"MenuContentImpl.useCallback[isPointerMovingToSubmenu]\": (event)=>{\n            const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n            return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n        }\n    }[\"MenuContentImpl.useCallback[isPointerMovingToSubmenu]\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentProvider, {\n        scope: __scopeMenu,\n        searchRef,\n        onItemEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"MenuContentImpl.useCallback\": (event)=>{\n                if (isPointerMovingToSubmenu(event)) event.preventDefault();\n            }\n        }[\"MenuContentImpl.useCallback\"], [\n            isPointerMovingToSubmenu\n        ]),\n        onItemLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"MenuContentImpl.useCallback\": (event)=>{\n                if (isPointerMovingToSubmenu(event)) return;\n                contentRef.current?.focus();\n                setCurrentItemId(null);\n            }\n        }[\"MenuContentImpl.useCallback\"], [\n            isPointerMovingToSubmenu\n        ]),\n        onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"MenuContentImpl.useCallback\": (event)=>{\n                if (isPointerMovingToSubmenu(event)) event.preventDefault();\n            }\n        }[\"MenuContentImpl.useCallback\"], [\n            isPointerMovingToSubmenu\n        ]),\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"MenuContentImpl.useCallback\": (intent)=>{\n                pointerGraceIntentRef.current = intent;\n            }\n        }[\"MenuContentImpl.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollLockWrapper, {\n            ...scrollLockWrapperProps,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__.FocusScope, {\n                asChild: true,\n                trapped: trapFocus,\n                onMountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onOpenAutoFocus, (event)=>{\n                    event.preventDefault();\n                    contentRef.current?.focus({\n                        preventScroll: true\n                    });\n                }),\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside,\n                    onInteractOutside,\n                    onDismiss,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Root, {\n                        asChild: true,\n                        ...rovingFocusGroupScope,\n                        dir: rootContext.dir,\n                        orientation: \"vertical\",\n                        loop,\n                        currentTabStopId: currentItemId,\n                        onCurrentTabStopIdChange: setCurrentItemId,\n                        onEntryFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEntryFocus, (event)=>{\n                            if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                        }),\n                        preventScrollOnEntryFocus: true,\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                            role: \"menu\",\n                            \"aria-orientation\": \"vertical\",\n                            \"data-state\": getOpenState(context.open),\n                            \"data-radix-menu-content\": \"\",\n                            dir: rootContext.dir,\n                            ...popperScope,\n                            ...contentProps,\n                            ref: composedRefs,\n                            style: {\n                                outline: \"none\",\n                                ...contentProps.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                                const target = event.target;\n                                const isKeyDownInside = target.closest(\"[data-radix-menu-content]\") === event.currentTarget;\n                                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                                const isCharacterKey = event.key.length === 1;\n                                if (isKeyDownInside) {\n                                    if (event.key === \"Tab\") event.preventDefault();\n                                    if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                                }\n                                const content = contentRef.current;\n                                if (event.target !== content) return;\n                                if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                                event.preventDefault();\n                                const items = getItems().filter((item)=>!item.disabled);\n                                const candidateNodes = items.map((item)=>item.ref.current);\n                                if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                                focusFirst(candidateNodes);\n                            }),\n                            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, (event)=>{\n                                if (!event.currentTarget.contains(event.target)) {\n                                    window.clearTimeout(timerRef.current);\n                                    searchRef.current = \"\";\n                                }\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                                const target = event.target;\n                                const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n                                if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                                    const newDir = event.clientX > lastPointerXRef.current ? \"right\" : \"left\";\n                                    pointerDirRef.current = newDir;\n                                    lastPointerXRef.current = event.clientX;\n                                }\n                            }))\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"MenuGroup\";\nvar MenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...groupProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"group\",\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"MenuLabel\";\nvar MenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...labelProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"MenuItem\";\nvar ITEM_SELECT = \"menu.itemSelect\";\nvar MenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleSelect = ()=>{\n        const menuItem = ref.current;\n        if (!disabled && menuItem) {\n            const itemSelectEvent = new CustomEvent(ITEM_SELECT, {\n                bubbles: true,\n                cancelable: true\n            });\n            menuItem.addEventListener(ITEM_SELECT, (event)=>onSelect?.(event), {\n                once: true\n            });\n            (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.dispatchDiscreteCustomEvent)(menuItem, itemSelectEvent);\n            if (itemSelectEvent.defaultPrevented) {\n                isPointerDownRef.current = false;\n            } else {\n                rootContext.onClose();\n            }\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n        ...itemProps,\n        ref: composedRefs,\n        disabled,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, handleSelect),\n        onPointerDown: (event)=>{\n            props.onPointerDown?.(event);\n            isPointerDownRef.current = true;\n        },\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            if (!isPointerDownRef.current) event.currentTarget?.click();\n        }),\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            const isTypingAhead = contentContext.searchRef.current !== \"\";\n            if (disabled || isTypingAhead && event.key === \" \") return;\n            if (SELECTION_KEYS.includes(event.key)) {\n                event.currentTarget.click();\n                event.preventDefault();\n            }\n        })\n    });\n});\nMenuItem.displayName = ITEM_NAME;\nvar MenuItemImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [textContent, setTextContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuItemImpl.useEffect\": ()=>{\n            const menuItem = ref.current;\n            if (menuItem) {\n                setTextContent((menuItem.textContent ?? \"\").trim());\n            }\n        }\n    }[\"MenuItemImpl.useEffect\"], [\n        itemProps.children\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeMenu,\n        disabled,\n        textValue: textValue ?? textContent,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Item, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            focusable: !disabled,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n                role: \"menuitem\",\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                ...itemProps,\n                ref: composedRefs,\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                    if (disabled) {\n                        contentContext.onItemLeave(event);\n                    } else {\n                        contentContext.onItemEnter(event);\n                        if (!event.defaultPrevented) {\n                            const item = event.currentTarget;\n                            item.focus({\n                                preventScroll: true\n                            });\n                        }\n                    }\n                })),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>contentContext.onItemLeave(event))),\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, ()=>setIsFocused(false))\n            })\n        })\n    });\n});\nvar CHECKBOX_ITEM_NAME = \"MenuCheckboxItem\";\nvar MenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemcheckbox\",\n            \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n            ...checkboxItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(checkboxItemProps.onSelect, ()=>onCheckedChange?.(isIndeterminate(checked) ? true : !checked), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"MenuRadioGroup\";\nvar [RadioGroupProvider, useRadioGroupContext] = createMenuContext(RADIO_GROUP_NAME, {\n    value: void 0,\n    onValueChange: ()=>{}\n});\nvar MenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onValueChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: props.__scopeMenu,\n        value,\n        onValueChange: handleValueChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuGroup, {\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"MenuRadioItem\";\nvar MenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemradio\",\n            \"aria-checked\": checked,\n            ...radioItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(radioItemProps.onSelect, ()=>context.onValueChange?.(value), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar ITEM_INDICATOR_NAME = \"MenuItemIndicator\";\nvar [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext(ITEM_INDICATOR_NAME, {\n    checked: false\n});\nvar MenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || isIndeterminate(indicatorContext.checked) || indicatorContext.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.span, {\n            ...itemIndicatorProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(indicatorContext.checked)\n        })\n    });\n});\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SEPARATOR_NAME = \"MenuSeparator\";\nvar MenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"separator\",\n        \"aria-orientation\": \"horizontal\",\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"MenuArrow\";\nvar MenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nMenuArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"MenuSub\";\nvar [MenuSubProvider, useMenuSubContext] = createMenuContext(SUB_NAME);\nvar MenuSub = (props)=>{\n    const { __scopeMenu, children, open = false, onOpenChange } = props;\n    const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuSub.useEffect\": ()=>{\n            if (parentMenuContext.open === false) handleOpenChange(false);\n            return ({\n                \"MenuSub.useEffect\": ()=>handleOpenChange(false)\n            })[\"MenuSub.useEffect\"];\n        }\n    }[\"MenuSub.useEffect\"], [\n        parentMenuContext.open,\n        handleOpenChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuSubProvider, {\n                scope: __scopeMenu,\n                contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                trigger,\n                onTriggerChange: setTrigger,\n                children\n            })\n        })\n    });\n};\nMenuSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"MenuSubTrigger\";\nvar MenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = {\n        __scopeMenu: props.__scopeMenu\n    };\n    const clearOpenTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"MenuSubTrigger.useCallback[clearOpenTimer]\": ()=>{\n            if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = null;\n        }\n    }[\"MenuSubTrigger.useCallback[clearOpenTimer]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuSubTrigger.useEffect\": ()=>clearOpenTimer\n    }[\"MenuSubTrigger.useEffect\"], [\n        clearOpenTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuSubTrigger.useEffect\": ()=>{\n            const pointerGraceTimer = pointerGraceTimerRef.current;\n            return ({\n                \"MenuSubTrigger.useEffect\": ()=>{\n                    window.clearTimeout(pointerGraceTimer);\n                    onPointerGraceIntentChange(null);\n                }\n            })[\"MenuSubTrigger.useEffect\"];\n        }\n    }[\"MenuSubTrigger.useEffect\"], [\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuAnchor, {\n        asChild: true,\n        ...scope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n            id: subContext.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": subContext.contentId,\n            \"data-state\": getOpenState(context.open),\n            ...props,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.composeRefs)(forwardedRef, subContext.onTriggerChange),\n            onClick: (event)=>{\n                props.onClick?.(event);\n                if (props.disabled || event.defaultPrevented) return;\n                event.currentTarget.focus();\n                if (!context.open) context.onOpenChange(true);\n            },\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                contentContext.onItemEnter(event);\n                if (event.defaultPrevented) return;\n                if (!props.disabled && !context.open && !openTimerRef.current) {\n                    contentContext.onPointerGraceIntentChange(null);\n                    openTimerRef.current = window.setTimeout(()=>{\n                        context.onOpenChange(true);\n                        clearOpenTimer();\n                    }, 100);\n                }\n            })),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>{\n                clearOpenTimer();\n                const contentRect = context.content?.getBoundingClientRect();\n                if (contentRect) {\n                    const side = context.content?.dataset.side;\n                    const rightSide = side === \"right\";\n                    const bleed = rightSide ? -5 : 5;\n                    const contentNearEdge = contentRect[rightSide ? \"left\" : \"right\"];\n                    const contentFarEdge = contentRect[rightSide ? \"right\" : \"left\"];\n                    contentContext.onPointerGraceIntentChange({\n                        area: [\n                            // Apply a bleed on clientX to ensure that our exit point is\n                            // consistently within polygon bounds\n                            {\n                                x: event.clientX + bleed,\n                                y: event.clientY\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.bottom\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.bottom\n                            }\n                        ],\n                        side\n                    });\n                    window.clearTimeout(pointerGraceTimerRef.current);\n                    pointerGraceTimerRef.current = window.setTimeout(()=>contentContext.onPointerGraceIntentChange(null), 300);\n                } else {\n                    contentContext.onTriggerLeave(event);\n                    if (event.defaultPrevented) return;\n                    contentContext.onPointerGraceIntentChange(null);\n                }\n            })),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isTypingAhead = contentContext.searchRef.current !== \"\";\n                if (props.disabled || isTypingAhead && event.key === \" \") return;\n                if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n                    context.onOpenChange(true);\n                    context.content?.focus();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"MenuSubContent\";\nvar MenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n                    id: subContext.contentId,\n                    \"aria-labelledby\": subContext.triggerId,\n                    ...subContentProps,\n                    ref: composedRefs,\n                    align: \"start\",\n                    side: rootContext.dir === \"rtl\" ? \"left\" : \"right\",\n                    disableOutsidePointerEvents: false,\n                    disableOutsideScroll: false,\n                    trapFocus: false,\n                    onOpenAutoFocus: (event)=>{\n                        if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                        event.preventDefault();\n                    },\n                    onCloseAutoFocus: (event)=>event.preventDefault(),\n                    onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                        if (event.target !== subContext.trigger) context.onOpenChange(false);\n                    }),\n                    onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (event)=>{\n                        rootContext.onClose();\n                        event.preventDefault();\n                    }),\n                    onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                        const isKeyDownInside = event.currentTarget.contains(event.target);\n                        const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                        if (isKeyDownInside && isCloseKey) {\n                            context.onOpenChange(false);\n                            subContext.trigger?.focus();\n                            event.preventDefault();\n                        }\n                    })\n                })\n            })\n        })\n    });\n});\nMenuSubContent.displayName = SUB_CONTENT_NAME;\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getCheckedState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nfunction focusFirst(candidates) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus();\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nfunction getNextMatch(values, search, currentMatch) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n    let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n    const excludeCurrentMatch = normalizedSearch.length === 1;\n    if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v)=>v !== currentMatch);\n    const nextMatch = wrappedValues.find((value)=>value.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextMatch !== currentMatch ? nextMatch : void 0;\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction isPointerInGraceArea(event, area) {\n    if (!area) return false;\n    const cursorPos = {\n        x: event.clientX,\n        y: event.clientY\n    };\n    return isPointInPolygon(cursorPos, area);\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root3 = Menu;\nvar Anchor2 = MenuAnchor;\nvar Portal = MenuPortal;\nvar Content2 = MenuContent;\nvar Group = MenuGroup;\nvar Label = MenuLabel;\nvar Item2 = MenuItem;\nvar CheckboxItem = MenuCheckboxItem;\nvar RadioGroup = MenuRadioGroup;\nvar RadioItem = MenuRadioItem;\nvar ItemIndicator = MenuItemIndicator;\nvar Separator = MenuSeparator;\nvar Arrow2 = MenuArrow;\nvar Sub = MenuSub;\nvar SubTrigger = MenuSubTrigger;\nvar SubContent = MenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.14_@types+react-dom@19.1.5_@types+react@19.1.4__@types+react@1_002d64d17ba89dba2d9227230aa23ed2/node_modules/@radix-ui/react-menu/dist/index.mjs\n");

/***/ })

};
;