(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8549],{14108:(e,t,s)=>{"use strict";s.d(t,{e:()=>l});var a=s(93853);class i{static getInstance(){return i.instance||(i.instance=new i),i.instance}startPolling(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5e3;this.pollInterval||(this.lastCheck=Math.floor(Date.now()/1e3),this.pollInterval=setInterval(()=>{this.checkInvalidations()},e),this.checkInvalidations())}stopPolling(){this.pollInterval&&(clearInterval(this.pollInterval),this.pollInterval=null)}async checkInvalidations(){if(!this.isPolling){this.isPolling=!0;try{let e=await fetch("/api/wp-proxy/cache/invalidations?since=".concat(this.lastCheck),{headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok)return void console.error("Failed to check cache invalidations:",e.status);let t=await e.json();t.invalidations&&t.invalidations.length>0&&(console.log("Processing cache invalidations:",t.invalidations),this.processInvalidations(t.invalidations)),this.lastCheck=t.timestamp}catch(e){console.error("Error checking cache invalidations:",e)}finally{this.isPolling=!1}}}processInvalidations(e){e.forEach(e=>{switch(e.type){case"post":this.invalidatePostCache(e.id);break;case"category":this.invalidateCategoryCache(e.id);break;case"all":this.invalidateAllCache()}})}async triggerServerRevalidation(e,t){try{await fetch("/api/revalidate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:e,id:t||0})}),console.log("[Cache] Server-side revalidation triggered for ".concat(e," ").concat(t?"(ID: ".concat(t,")"):""))}catch(e){console.error("Failed to trigger server-side revalidation:",e)}}invalidatePostCache(e){a.qQ.invalidateQueries({queryKey:a.lH.posts.detail(e)}),a.qQ.invalidateQueries({queryKey:a.lH.posts.all}),a.qQ.invalidateQueries({predicate:e=>{let t=e.queryKey;return Array.isArray(t)&&"posts"===t[0]&&"user"===t[1]}})}invalidateCategoryCache(e){a.qQ.invalidateQueries({predicate:e=>{var t;let s=e.queryKey;return Array.isArray(s)&&"posts"===s[0]&&("by-category"===s[1]||"list"===s[1]&&(null==(t=s[2])?void 0:t.category))}}),a.qQ.invalidateQueries({queryKey:a.lH.categories.all})}invalidateAllCache(){a.qQ.invalidateQueries()}async invalidateCache(e,t){let s={type:e,id:t||0,timestamp:Math.floor(Date.now()/1e3)};this.processInvalidations([s]),await this.triggerServerRevalidation(e,t)}constructor(){this.pollInterval=null,this.lastCheck=0,this.isPolling=!1}}let l=i.getInstance();"visible"===document.visibilityState&&l.startPolling(),document.addEventListener("visibilitychange",()=>{"visible"===document.visibilityState?l.startPolling():l.stopPolling()}),window.addEventListener("beforeunload",()=>{l.stopPolling()})},26715:(e,t,s)=>{"use strict";s.d(t,{Ht:()=>o,jE:()=>n});var a=s(12115),i=s(95155),l=a.createContext(void 0),n=e=>{let t=a.useContext(l);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},o=e=>{let{client:t,children:s}=e;return a.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,i.jsx)(l.Provider,{value:t,children:s})}},87633:(e,t,s)=>{Promise.resolve().then(s.bind(s,97243))},93853:(e,t,s)=>{"use strict";s.d(t,{WG:()=>l,lH:()=>i,qQ:()=>a});let a=new(s(87017)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:3,refetchOnWindowFocus:!1,refetchOnReconnect:!1},mutations:{retry:1}}}),i={posts:{all:["posts"],lists:()=>[...i.posts.all,"list"],list:e=>[...i.posts.lists(),e],details:()=>[...i.posts.all,"detail"],detail:e=>[...i.posts.details(),e],userPosts:(e,t,s)=>[...i.posts.all,"user",e,{page:t,perPage:s}],byCategory:(e,t)=>[...i.posts.all,"category",e,t]},auth:{all:["auth"],status:()=>[...i.auth.all,"status"],profile:()=>[...i.auth.all,"profile"]},members:{all:["members"],lists:()=>[...i.members.all,"list"],list:e=>[...i.members.lists(),e],details:()=>[...i.members.all,"detail"],detail:e=>[...i.members.details(),e],profile:e=>[...i.members.all,"profile",e]},connections:{all:["connections"],status:e=>[...i.connections.all,"status",e],list:e=>[...i.connections.all,"list",e],pending:()=>[...i.connections.all,"pending"]},iqScore:{all:["iqScore"],user:e=>[...i.iqScore.all,"user",e],me:()=>[...i.iqScore.all,"me"],leaderboard:()=>[...i.iqScore.all,"leaderboard"],ranks:()=>[...i.iqScore.all,"ranks"]},forum:{all:["forum"],questions:e=>[...i.forum.all,"questions",e],question:e=>[...i.forum.all,"question",e],userQuestions:e=>[...i.forum.all,"user",e],comments:e=>[...i.forum.all,"comments",e]},messages:{all:["messages"],conversations:()=>[...i.messages.all,"conversations"],conversation:e=>[...i.messages.all,"conversation",e],unreadCount:()=>[...i.messages.all,"unreadCount"]},vendors:{all:["vendors"],lists:()=>[...i.vendors.all,"list"],list:e=>[...i.vendors.lists(),e],details:()=>[...i.vendors.all,"detail"],detail:e=>[...i.vendors.details(),e],posts:e=>[...i.vendors.all,"posts",e],categories:()=>[...i.vendors.all,"categories"]},categories:{all:["categories"],list:()=>[...i.categories.all,"list"]},bookmarks:{all:["bookmarks"],user:e=>[...i.bookmarks.all,"user",e]},notifications:{all:["notifications"],list:()=>[...i.notifications.all,"list"]}},l={allPosts:()=>a.invalidateQueries({queryKey:i.posts.all}),post:e=>a.invalidateQueries({queryKey:i.posts.detail(e)}),userPosts:e=>a.invalidateQueries({queryKey:[...i.posts.all,"user",e]}),auth:()=>a.invalidateQueries({queryKey:i.auth.all}),connections:()=>a.invalidateQueries({queryKey:i.connections.all}),iqScores:()=>a.invalidateQueries({queryKey:i.iqScore.all}),forum:()=>a.invalidateQueries({queryKey:i.forum.all}),messages:()=>a.invalidateQueries({queryKey:i.messages.all}),notifications:()=>a.invalidateQueries({queryKey:i.notifications.all})}},97243:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(95155),i=s(12115),l=s(14108),n=s(26715),o=s(32960),r=s(93853);function c(){let[e,t]=(0,i.useState)(""),s=(0,n.jE)(),{data:c,isLoading:d}=(0,o.I)({queryKey:r.lH.posts.list({}),queryFn:async()=>{let e=await fetch("/api/wp-proxy/posts?per_page=5&_embed=1");if(!e.ok)throw Error("Failed to fetch posts");return e.json()},staleTime:3e4}),h=async(e,s)=>{try{await l.e.invalidateCache(e,s),t("Invalidated ".concat(e," ").concat(s?"(ID: ".concat(s,")"):""," at ").concat(new Date().toLocaleTimeString()))}catch(s){console.error("Invalidation failed:",s),t("Failed to invalidate ".concat(e," - check console"))}},u=async()=>{try{let e=await fetch("/api/wp-proxy/cache/invalidations?since=0"),t=await e.json();console.log("Cache invalidations:",t),alert("Found ".concat(t.invalidations.length," recent invalidations. Check console for details."))}catch(e){console.error("Error checking cache:",e),alert("Error checking cache status")}};return(0,a.jsxs)("div",{className:"container mx-auto p-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Cache Invalidation Test"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Controls"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:()=>h("post",123),className:"w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",children:"Invalidate Post Cache (ID: 123)"}),(0,a.jsx)("button",{onClick:()=>h("category",5),className:"w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600",children:"Invalidate Category Cache (ID: 5)"}),(0,a.jsx)("button",{onClick:()=>h("all"),className:"w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600",children:"Invalidate All Caches"}),(0,a.jsx)("button",{onClick:u,className:"w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600",children:"Check Cache Status"}),(0,a.jsx)("button",{onClick:()=>s.invalidateQueries(),className:"w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600",children:"Manual React Query Invalidation"})]}),e&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-gray-100 rounded",children:[(0,a.jsx)("strong",{children:"Last Action:"})," ",e]})]}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Sample Posts (Test Data)"}),d?(0,a.jsx)("p",{children:"Loading posts..."}):(0,a.jsx)("div",{className:"space-y-2",children:(null==c?void 0:c.map(e=>(0,a.jsxs)("div",{className:"p-2 border rounded",children:[(0,a.jsx)("h3",{className:"font-medium",children:e.title.rendered}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["ID: ",e.id]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Modified: ",new Date(e.modified).toLocaleString()]})]},e.id)))||(0,a.jsx)("p",{children:"No posts found"})}),(0,a.jsx)("div",{className:"mt-4 text-sm text-gray-500",children:"Cache should refresh automatically when posts are updated in WordPress admin. Use the test buttons to simulate invalidation events."})]})]}),(0,a.jsxs)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"How to Test:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"Open WordPress admin and edit a post"}),(0,a.jsx)("li",{children:"Save the post in WordPress"}),(0,a.jsx)("li",{children:"Return to this page - the cache should automatically invalidate"}),(0,a.jsx)("li",{children:"Posts should refresh with updated content within 5-30 seconds"}),(0,a.jsx)("li",{children:"Use the test buttons above to manually trigger cache invalidation"}),(0,a.jsx)("li",{children:"Check browser console for cache invalidation logs"})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5272,8441,1684,7358],()=>t(87633)),_N_E=e.O()}]);