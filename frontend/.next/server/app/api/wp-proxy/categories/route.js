/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wp-proxy/categories/route";
exports.ids = ["app/api/wp-proxy/categories/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fcategories%2Froute&page=%2Fapi%2Fwp-proxy%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fcategories%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fcategories%2Froute&page=%2Fapi%2Fwp-proxy%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fcategories%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_categories_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wp-proxy/categories/route.ts */ \"(rsc)/./src/app/api/wp-proxy/categories/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wp-proxy/categories/route\",\n        pathname: \"/api/wp-proxy/categories\",\n        filename: \"route\",\n        bundlePath: \"app/api/wp-proxy/categories/route\"\n    },\n    resolvedPagePath: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/api/wp-proxy/categories/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_categories_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fcategories%2Froute&page=%2Fapi%2Fwp-proxy%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fcategories%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wp-proxy/categories/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/wp-proxy/categories/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n\n/**\n * Proxy handler for WordPress categories\n */ async function GET(request) {\n    try {\n        // WordPress API URL from environment variable\n        const wpBaseUrl = \"http://mytourismiq.wpenginepowered.com/\" || 0;\n        const apiUrl = `${wpBaseUrl}/wp-json`;\n        // Get query parameters\n        const searchParams = request.nextUrl.searchParams;\n        const perPage = searchParams.get('per_page') || '100';\n        const slug = searchParams.get('slug');\n        // Construct the WordPress API URL for categories\n        let wpUrl = `${apiUrl}/wp/v2/categories?per_page=${perPage}`;\n        // Add slug filter if provided\n        if (slug) {\n            wpUrl += `&slug=${encodeURIComponent(slug)}`;\n        }\n        // Use basic auth with application password if available\n        const username = process.env.WORDPRESS_USERNAME;\n        const password = process.env.WORDPRESS_APPLICATION_PASSWORD;\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        // Add authorization header if credentials are available\n        if (username && password) {\n            const auth = Buffer.from(`${username}:${password}`).toString('base64');\n            headers['Authorization'] = `Basic ${auth}`;\n        }\n        // Fetch categories from WordPress\n        const response = await fetch(wpUrl, {\n            headers,\n            credentials: 'include'\n        });\n        if (!response.ok) {\n            console.error(`WordPress API error: ${response.status} ${response.statusText}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch categories from WordPress'\n            }, {\n                status: response.status\n            });\n        }\n        // Get the categories data\n        const categories = await response.json();\n        // Return the categories\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(categories);\n    } catch (error) {\n        console.error('Error in categories proxy:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wp-proxy/categories/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fcategories%2Froute&page=%2Fapi%2Fwp-proxy%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fcategories%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();