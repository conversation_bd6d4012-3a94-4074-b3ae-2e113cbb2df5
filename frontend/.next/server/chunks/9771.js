exports.id=9771,exports.ids=[9771],exports.modules={90:(e,t,s)=>{"use strict";s.d(t,{ChatWindow:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ChatWindow() from the server but ChatWindow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/messaging/chat-window.tsx","ChatWindow")},958:(e,t,s)=>{"use strict";s.d(t,{ConnectionProvider:()=>i});var r=s(60687),a=s(43210);let n=(0,a.createContext)(void 0);function i({children:e}){let[t,s]=(0,a.useState)({}),i=(0,a.useRef)({}),o=(0,a.useCallback)(e=>t[e]||null,[t]),l=(0,a.useCallback)(async e=>{if(t[e])return t[e];if(e in i.current)return await i.current[e]??null;let r=async()=>{try{let t=new AbortController,r=setTimeout(()=>{t.abort()},1e4),a=await fetch(`/api/wp-proxy/connections/status/${e}`,{credentials:"include",signal:t.signal});if(clearTimeout(r),!a.ok)throw Error(`Failed to fetch connection status: ${a.status}`);let n=await a.json();if(n&&n.status){let t={status:n.status||"none",pending_type:n.pending_type};return s(s=>({...s,[e]:t})),t}return{status:"none"}}catch(t){return console.error(`Error fetching connection status for user ${e}:`,t),null}finally{delete i.current[e]}};return i.current[e]=r(),i.current[e]},[t]),c=(0,a.useCallback)(async e=>{let s={},r=[];if(e.forEach(e=>{t[e]?s[e]=t[e]:r.push(e)}),r.length>0){let e=[];for(let t=0;t<r.length;t+=5)e.push(r.slice(t,t+5));for(let t of e){let e=t.map(e=>l(e));(await Promise.all(e)).forEach((e,r)=>{e&&void 0!==t[r]&&(s[t[r]]=e)})}}return s},[t,l]),d=(0,a.useCallback)(e=>{s(t=>{let s={...t};return delete s[e],s}),delete i.current[e]},[]),u=(0,a.useCallback)(()=>{s({}),i.current={}},[]);return(0,r.jsx)(n.Provider,{value:{getConnectionStatus:l,batchGetConnectionStatuses:c,clearCache:u,getCachedConnectionStatus:o,invalidateUserStatus:d},children:e})}},3177:(e,t,s)=>{"use strict";s.d(t,{IQScoreProvider:()=>o,m:()=>l});var r=s(60687),a=s(43210),n=s(65885);let i=(0,a.createContext)(void 0);function o({children:e}){let[t,s]=(0,a.useState)({}),o=(0,a.useRef)({}),l=(0,a.useCallback)(e=>t[e]||null,[t]),c=(0,a.useCallback)(async e=>{if(!e||isNaN(e)||e<=0)return console.warn("Invalid userId provided to getUserIQ:",e),null;if(t[e])return t[e];if(e in o.current&&o.current[e])try{return await o.current[e]}catch(t){return console.error(`Error awaiting pending request for user ${e}:`,t),null}let r=async()=>{try{let t=await n.f.getUserIQ(e);if(t?.success&&t.data)return s(s=>({...s,[e]:t.data})),t.data;return null}catch(t){return console.error(`Error fetching IQ score for user ${e}:`,t),null}finally{setTimeout(()=>{delete o.current[e]},100)}};try{return o.current[e]=r(),await o.current[e]}catch(t){return console.error(`Critical error in getUserIQ for user ${e}:`,t),delete o.current[e],null}},[t]),d=(0,a.useCallback)(async e=>{let s={},r=[];if(e.forEach(e=>{t[e]?s[e]=t[e]:r.push(e)}),r.length>0){let e=[];for(let t=0;t<r.length;t+=5)e.push(r.slice(t,t+5));for(let t of e){let e=t.map(e=>c(e));(await Promise.all(e)).forEach((e,r)=>{e&&void 0!==t[r]&&(s[t[r]]=e)})}}return s},[t,c]),u=(0,a.useCallback)(()=>{s({}),o.current={}},[]);return(0,r.jsx)(i.Provider,{value:{getUserIQ:c,batchGetUserIQs:d,clearCache:u,getCachedUserIQ:l},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useIQScore must be used within an IQScoreProvider");return e}},4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(49384),a=s(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}},5972:(e,t,s)=>{"use strict";s.d(t,{ChatWindow:()=>g});var r=s(60687),a=s(43210),n=s(11860),i=s(5748),o=s(79351),l=s(49153),c=s(27900),d=s(32584),u=s(29523),m=s(34729),h=s(4780),p=s(71114),x=s(46952),f=s(7112);function g({conversation:e,position:t={bottom:0,right:340},isMinimized:s,isMaximized:g,onClose:b,onMinimize:v,onMaximize:y}){let[j,w]=(0,a.useState)([]),[N,C]=(0,a.useState)(""),[k,A]=(0,a.useState)(!1),[S,_]=(0,a.useState)(!1),[P,E]=(0,a.useState)(!1),[F,q]=(0,a.useState)(384),[$,I]=(0,a.useState)(!1),T=(0,a.useRef)(null),L=(0,a.useRef)(null),{socket:M}=(0,x.E)(),{isAuthenticated:R}=(0,f.A)(),z=(0,a.useCallback)(e=>{e.preventDefault(),I(!0);let t=e.clientY,s=e=>{q(Math.max(200,Math.min(800,F+(t-e.clientY))))},r=()=>{I(!1),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",r)};document.addEventListener("mousemove",s),document.addEventListener("mouseup",r)},[F]),D=(0,a.useMemo)(()=>e.participants[0],[e.participants]),U=(0,a.useMemo)(()=>D?.id,[D?.id]),Q=(0,a.useCallback)(async()=>{if(""!==N.trim()&&U)try{let e=await p.J.sendMessage({recipient_id:parseInt(U),content:N.trim()});w(t=>[...t,e.message]),C(""),M&&M.emit("typing_stop",{receiverId:U})}catch(e){console.error("Failed to send message:",e)}},[N,U,M]),O=(0,a.useCallback)(()=>{M&&U&&(M.emit("typing_start",{receiverId:U}),setTimeout(()=>{M.emit("typing_stop",{receiverId:U})},3e3))},[M,U]);if(!D)return null;let W=()=>D?.firstName&&D?.lastName?`${D.firstName.charAt(0)}${D.lastName.charAt(0)}`:(D?.username??"User").substring(0,2).toUpperCase(),H=()=>D?.firstName&&D?.lastName?`${D.firstName} ${D.lastName}`:D?.username??"User",B=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),V=e=>{let t=new Date(e),s=new Date,r=new Date(s);return(r.setDate(r.getDate()-1),t.toDateString()===s.toDateString())?"Today":t.toDateString()===r.toDateString()?"Yesterday":t.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})};return s?(0,r.jsx)("div",{ref:L,className:"fixed bg-white border border-gray-200 rounded-t-lg shadow-lg z-50 w-48",style:{bottom:`${t.bottom}px`,right:`${t.right}px`},children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-primary text-primary-foreground rounded-t-lg cursor-pointer",onClick:v,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 min-w-0",children:[(0,r.jsxs)(d.eu,{className:"h-6 w-6",children:[D.avatar?.url&&(0,r.jsx)(d.BK,{src:D.avatar.url,alt:H()}),(0,r.jsx)(d.q5,{className:"text-xs",children:W()})]}),(0,r.jsx)("span",{className:"text-sm font-medium truncate",children:H()}),e.unreadCount>0&&(0,r.jsx)("span",{className:"bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center",children:e.unreadCount})]}),(0,r.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 text-primary-foreground hover:bg-white/15",onClick:e=>{e.stopPropagation(),b()},children:(0,r.jsx)(n.A,{className:"h-3 w-3"})})]})}):(0,r.jsxs)("div",{ref:L,className:(0,h.cn)("fixed bg-white border border-gray-200 rounded-t-lg shadow-lg z-50 select-none",g?"inset-4":"w-80",$&&"cursor-ns-resize"),style:g?{}:{bottom:`${t.bottom}px`,right:`${t.right}px`,height:`${F}px`},children:[!g&&(0,r.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1 cursor-ns-resize hover:bg-primary/20 transition-colors",onMouseDown:z}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-[#3d405b] text-white rounded-t-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 min-w-0",children:[(0,r.jsxs)(d.eu,{className:"h-8 w-8",children:[D.avatar?.url&&(0,r.jsx)(d.BK,{src:D.avatar.url,alt:H()}),(0,r.jsx)(d.q5,{children:W()})]}),(0,r.jsxs)("div",{className:"min-w-0",children:[(0,r.jsx)("h3",{className:"font-medium truncate text-white",children:H()}),(0,r.jsx)("p",{className:"text-xs text-white/80 truncate",children:P?"Active now":"Offline"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]",onClick:v,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}),g?(0,r.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]",onClick:v,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}):(0,r.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]",onClick:y,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})}),(0,r.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]",onClick:b,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{ref:T,className:"flex-1 overflow-y-auto p-4 space-y-4",style:{height:`${F-140}px`},children:[k?(0,r.jsx)("div",{className:"flex justify-center items-center h-full",children:(0,r.jsx)("div",{className:"text-gray-500",children:"Loading messages..."})}):0===j.length?(0,r.jsx)("div",{className:"flex justify-center items-center h-full",children:(0,r.jsxs)("div",{className:"text-gray-500 text-center",children:[(0,r.jsx)("p",{children:"No messages yet"}),(0,r.jsxs)("p",{className:"text-sm",children:["Start a conversation with ",H()]})]})}):(()=>{let e={};return j.forEach(t=>{let s=new Date(t.timestamp).toDateString();e[s]||(e[s]=[]),e[s].push(t)}),Object.entries(e).map(([e,t])=>({date:V(new Date(e).toISOString()),messages:t})).sort((e,t)=>"Today"===e.date?1:"Today"===t.date?-1:"Yesterday"===e.date?1:"Yesterday"===t.date?-1:new Date(e.messages[0]?.timestamp||0).getTime()-new Date(t.messages[0]?.timestamp||0).getTime())})().map((e,t)=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)("span",{className:"bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full",children:e.date})}),e.messages.map((t,s)=>{let a=t.senderId!==U,n=!a&&(s===e.messages.length-1||e.messages[s+1]?.senderId!==t.senderId),i=0===s||e.messages[s-1]?.senderId!==t.senderId,o=s===e.messages.length-1||e.messages[s+1]?.senderId!==t.senderId;return(0,r.jsxs)("div",{className:(0,h.cn)("flex items-end space-x-2",a?"justify-end":"justify-start",!o&&"mb-2"),children:[!a&&(0,r.jsxs)(d.eu,{className:(0,h.cn)("h-6 w-6",n?"opacity-100":"opacity-0"),children:[D.avatar?.url&&(0,r.jsx)(d.BK,{src:D.avatar.url,alt:H()}),(0,r.jsx)(d.q5,{className:"text-xs",children:W()})]}),(0,r.jsxs)("div",{className:(0,h.cn)("max-w-xs px-3 py-2 relative",a?i||o?"rounded-t-lg rounded-bl-lg":"rounded-t-lg":i||o?"rounded-t-lg rounded-br-lg":"rounded-t-lg",a?"bg-primary/30 text-primary-foreground":"bg-gray-100 text-dark-text"),children:[(0,r.jsx)("p",{className:"text-sm",children:t.content}),(0,r.jsx)("p",{className:(0,h.cn)("text-xs mt-1",a?"text-primary-foreground/80":"text-gray-500"),children:B(t.timestamp)})]})]},t.id)})]},t)),S&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(d.eu,{className:"h-6 w-6",children:[D.avatar?.url&&(0,r.jsx)(d.BK,{src:D.avatar.url,alt:H()}),(0,r.jsx)(d.q5,{className:"text-xs",children:W()})]}),(0,r.jsx)("div",{className:"bg-gray-100 px-3 py-2 rounded-lg",children:(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 p-3",children:(0,r.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(m.T,{value:N,onChange:e=>C(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey?O():(e.preventDefault(),Q())},placeholder:`Message ${H()}...`,className:"min-h-[36px] max-h-24 resize-none rounded-full border-gray-300 px-4 py-2 focus:border-primary focus:ring-primary",rows:1})}),(0,r.jsx)(u.$,{onClick:Q,disabled:""===N.trim(),size:"sm",className:"h-9 w-9 p-0 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 border-2 border-[#5cc8ff] rounded-full",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]})})]})}},7112:(e,t,s)=>{"use strict";s.d(t,{A:()=>m,AuthProvider:()=>u});var r=s(60687),a=s(43210),n=s(16189),i=s(8693),o=s(66816);let l=(0,a.createContext)(void 0),c=()=>null,d=(e,t)=>{},u=({children:e})=>{let[t,s]=(0,a.useState)(null),[u,m]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!0),[x,f]=(0,a.useState)(0),[g,b]=(0,a.useState)(!1),v=(0,n.useRouter)(),y=(0,i.jE)();(0,a.useEffect)(()=>{let e=c();e?(s(e.user),m(e.isAuthenticated),f(e.timestamp),p(!1)):p(!0),b(!0)},[]);let j=(0,a.useCallback)(async(e=!1)=>{let t=Date.now();if(!e&&x&&t-x<3e5)return void p(!1);p(!0);try{let e=await (0,o.z)();e.isAuthenticated&&e.user?(s(e.user),m(!0),d(e.user,!0)):(s(null),m(!1),d(null,!1)),f(t)}catch{s(null),m(!1),d(null,!1)}finally{p(!1)}},[x,y]);(0,a.useEffect)(()=>{g&&j()},[g,j]);let w=async(e,t)=>{p(!0);try{let r=await (0,o.Lx)(e,t);if(!r.success||!r.user)return s(null),m(!1),d(null,!1),{success:!1,error:r.error||"Login failed"};s(r.user),m(!0),d(r.user,!0),f(Date.now());try{await j(!0)}catch(e){console.warn("Failed to refresh auth after login:",e)}return{success:!0}}catch(e){return s(null),m(!1),{success:!1,error:e instanceof Error?e.message:"Login failed"}}finally{p(!1)}},N=async()=>{p(!0);try{await (0,o.y4)()?(s(null),m(!1),d(null,!1),f(0),v.push("/")):console.error("Logout failed")}catch(e){console.error("Logout error:",e instanceof Error?e.message:"Logout failed")}finally{p(!1)}},C=async e=>{try{let r=await (0,o.eg)(e);if(!r.success)throw Error(r.error||"Failed to update profile");if(t){let r={...t,...e,acf:t.acf?{...t.acf,job_title:void 0!==e.jobTitle?e.jobTitle:t.acf?.job_title||"",location:void 0!==e.location?e.location:t.acf?.location||"",profile_picture:void 0!==e.avatarUrl?e.avatarUrl:t.acf?.profile_picture,cover_image:void 0!==e.coverImageUrl?e.coverImageUrl:t.acf?.cover_image}:void 0};s(r)}}catch(e){throw console.error("Error updating profile:",e),e}finally{p(!1)}},k=(0,a.useCallback)(async()=>{await j(!0)},[j]);return(0,r.jsx)(l.Provider,{value:{user:t,isLoading:h,isAuthenticated:u,login:w,logout:N,updateProfile:C,refreshAuth:k},children:e})},m=()=>{let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},9611:(e,t,s)=>{"use strict";s.d(t,{ErrorBoundary:()=>n});var r=s(60687),a=s(43210);class n extends a.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.props.onError&&this.props.onError(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,r.jsx)("div",{className:"min-h-[200px] flex items-center justify-center p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-dark-text mb-2",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"We're sorry, but something unexpected happened. Please try refreshing the page."}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"w-1/3 px-3 py-2 text-sm font-semibold bg-[#5cc8ff] text-[#3d405b] rounded-full hover:bg-[#5cc8ff]/80 transition-colors",children:"Refresh Page"})}),!1]})}):this.props.children}}},9877:(e,t,s)=>{"use strict";s.d(t,{bG:()=>n,oj:()=>i,ue:()=>a});var r=s(37371);async function a(e=1,t=20,s="date",n,i=!1){try{let a=new URLSearchParams({per_page:t.toString(),page:e.toString(),_embed:"1",orderby:s,order:"desc"});n&&a.set("meta_key",n),i&&a.set("exclude_vendor_posts","true");let o=await fetch(`${r.tE}/wp-proxy/posts?sticky=true&${a.toString()}`,{headers:{"Content-Type":"application/json"},credentials:"include"});if(o.ok){let e=await o.json();if(Array.isArray(e)&&e.length>0){let t=parseInt(o.headers.get("X-WP-TotalPages")||"1",10),s=parseInt(o.headers.get("X-WP-Total")||e.length.toString(),10);return{posts:e,totalPages:t,totalPosts:s}}}else console.warn(`Failed to fetch sticky posts: ${o.status}. Trying regular posts...`);if(!(o=await fetch(`${r.tE}/wp-proxy/posts?${a.toString()}`,{headers:{"Content-Type":"application/json"},credentials:"include"})).ok)throw Error(`Failed to fetch posts: ${o.status}`);let l=await o.json(),c=parseInt(o.headers.get("X-WP-TotalPages")||"1",10),d=parseInt(o.headers.get("X-WP-Total")||l.length.toString(),10);return{posts:Array.isArray(l)?l:[],totalPages:c,totalPosts:d}}catch(e){return console.error("Error fetching posts:",e),{posts:[],totalPages:0,totalPosts:0}}}async function n(e,t=20,s=1,a="date",i){try{let n=new URLSearchParams({per_page:t.toString(),page:s.toString(),_embed:"1",orderby:a,order:"desc"});i&&n.set("meta_key",i);let o=await fetch(`${r.tE}/wp-proxy/posts/by-category/${encodeURIComponent(e)}?${n.toString()}`,{headers:{"Content-Type":"application/json"},credentials:"include"});if(!o.ok)return console.warn(`[getPostsByCategory] Failed to fetch posts for category '${e}': ${o.status}`),{posts:[],totalPages:0,totalPosts:0};let l=await o.json(),c=parseInt(o.headers.get("X-WP-TotalPages")||"0",10),d=parseInt(o.headers.get("X-WP-Total")||"0",10);return{posts:Array.isArray(l)?l:[],totalPages:c,totalPosts:d}}catch(t){return console.error(`Error fetching posts for category '${e}':`,t),{posts:[],totalPages:0,totalPosts:0}}}async function i(e=3){try{let t=await fetch(`${r.tE}/wp-proxy/categories?slug=event`,{headers:{"Content-Type":"application/json"},credentials:"include"});if(!t.ok)return console.warn(`Failed to fetch event category: ${t.status}`),[];let s=await t.json();if(!Array.isArray(s)||0===s.length)return console.warn("Event category not found"),[];let a=s[0].id,n=await fetch(`${r.tE}/wp-proxy/posts?categories=${a}&per_page=${e}&_embed&orderby=date&order=desc`,{headers:{"Content-Type":"application/json"},credentials:"include"});if(!n.ok)return console.warn(`Failed to fetch event posts: ${n.status}`),[];let i=await n.json();if(!Array.isArray(i))return[];return i.map(e=>{let t={...e};return e.acf&&(e.acf.event_start_date?e.acf.event_end_date?t.formatted_date=`${e.acf.event_start_date} - ${e.acf.event_end_date}`:t.formatted_date=e.acf.event_start_date:t.formatted_date=new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),e.acf.event_host_logo&&"object"==typeof e.acf.event_host_logo&&!e.acf.event_host_logo.url&&e.acf.event_host_logo.ID&&console.warn("Host logo is missing URL property:",e.acf.event_host_logo)),t})}catch(e){return console.error("Error fetching event posts:",e),[]}}},10246:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A,metadata:()=>k});var r=s(37413),a=s(2202),n=s.n(a),i=s(64988),o=s.n(i),l=s(36162);function c({children:e}){return(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"flex w-full max-w-[1360px]",children:[(0,r.jsx)("div",{className:"hidden min-[1280px]:block flex-shrink-0",children:(0,r.jsx)("div",{className:"sticky top-[80px] md:top-[90px] h-[calc(100vh-80px)] md:h-[calc(100vh-90px)] overflow-y-auto",children:Array.isArray(e)?e[0]:null})}),(0,r.jsx)("div",{className:"flex-1 max-w-[900px] min-[1280px]:max-w-[750px] max-[1023px]:mx-auto px-3 sm:px-4 md:pl-[30px] md:pr-[20px] relative",children:Array.isArray(e)?e[1]:e}),(0,r.jsx)("div",{className:"hidden min-[1024px]:block w-[300px] min-[1280px]:w-[350px] flex-shrink-0",children:(0,r.jsx)("div",{className:"sticky top-[80px] md:top-[90px] h-[calc(100vh-80px)] md:h-[calc(100vh-90px)] overflow-y-auto",children:Array.isArray(e)?e[2]:null})})]})})}s(61135);var d=s(68926),u=s(69857),m=s(34199),h=s(71858),p=s(81326),x=s(66180);s(15771),s(90);var f=s(59507),g=s(80875),b=s(16820),v=s(6931),y=s(21465),j=s(28909),w=s(17835),N=s(79630),C=s(81410);let k={title:"TourismIQ",description:"Your premier destination for tourism industry insights, resources, and community connections.",icons:{icon:[{url:"/favicon.ico",sizes:"any"},{url:"/favicon.svg",type:"image/svg+xml"},{url:"/favicon-96x96.png",sizes:"96x96",type:"image/png"}],apple:"/apple-touch-icon.png"},manifest:"/site.webmanifest"};function A({children:e}){return(0,r.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsxs)("body",{className:`${n().variable} ${o().variable} antialiased min-h-screen bg-[#eff1f4]`,children:[(0,r.jsx)(l.default,{src:"https://www.googletagmanager.com/gtag/js?id=G-7S7748SWVQ",strategy:"afterInteractive"}),(0,r.jsx)(l.default,{id:"google-analytics",strategy:"afterInteractive",children:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-7S7748SWVQ');
          `}),(0,r.jsx)(j.ErrorBoundary,{children:(0,r.jsx)(w.ReactQueryProvider,{children:(0,r.jsx)(N.CacheInvalidationProvider,{children:(0,r.jsx)(h.AuthProvider,{children:(0,r.jsx)(p.NotificationProvider,{children:(0,r.jsx)(x.UpvoteProvider,{children:(0,r.jsx)(g.IQScoreProvider,{children:(0,r.jsx)(b.ConnectionProvider,{children:(0,r.jsxs)(f.MessagingProvider,{children:[(0,r.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,r.jsx)(d.Header,{}),(0,r.jsxs)(c,{children:[(0,r.jsx)(u.Sidebar,{}),(0,r.jsx)(j.ErrorBoundary,{children:e}),(0,r.jsx)(m.RightSidebar,{})]})]}),(0,r.jsx)(v.Toaster,{position:"top-right"}),(0,r.jsx)(y.IQPointsToastContainer,{}),(0,r.jsx)(C.WelcomeModal,{})]})})})})})})})})})]})})}},11829:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,79167,23)),Promise.resolve().then(s.bind(s,52581)),Promise.resolve().then(s.bind(s,73276)),Promise.resolve().then(s.bind(s,88095)),Promise.resolve().then(s.bind(s,26004)),Promise.resolve().then(s.bind(s,5972)),Promise.resolve().then(s.bind(s,82949)),Promise.resolve().then(s.bind(s,78973)),Promise.resolve().then(s.bind(s,92308)),Promise.resolve().then(s.bind(s,14737)),Promise.resolve().then(s.bind(s,9611)),Promise.resolve().then(s.bind(s,81475)),Promise.resolve().then(s.bind(s,52548)),Promise.resolve().then(s.bind(s,7112)),Promise.resolve().then(s.bind(s,958)),Promise.resolve().then(s.bind(s,3177)),Promise.resolve().then(s.bind(s,46952)),Promise.resolve().then(s.bind(s,54278))},14089:(e,t,s)=>{"use strict";s.d(t,{I$:()=>r.useMessaging}),s(82949),s(5972);var r=s(78973)},14737:(e,t,s)=>{"use strict";s.d(t,{ReactQueryProvider:()=>i});var r=s(60687),a=s(8693),n=s(30007);function i({children:e}){return(0,r.jsxs)(a.Ht,{client:n.qQ,children:[e,!1]})}},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>h,gC:()=>m,l6:()=>c,yv:()=>d});var r=s(60687);s(43210);var a=s(91413),n=s(78272),i=s(13964),o=s(3589),l=s(4780);function c({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function d({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...i}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 text-[#3d405b]"})})]})}function m({className:e,children:t,position:s="popper",...n}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-[20px] border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,r.jsx)(p,{}),(0,r.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(x,{})]})})}function h({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-[rgba(92,200,255,0.15)] focus:text-accent-foreground data-[state=checked]:font-bold [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-full py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function p({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"size-4"})})}function x({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(n.A,{className:"size-4"})})}},15771:(e,t,s)=>{"use strict";s.d(t,{MessageList:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call MessageList() from the server but MessageList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/messaging/message-list.tsx","MessageList")},16820:(e,t,s)=>{"use strict";s.d(t,{ConnectionProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ConnectionProvider() from the server but ConnectionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/connection-context.tsx","ConnectionProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useConnectionCache() from the server but useConnectionCache is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/connection-context.tsx","useConnectionCache")},17835:(e,t,s)=>{"use strict";s.d(t,{ReactQueryProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/providers/ReactQueryProvider.tsx","ReactQueryProvider")},18332:(e,t,s)=>{"use strict";s.d(t,{iZ:()=>i,w1:()=>n});var r=s(51423),a=s(30007);function n(){let{data:e,isLoading:t,error:s,refetch:n}=(0,r.I)({queryKey:a.lH.auth.status(),queryFn:async()=>{let e=await fetch("/api/wp-proxy/auth/status",{headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok)return{isAuthenticated:!1,user:null};let t=await e.json();return{isAuthenticated:t.isLoggedIn||t.isAuthenticated||!1,user:t.wpUser||t.user||null}},staleTime:3e5,gcTime:6e5,retry:!1,refetchOnWindowFocus:!1,refetchOnReconnect:!0});return{isLoggedIn:e?.isAuthenticated||!1,isLoading:t,user:e?.user||null,error:s,refetch:n}}function i(){let{isLoggedIn:e,isLoading:t,user:s,error:r}=n();return{user:s||null,isAuthenticated:e||!1,isLoading:t,error:r}}},18636:(e,t,s)=>{"use strict";s.d(t,{l:()=>a});var r=s(43210);function a(){let[e,t]=(0,r.useState)({width:0,height:0,shouldUseAccordion:!0});return(0,r.useRef)(null),(0,r.useCallback)(()=>{let e=window.innerHeight;return{width:window.innerWidth,height:e,shouldUseAccordion:e<1338}},[]),e}},21342:(e,t,s)=>{"use strict";s.d(t,{M5:()=>x,SQ:()=>c,_2:()=>d,lp:()=>u,lv:()=>h,mB:()=>m,nV:()=>p,rI:()=>o,ty:()=>l});var r=s(60687);s(43210);var a=s(26312),n=s(14952),i=s(4780);function o({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dropdown-menu",...e})}function l({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...e})}function c({className:e,sideOffset:t=4,...s}){return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-[20px] border p-1 shadow-md",e),...s})})}function d({className:e,inset:t,variant:s="default",...n}){return(0,r.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,i.cn)("focus:bg-[rgba(92,200,255,0.15)] focus:text-accent-foreground data-[state=checked]:font-bold data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-full px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function u({className:e,inset:t,...s}){return(0,r.jsx)(a.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,i.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...s})}function m({className:e,...t}){return(0,r.jsx)(a.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",e),...t})}function h({...e}){return(0,r.jsx)(a.Pb,{"data-slot":"dropdown-menu-sub",...e})}function p({className:e,inset:t,children:s,...o}){return(0,r.jsxs)(a.ZP,{"data-slot":"dropdown-menu-sub-trigger","data-inset":t,className:(0,i.cn)("focus:bg-[rgba(92,200,255,0.15)] focus:text-accent-foreground data-[state=open]:bg-[rgba(92,200,255,0.15)] data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-full px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8",e),...o,children:[s,(0,r.jsx)(n.A,{className:"ml-auto size-4 text-[#3d405b]"})]})}function x({className:e,...t}){return(0,r.jsx)(a.G5,{"data-slot":"dropdown-menu-sub-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg",e),...t})}},21465:(e,t,s)=>{"use strict";s.d(t,{IQPointsToastContainer:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call IQPointsToast() from the server but IQPointsToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/ui/IQPointsToast.tsx","IQPointsToast");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call IQPointsToastContainer() from the server but IQPointsToastContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/ui/IQPointsToast.tsx","IQPointsToastContainer")},21557:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,47429,23)),Promise.resolve().then(s.bind(s,6931)),Promise.resolve().then(s.bind(s,68926)),Promise.resolve().then(s.bind(s,34199)),Promise.resolve().then(s.bind(s,69857)),Promise.resolve().then(s.bind(s,90)),Promise.resolve().then(s.bind(s,15771)),Promise.resolve().then(s.bind(s,59507)),Promise.resolve().then(s.bind(s,79630)),Promise.resolve().then(s.bind(s,17835)),Promise.resolve().then(s.bind(s,28909)),Promise.resolve().then(s.bind(s,21465)),Promise.resolve().then(s.bind(s,81410)),Promise.resolve().then(s.bind(s,71858)),Promise.resolve().then(s.bind(s,16820)),Promise.resolve().then(s.bind(s,80875)),Promise.resolve().then(s.bind(s,81326)),Promise.resolve().then(s.bind(s,66180))},25151:(e,t,s)=>{"use strict";s.d(t,{R:()=>p});var r=s(60687),a=s(43210),n=s(63503),i=s(29523),o=s(89667),l=s(34729),c=s(15079),d=s(80013),u=s(65668),m=s(16023),h=s(11860);function p({isOpen:e,onClose:t}){let[s,p]=(0,a.useState)({name:"",email:"",subject:"",priority:"",description:"",attachments:[]}),[x,f]=(0,a.useState)(!1),g=async e=>{e.preventDefault(),f(!0);try{let e=new FormData;Object.entries(s).forEach(([t,s])=>{"attachments"===t?s.forEach((t,s)=>{e.append(`attachment_${s}`,t)}):e.append(t,s)}),(await fetch("/api/support",{method:"POST",body:e})).ok?(alert("Support request submitted successfully!"),p({name:"",email:"",subject:"",priority:"",description:"",attachments:[]}),t()):alert("Failed to submit support request. Please try again.")}catch(e){console.error("Error submitting support request:",e),alert("An error occurred. Please try again.")}finally{f(!1)}},b=e=>{p(t=>({...t,attachments:t.attachments.filter((t,s)=>s!==e)}))};return(0,r.jsx)(n.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(n.Cf,{className:"sm:max-w-[600px] max-h-[90vh] p-0 bg-white rounded-2xl shadow-2xl border-0 flex flex-col overflow-hidden",children:[(0,r.jsx)(n.c7,{children:(0,r.jsx)(n.L3,{className:"sr-only",children:"Get Support"})}),(0,r.jsx)("div",{className:"relative bg-gradient-to-r from-[#5cc8ff]/30 to-[#5cc8ff]/20 p-6 rounded-t-2xl",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-white rounded-xl shadow-sm",children:(0,r.jsx)(u.A,{className:"h-6 w-6 text-[#5cc8ff]"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-[#3d405b] leading-tight",children:"Get Support"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"We're here to help you with any questions or issues"})]})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,r.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"name",className:"text-sm font-semibold text-gray-700",children:"Full Name *"}),(0,r.jsx)(o.p,{id:"name",type:"text",required:!0,value:s.name,onChange:e=>p(t=>({...t,name:e.target.value})),className:"h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200",placeholder:"Enter your full name"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"email",className:"text-sm font-semibold text-gray-700",children:"Email Address *"}),(0,r.jsx)(o.p,{id:"email",type:"email",required:!0,value:s.email,onChange:e=>p(t=>({...t,email:e.target.value})),className:"h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200",placeholder:"Enter your email"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"subject",className:"text-sm font-semibold text-gray-700",children:"Subject *"}),(0,r.jsx)(o.p,{id:"subject",type:"text",required:!0,value:s.subject,onChange:e=>p(t=>({...t,subject:e.target.value})),className:"h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200",placeholder:"Brief description of your issue"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"priority",className:"text-sm font-semibold text-gray-700",children:"Priority Level *"}),(0,r.jsxs)(c.l6,{value:s.priority,onValueChange:e=>p(t=>({...t,priority:e})),required:!0,children:[(0,r.jsx)(c.bq,{className:"h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200",children:(0,r.jsx)(c.yv,{placeholder:"Select priority"})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"low",children:"Low - General question"}),(0,r.jsx)(c.eb,{value:"medium",children:"Medium - Feature request"}),(0,r.jsx)(c.eb,{value:"high",children:"High - Bug report"}),(0,r.jsx)(c.eb,{value:"urgent",children:"Urgent - System issue"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"description",className:"text-sm font-semibold text-gray-700",children:"Description *"}),(0,r.jsx)(l.T,{id:"description",required:!0,value:s.description,onChange:e=>p(t=>({...t,description:e.target.value})),className:"min-h-[120px] px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200 resize-none",placeholder:"Please provide detailed information about your issue or question..."})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{className:"text-sm font-semibold text-gray-700",children:"Attachments (Optional)"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-[#5cc8ff] transition-colors duration-200",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Drop files here or click to upload"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mb-4",children:"Max 5 files, 10MB each (PDF, JPG, PNG, DOC)"}),(0,r.jsx)(o.p,{type:"file",multiple:!0,accept:".pdf,.jpg,.jpeg,.png,.doc,.docx",onChange:e=>{let t=Array.from(e.target.files||[]);p(e=>({...e,attachments:[...e.attachments,...t]}))},className:"hidden",id:"file-upload"}),(0,r.jsx)(d.J,{htmlFor:"file-upload",className:"inline-flex items-center px-6 py-3 bg-[#5cc8ff] text-[#3d405b] rounded-full hover:bg-[#5cc8ff]/80 font-semibold cursor-pointer transition-colors duration-200",children:"Choose Files"})]}),s.attachments.length>0&&(0,r.jsx)("div",{className:"space-y-2",children:s.attachments.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700 truncate",children:e.name}),(0,r.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>b(t),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:(0,r.jsx)(h.A,{className:"h-3 w-3"})})]},t))})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(i.$,{type:"button",variant:"outline",onClick:t,className:"px-8 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 font-semibold rounded-full transition-all duration-200",children:"Cancel"}),(0,r.jsx)(i.$,{type:"submit",disabled:x,className:"px-8 py-3 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-bold rounded-full transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed",children:x?"Submitting...":"Submit Request"})]})]})})]})})}},26004:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>q});var r=s(60687),a=s(85814),n=s.n(a),i=s(43210),o=s(32192),l=s(80659),c=s(41312),d=s(49653),u=s(20798),m=s(63654),h=s(44689),p=s(82080),x=s(10022),f=s(8403),g=s(27351),b=s(550),v=s(78005),y=s(28440),j=s(39233),w=s(58887),N=s(40228),C=s(11860),k=s(3589),A=s(16189),S=s(4780),_=s(21342);let P=["Events","Community Q&A","Podcasts","Thought Leadership","News","Resources","Vendor Directory","RFPs","People on the Move","Jobs Hub"];var E=s(93661);function F({collapsedItems:e,onNavigate:t=()=>{},isActive:s,isLoading:a,handleNavigation:n}){let[o,l]=(0,i.useState)(!1),c=(0,A.useRouter)(),d=(e,i,o)=>{let d=e.children&&e.children.length>0,u=s(e.href,!d),m=a(e.href);return"Resources"===e.label&&d?(0,r.jsxs)(_.lv,{children:[(0,r.jsxs)(_.nV,{className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 h-[52px] cursor-pointer",0===i&&"mt-0",i===o-1&&"mb-0",i!==o-1&&"mb-[3px]",u?"text-gray-600 font-semibold bg-[rgba(92,200,255,0.1)]":"text-gray-600 font-medium",u?"hover:bg-transparent focus:bg-transparent":"hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]"),children:[(0,r.jsx)(e.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.label})]}),(0,r.jsx)(_.M5,{className:"w-72 rounded-[30px]",children:e.children.map((i,o)=>{let c=s(i.href,!0);return(0,r.jsxs)(_._2,{onSelect:e=>{e.preventDefault(),l(!1),n({preventDefault:()=>{},currentTarget:{}},i),t?.()},className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 h-[52px] cursor-pointer",0===o&&"mt-0",o===e.children.length-1&&"mb-0",o!==e.children.length-1&&"mb-[3px]",c?"text-gray-600 font-semibold bg-[rgba(92,200,255,0.1)]":"text-gray-600 font-medium",c?"hover:bg-transparent focus:bg-transparent":"hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]"),children:[(0,r.jsx)(i.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:i.label}),a(i.href)&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5 animate-spin",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]},i.href)})})]},e.href):(0,r.jsxs)(_._2,{onSelect:s=>{s.preventDefault(),l(!1),e.isFeedFilter||e.children?.length||"#"===e.href?n({preventDefault:()=>{},currentTarget:{}},e):c.push(e.href),t?.()},className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 h-[52px] cursor-pointer",0===i&&"mt-0",i===o-1&&"mb-0",i!==o-1&&"mb-[3px]",u?"text-gray-600 font-semibold bg-[rgba(92,200,255,0.1)]":"text-gray-600 font-medium",u?"hover:bg-transparent focus:bg-transparent":"hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]"),children:[(0,r.jsx)(e.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.label}),m&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5 animate-spin",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]},e.href)};return(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)(_.rI,{open:o,onOpenChange:l,modal:!1,children:[(0,r.jsx)(_.ty,{asChild:!0,children:(0,r.jsxs)("button",{className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-all duration-200 ease-in-out w-full focus:outline-none focus:ring-0 border-0 cursor-pointer","h-[52px]","text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.15)]"),children:[(0,r.jsx)(E.A,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:"More"}),(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})}),(0,r.jsx)(_.SQ,{align:"start",side:"right",className:"w-72 rounded-[30px]",children:e.map((t,s)=>d(t,s,e.length))})]})})}function q({onNavigate:e=()=>{},onResourcesClick:t,currentMenu:s="main"}={}){let a=(0,A.usePathname)(),E=(0,A.useRouter)(),[$,I]=(0,i.useState)({resources:a.startsWith("/resources")}),[T,L]=(0,i.useState)(null),[M,R]=(0,i.useState)(null),[z,D]=(0,i.useState)(!0),[U,Q]=(0,i.useState)(!1),O=e=>{I(t=>({...t,[e]:!t[e]}))},W=(e,t=!0)=>{if(e.startsWith("/category/")){let t=e.split("/category/")[1];return"/"===a&&T===t}return"/"===e?"/"===a&&!T:t?a===e:a.startsWith(e)},H=e=>!!e.startsWith("/category/")&&M===e.split("/category/")[1],B=(t,s)=>{if(s.children&&s.children.length>0&&"Resources"!==s.label){t.preventDefault(),O(s.href);return}if("Resources"===s.label||"#"===s.href)return void t.preventDefault();if("/"===s.href&&"Home"===s.label){t.preventDefault(),L(null),window.setFeedCategory&&window.setFeedCategory(null),E.push("/"),e?.();return}if(s.isFeedFilter){t.preventDefault();let r=s.href.split("/category/")[1];if(!r)return;if("/"!==a)E.push(`/?category=${r}`),e?.();else{if(T===r)return;R(r),L(r),window.setFeedCategory?(window.setFeedCategory(r),window.scrollTo({top:0,behavior:"smooth"}),setTimeout(()=>{R(null)},800)):(E.push(`/?category=${r}`),e?.()),e?.()}}},V=[{href:"/",label:"Home",icon:o.A},{href:"/jobs",label:"Jobs Hub",icon:l.A},{href:"/people",label:"People on the Move",icon:c.A},{href:"/rfps",label:"RFPs",icon:d.A},{href:"/vendors",label:"Vendor Directory",icon:l.A},{href:"/category/news",label:"News",icon:u.A,isFeedFilter:!0},{href:"/category/thought-leadership",label:"Thought Leadership",icon:m.A,isFeedFilter:!0},{href:"/category/podcast",label:"Podcasts",icon:h.A,isFeedFilter:!0},{href:"/resources",label:"Resources",icon:p.A,children:[{href:"/category/blog-post",label:"Blog Posts",icon:x.A,isFeedFilter:!0},{href:"/category/book",label:"Books",icon:f.A,isFeedFilter:!0},{href:"/category/case-study",label:"Case Studies",icon:d.A,isFeedFilter:!0},{href:"/category/course",label:"Courses",icon:g.A,isFeedFilter:!0},{href:"/category/presentation",label:"Presentations",icon:b.A,isFeedFilter:!0},{href:"/category/press-release",label:"Press Releases",icon:u.A,isFeedFilter:!0},{href:"/category/template",label:"Templates",icon:v.A,isFeedFilter:!0},{href:"/category/video",label:"Videos",icon:y.A,isFeedFilter:!0},{href:"/category/webinar",label:"Webinars",icon:j.A,isFeedFilter:!0},{href:"/category/whitepaper",label:"Whitepapers",icon:d.A,isFeedFilter:!0}]},{href:"/forum",label:"Community Q&A",icon:w.A},{href:"/category/event",label:"Events",icon:N.A,isFeedFilter:!0}],{visibleItems:J,collapsedItems:K,showMoreButton:G,navContainerRef:Y,newsletterRef:Z}=function(e){let[t,s]=(0,i.useState)(!1),[r,a]=(0,i.useState)(0),n=(0,i.useRef)(null),o=(0,i.useRef)(null);(0,i.useRef)(null);let l=(0,i.useMemo)(()=>[...e].sort((e,t)=>{let s=P.indexOf(e.label),r=P.indexOf(t.label);return -1===s&&-1===r?0:-1===s?-1:-1===r?1:r-s}),[e]),c=(0,i.useMemo)(()=>{if(!t||0===r)return{visibleItems:e,collapsedItems:[],showMoreButton:!1,isCollapsing:!1};let s=Math.max(1,Math.floor(r/64));if(s>=e.length)return{visibleItems:e,collapsedItems:[],showMoreButton:!1,isCollapsing:!1};let a=Math.max(1,s-1),n=l.slice(0,a),i=l.slice(a);return{visibleItems:n,collapsedItems:i,showMoreButton:i.length>0,isCollapsing:i.length>0}},[t,r,e,l]),d=(0,i.useCallback)(()=>{if(!n.current||!o.current)return;let e=n.current.getBoundingClientRect(),t=o.current.getBoundingClientRect(),r=t.top-e.bottom<100;s(r),r&&a(Math.max(0,t.top-e.top-50))},[]);return{...c,navContainerRef:n,newsletterRef:o,recalculate:d}}(V),X=(a,i=0)=>{let o=a.children&&a.children.length>0,l=W(a.href,!o),c=H(a.href),d="Resources"===a.label,u=o&&a.children.length>0&&!d,m=$[a.href]??l;return d&&o?"resources"===s&&t?(0,r.jsx)("div",{className:"space-y-3",children:a.children.map(t=>(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)(n(),{href:t.href,onClick:s=>{B(s,t),e?.()},className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 cursor-pointer","h-[52px]",W(t.href)?"text-gray-600 font-bold bg-[rgba(92,200,255,0.2)]":"text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.2)]"),children:[(0,r.jsx)(t.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:t.label}),H(t.href)&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5 animate-spin",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]})},t.href))},"resources-children"):"main"!==s&&t?null:(0,r.jsxs)("div",{className:"space-y-3",children:[t&&(0,r.jsxs)("button",{onClick:t,className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-all duration-200 ease-in-out w-full focus:outline-none focus:ring-0 border-0 cursor-pointer",0===i?"h-[52px]":"h-[52px] pl-8 text-sm",l?"text-gray-600 font-extrabold":"text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.15)]"),children:[(0,r.jsx)(a.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:a.label}),(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),!t&&(0,r.jsxs)(_.rI,{open:U,onOpenChange:Q,modal:!1,children:[(0,r.jsx)(_.ty,{asChild:!0,children:(0,r.jsxs)("button",{className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-all duration-200 ease-in-out w-full focus:outline-none focus:ring-0 border-0 cursor-pointer",0===i?"h-[52px]":"h-[52px] pl-8 text-sm",l?"text-gray-600 font-extrabold":"text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.15)]"),children:[(0,r.jsx)(a.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:a.label}),(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})}),(0,r.jsx)(_.SQ,{align:"start",side:"right",className:"w-72 rounded-[30px]",children:a.children.map((t,s)=>(0,r.jsxs)(_._2,{onSelect:s=>{s.preventDefault(),Q(!1),B({preventDefault:()=>{},currentTarget:{}},t),e?.()},className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 h-[52px] cursor-pointer",0===s&&"mt-0",s===a.children.length-1&&"mb-0",s!==a.children.length-1&&"mb-[3px]",W(t.href,!0)?"text-gray-600 font-bold bg-[rgba(92,200,255,0.2)]":"text-gray-600 font-medium",!W(t.href,!0)&&"hover:bg-[rgba(92,200,255,0.2)] focus:bg-[rgba(92,200,255,0.2)]"),children:[(0,r.jsx)(t.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:t.label})]},t.href))})]})]},a.href):(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(n(),{href:a.href,onClick:t=>{B(t,a),a.isFeedFilter||a.children?.length||"#"===a.href||e?.()},className:(0,S.cn)("flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 cursor-pointer w-full",0===i?"h-[52px]":"h-[52px] pl-8 text-sm",l?"text-gray-600 font-bold bg-[rgba(92,200,255,0.2)]":"text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.2)]","#"===a.href&&"text-gray-400 cursor-not-allowed"),children:[(0,r.jsx)(a.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:a.label}),c&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5 animate-spin",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),u&&!c&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:`h-5 w-5 transition-transform ${m?"rotate-90":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),m&&o&&(0,r.jsx)("div",{className:"mt-3 space-y-3",children:a.children.map(e=>X(e,i+1))})]},a.href)};return(0,r.jsx)("aside",{className:"flex w-full h-full",children:(0,r.jsxs)("div",{className:"flex flex-col h-full w-full p-4",children:[(0,r.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[(0,r.jsxs)("nav",{ref:Y,className:`flex flex-col gap-3 overflow-y-auto h-full transition-transform duration-300 ease-in-out ${"main"===s?"translate-x-0":"-translate-x-full"}`,children:[J.map(e=>X(e)),G&&(0,r.jsx)(F,{collapsedItems:K,onNavigate:e,isActive:W,isLoading:H,handleNavigation:B})]}),(0,r.jsx)("nav",{className:`flex flex-col gap-3 overflow-y-auto h-full absolute top-0 left-0 right-0 transition-transform duration-300 ease-in-out ${"resources"===s?"translate-x-0":"translate-x-full"}`,children:V.find(e=>"Resources"===e.label)?.children?.map(e=>X(e))})]}),(0,r.jsxs)("div",{ref:Z,className:"mt-auto pt-6 border-t border-[#3d405b]/20 relative",children:[(0,r.jsx)("button",{onClick:()=>{let e=!z;D(e),localStorage.setItem("newsletterVisible",e.toString())},className:"absolute -top-3 right-0 rounded-full w-6 h-6 bg-white flex items-center justify-center border border-gray-200 shadow-sm hover:shadow transition-all duration-200","aria-label":z?"Hide newsletter form":"Show newsletter form",children:z?(0,r.jsx)(C.A,{className:"h-3 w-3 text-gray-500"}):(0,r.jsx)(k.A,{className:"h-3 w-3 text-gray-500"})}),(0,r.jsxs)("div",{className:(0,S.cn)("transition-all duration-300 overflow-hidden",z?"max-h-[230px] opacity-100 pt-0 mb-4":"max-h-0 opacity-0 pt-0 mb-0"),children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-2",children:(0,r.jsx)("span",{className:"text-md font-bold text-gray-700",children:"Subscribe to our Newsletter"})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mb-4",children:"Stay updated with the latest news and insights"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("input",{type:"email",placeholder:"Email Address*",className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-full focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"}),(0,r.jsx)("button",{className:"w-full px-3 py-2 text-sm font-semibold bg-primary text-foreground rounded-full hover:bg-primary/90 transition-colors",children:"Sign Up"})]})]}),(0,r.jsxs)("div",{className:(0,S.cn)("text-[10px] text-gray-500 space-y-3 transition-all duration-300",z?"":"mt-6"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,r.jsx)("a",{href:"/sponsorships",className:"hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200",children:"Sponsorships"}),(0,r.jsx)("span",{children:"|"}),(0,r.jsx)("a",{href:"/terms-of-use",className:"hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200",children:"Terms & Use"}),(0,r.jsx)("span",{children:"|"}),(0,r.jsx)("a",{href:"/privacy-policy",className:"hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200",children:"Privacy Policy"})]}),(0,r.jsx)("div",{children:"\xa9 TourismIQ 2025. All rights reserved."})]})]})]})})}},28909:(e,t,s)=>{"use strict";s.d(t,{ErrorBoundary:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/ui/ErrorBoundary.tsx","ErrorBoundary");(0,r.registerClientReference)(function(){throw Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/ui/ErrorBoundary.tsx","useErrorHandler")},29103:(e,t,s)=>{"use strict";s.d(t,{UserAvatarWithRank:()=>l});var r=s(60687),a=s(43210),n=s(32584),i=s(3177),o=s(30474);function l({userId:e,avatarUrl:t,displayName:s,size:l="h-10 w-10",containerSize:c="w-12 h-12",showRankBadge:d=!0,userRoles:u=[],iqScoreData:m}){let h,{getUserIQ:p,getCachedUserIQ:x}=(0,i.m)(),[f,g]=(0,a.useState)(null),[b,v]=(0,a.useState)(!1);(0,a.useCallback)(async()=>{if(m)return void g({score:m.score,rank:{name:m.rank},rank_data:m.rank_data,total_activities:0,activities:[]});if(!b)try{v(!0);let t=await p(e);t&&g({score:t.iq_score||0,rank:t.rank?{name:"string"==typeof t.rank?t.rank:t.rank.name}:void 0,rank_data:void 0,total_activities:0,activities:[]})}catch(e){console.error("Error fetching user rank:",e)}finally{v(!1)}},[b,p,e,m]),(0,a.useCallback)(async()=>{},[]);let y=(()=>{let e=c.match(/w-(?:\[(\d+)px\]|(\d+))/),t=48;e&&(e[1]?t=parseInt(e[1]):e[2]&&(t=4*parseInt(e[2])));let s=Math.max(1.1*t,56);return{width:s,height:s}})(),j=(h=[],(u&&(Array.isArray(u)?u.length>0:Object.keys(u).length>0)?Array.isArray(u)?h=u:"object"==typeof u&&(h=Object.values(u)):h=[],h.includes("administrator")||h.includes("admin"))?"admin":h.includes("founder")?"founder":"member");return(0,r.jsxs)("div",{className:`relative flex items-center justify-center ${c}`,children:[d&&"admin"===j&&(0,r.jsx)(o.default,{src:`/images/icons/${j}.svg`,alt:`${j} badge`,width:y.width,height:y.height,className:"absolute inset-0 object-contain"}),d&&("member"===j||"founder"===j)&&f&&(0,r.jsx)(o.default,{src:((e,t=!1)=>{if(!e)return`/images/icons/novice${t?"-fc":""}.svg`;let s=e.toLowerCase();return`/images/icons/${s}${t?"-fc":""}.svg`})(f.rank?.name||f.rank_data?.name,"founder"===j),alt:`${f.rank?.name||f.rank_data?.name||"Member"} rank badge`,width:y.width,height:y.height,className:"absolute inset-0 object-contain"}),(0,r.jsxs)(n.eu,{className:`${l} relative z-10 border ${l.includes("150px")?"border-4 border-white bg-white":""}`,children:[(0,r.jsx)(n.BK,{src:t,alt:s,className:l.includes("150px")?"object-cover":""}),(0,r.jsx)(n.q5,{className:`bg-gray-100 ${l.includes("150px")?"text-xl":""}`,children:l.includes("150px")?s.split(" ").map(e=>e[0]).join("").toUpperCase():`${s.charAt(0)}${s.split(" ")[1]?.charAt(0)||""}`})]})]})}},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,r:()=>o});var r=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:n=!1,...l}){let c=n?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:s,className:e})),...l})}},30007:(e,t,s)=>{"use strict";s.d(t,{lH:()=>a,qQ:()=>r});let r=new(s(39091)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:3,refetchOnWindowFocus:!1,refetchOnReconnect:!1},mutations:{retry:1}}}),a={posts:{all:["posts"],lists:()=>[...a.posts.all,"list"],list:e=>[...a.posts.lists(),e],details:()=>[...a.posts.all,"detail"],detail:e=>[...a.posts.details(),e],userPosts:(e,t,s)=>[...a.posts.all,"user",e,{page:t,perPage:s}],byCategory:(e,t)=>[...a.posts.all,"category",e,t]},auth:{all:["auth"],status:()=>[...a.auth.all,"status"],profile:()=>[...a.auth.all,"profile"]},members:{all:["members"],lists:()=>[...a.members.all,"list"],list:e=>[...a.members.lists(),e],details:()=>[...a.members.all,"detail"],detail:e=>[...a.members.details(),e],profile:e=>[...a.members.all,"profile",e]},connections:{all:["connections"],status:e=>[...a.connections.all,"status",e],list:e=>[...a.connections.all,"list",e],pending:()=>[...a.connections.all,"pending"]},iqScore:{all:["iqScore"],user:e=>[...a.iqScore.all,"user",e],me:()=>[...a.iqScore.all,"me"],leaderboard:()=>[...a.iqScore.all,"leaderboard"],ranks:()=>[...a.iqScore.all,"ranks"]},forum:{all:["forum"],questions:e=>[...a.forum.all,"questions",e],question:e=>[...a.forum.all,"question",e],userQuestions:e=>[...a.forum.all,"user",e],comments:e=>[...a.forum.all,"comments",e]},messages:{all:["messages"],conversations:()=>[...a.messages.all,"conversations"],conversation:e=>[...a.messages.all,"conversation",e],unreadCount:()=>[...a.messages.all,"unreadCount"]},vendors:{all:["vendors"],lists:()=>[...a.vendors.all,"list"],list:e=>[...a.vendors.lists(),e],details:()=>[...a.vendors.all,"detail"],detail:e=>[...a.vendors.details(),e],posts:e=>[...a.vendors.all,"posts",e],categories:()=>[...a.vendors.all,"categories"]},categories:{all:["categories"],list:()=>[...a.categories.all,"list"]},bookmarks:{all:["bookmarks"],user:e=>[...a.bookmarks.all,"user",e]},notifications:{all:["notifications"],list:()=>[...a.notifications.all,"list"]}}},32584:(e,t,s)=>{"use strict";s.d(t,{BK:()=>l,eu:()=>o,q5:()=>c});var r=s(60687),a=s(43210),n=s(11096),i=s(4780);function o({className:e,...t}){return(0,r.jsx)(n.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function l({className:e,src:t,alt:s,onError:o,...l}){let[c,d]=a.useState(!1),[u,m]=a.useState("string"==typeof t?t:void 0);return(a.useEffect(()=>{if("string"==typeof t&&""!==t.trim()){let e=t;e.startsWith("http")||e.startsWith("/")||(e=`/${e.replace(/^\/+/,"")}`),m(e),d(!1)}else m(void 0)},[t]),c||!u)?null:(0,r.jsx)(n._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full object-cover",e),src:u,alt:s||"User avatar",onError:e=>{console.error("Failed to load avatar:",t),d(!0),o&&o(e)},...l})}function c({className:e,...t}){return(0,r.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},34199:(e,t,s)=>{"use strict";s.d(t,{RightSidebar:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call RightSidebar() from the server but RightSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/RightSidebar.tsx","RightSidebar");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/RightSidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/RightSidebar.tsx","default")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},37371:(e,t,s)=>{"use strict";function r(){let e="http://tourismiq-headless.local";return(e=e.replace(/\/+$/,"")).endsWith("/wp-json")||(e+="/wp-json"),e}s.d(t,{QZ:()=>o,e9:()=>r,iT:()=>l,oc:()=>n,q5:()=>i,tE:()=>a});let a=function(){let e="localhost:3000";return e=process.env.NEXT_PUBLIC_APP_URL?process.env.NEXT_PUBLIC_APP_URL.replace(/^https?:\/\//,""):"hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com",`https://${e}/api`}(),n=!1;function i(){return"include"}function o(e){return e.replace(/<[^>]*>?/gm,"").replace(/\[.*?\]/g,"").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#0?39;/g,"'").replace(/&#0?38;/g,"&").replace(/&#8217;/g,"'").replace(/&#8211;/g,"–").replace(/&#8212;/g,"—").replace(/&#8216;/g,"'").replace(/&#8220;/g,'"').replace(/&#8221;/g,'"').replace(/&#8230;/g,"…").trim()}function l(e){return e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#0?39;/g,"'").replace(/&#0?38;/g,"&").replace(/&#8217;/g,"'").replace(/&#8211;/g,"–").replace(/&#8212;/g,"—").replace(/&#8216;/g,"'").replace(/&#8220;/g,'"').replace(/&#8221;/g,'"').replace(/&#8230;/g,"…")}},38532:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},39727:()=>{},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var r=s(60687),a=s(43210),n=s(4780);let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6",e),...t}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6",e),...t}));u.displayName="CardFooter"},46952:(e,t,s)=>{"use strict";s.d(t,{E:()=>d,NotificationProvider:()=>c});var r=s(60687),a=s(43210),n=s(57405),i=s(7112);let o=(0,a.createContext)(void 0),l=new Set,c=({children:e})=>{let[t,s]=(0,a.useState)([]),[c,d]=(0,a.useState)(null),[u,m]=(0,a.useState)(!1),{isAuthenticated:h,user:p}=(0,i.A)(),x=t.filter(e=>!e.read&&"message"!==e.type).length;(0,a.useEffect)(()=>{if(!h||!p?.id)return c&&(c.disconnect(),d(null),m(!1)),s([]),()=>{};let e=(0,n.io)("http://localhost:3000",{transports:["websocket"],autoConnect:!0,withCredentials:!0,extraHeaders:{"Cache-Control":"no-cache",Pragma:"no-cache",Expires:"0"}});return e.on("connect",()=>{m(!0),p&&p.id?e.emit("authenticate",{userId:p.id}):console.error("Socket authentication failed: No user ID available")}),e.on("disconnect",()=>{m(!1)}),e.on("notification",e=>{if("message"===e.type)return;let t=`${e.type}:${e.referenceId}:${e.senderId}:${e.content}`;if(l.has(t))return;l.add(t),setTimeout(()=>{l.delete(t)},1e4);let r={...e,read:!1};s(e=>[r,...e])}),d(e),()=>{e.disconnect(),d(null),m(!1)}},[h,p?.id]),(0,a.useEffect)(()=>{},[p?.id]),(0,a.useEffect)(()=>{},[t,p?.id]),(0,a.useEffect)(()=>{},[]);let f=(0,a.useCallback)(async()=>{if(h&&p?.id)try{let e=await fetch("/api/wp-proxy/notifications",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)return void console.error("Failed to fetch notifications:",e.statusText);let t=await e.json();t.success&&t.notifications&&s(e=>{let s=new Set(e.map(e=>e.id)),r=t.notifications.filter(e=>!s.has(e.id)&&"message"!==e.type);return r.length>0?[...r,...e].sort((e,t)=>t.timestamp-e.timestamp):e})}catch(e){console.error("Error fetching notifications:",e)}},[h,p?.id]);(0,a.useEffect)(()=>{h&&p?.id&&f()},[h,p?.id,f]);let g=async e=>{if(s(t=>{let s=t.filter(t=>t.id!==e);return s}),/^\d+$/.test(e))try{let t=await fetch(`/api/wp-proxy/notifications?id=${e}`,{method:"DELETE",credentials:"include",headers:{"Content-Type":"application/json"}});t.ok||console.error("Failed to delete notification from database:",t.statusText)}catch(e){console.error("Error deleting notification:",e)}},b=async()=>{if(h&&p?.id)try{let e=await fetch("/api/wp-proxy/notifications",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)return void console.error("Failed to refresh notifications:",e.statusText);let t=await e.json();t.success&&t.notifications&&s(t.notifications)}catch(e){console.error("Error refreshing notifications:",e)}},v=async(e,t,s,r,a)=>{let n=!1;if(c&&u)try{c.emit("send_notification",{recipientId:e,type:t,content:s,referenceId:r,referenceType:a,senderId:p?.id}),n=!0}catch(e){console.error("Error sending notification via socket:",e)}try{let i=await fetch("/api/wp-proxy/notifications/send",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({recipient_id:e,type:t,content:s,reference_id:r,reference_type:a,sender_id:p?.id})});if(!i.ok){let e=await i.text();if(console.error("Notification API error:",e),!n)throw Error("Failed to send notification via REST API")}}catch(e){if(!n)throw e}};return(0,r.jsx)(o.Provider,{value:{notifications:t,unreadCount:x,socket:c,markAsRead:e=>{s(t=>t.map(t=>t.id===e?{...t,read:!0}:t))},markAllAsRead:()=>{s(e=>e.map(e=>({...e,read:!0})))},removeNotification:g,updateNotification:(e,t)=>{s(s=>s.map(s=>s.id===e?{...s,...t}:s))},addNotification:e=>{let t={...e,id:Math.random().toString(36).substring(2,11),timestamp:Date.now(),read:!1};s(e=>[t,...e])},clearNotifications:()=>{s([])},sendNotification:v,connected:u,fetchNotifications:f,refreshNotifications:b},children:e})},d=()=>{let e=(0,a.useContext)(o);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}},47990:()=>{},52548:(e,t,s)=>{"use strict";s.d(t,{WelcomeModal:()=>p});var r=s(60687),a=s(43210),n=s(63503),i=s(29523),o=s(45583),l=s(56085),c=s(41312),d=s(11437),u=s(99891),m=s(70334),h=s(25151);function p({onClose:e}){let[t,s]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),f=()=>{let t=new Date;t.setFullYear(t.getFullYear()+1),document.cookie=`tourismq_welcome_dismissed=true; expires=${t.toUTCString()}; path=/; SameSite=Lax`,s(!1),e?.()},g=[{icon:(0,r.jsx)(o.A,{className:"h-5 w-5"}),title:"Modern Tech Stack",description:"Upgraded to Next.js with enhanced performance and reliability"},{icon:(0,r.jsx)(l.A,{className:"h-5 w-5"}),title:"Fresh UI/UX",description:"Complete visual overhaul with improved navigation and user experience"},{icon:(0,r.jsx)(c.A,{className:"h-5 w-5"}),title:"Enhanced Features",description:"Better member directory, improved messaging, and streamlined vendor tools"},{icon:(0,r.jsx)(d.A,{className:"h-5 w-5"}),title:"New Hosting",description:"Faster, more reliable infrastructure for a smoother experience"}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.lG,{open:t,onOpenChange:f,children:(0,r.jsxs)(n.Cf,{className:"sm:max-w-[700px] max-h-[90vh] p-0 bg-white rounded-2xl shadow-2xl border-0 flex flex-col overflow-hidden",children:[(0,r.jsx)(n.c7,{children:(0,r.jsx)(n.L3,{className:"sr-only",children:"Welcome to TourismIQ V2"})}),(0,r.jsxs)("div",{className:"relative bg-gradient-to-br from-[#5cc8ff]/30 via-[#5cc8ff]/20 to-[#3d405b]/10 p-6 rounded-t-2xl overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#5cc8ff]/20 to-transparent rounded-full blur-2xl"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-[#3d405b]/10 to-transparent rounded-full blur-xl"}),(0,r.jsxs)("div",{className:"relative flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-3 bg-white rounded-2xl shadow-lg border border-[#5cc8ff]/20",children:(0,r.jsx)(l.A,{className:"h-8 w-8 text-[#5cc8ff]"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-[#3d405b] leading-tight mb-2",children:"Welcome to TourismIQ V2! \uD83C\uDF89"}),(0,r.jsx)("p",{className:"text-[#3d405b]/80 text-lg leading-relaxed",children:"We're excited to introduce our completely redesigned platform with major improvements across the board"})]})]})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-[#3d405b] mb-6 flex items-center",children:[(0,r.jsx)(l.A,{className:"h-5 w-5 text-[#5cc8ff] mr-2"}),"What's New:"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:g.map((e,t)=>(0,r.jsx)("div",{className:"group p-4 rounded-xl border-2 border-gray-100 hover:border-[#5cc8ff]/30 hover:bg-[#5cc8ff]/5 transition-all duration-300",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-[#5cc8ff]/10 text-[#5cc8ff] rounded-lg group-hover:bg-[#5cc8ff] group-hover:text-white transition-all duration-300",children:e.icon}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-semibold text-[#3d405b] text-sm mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]})]})},t))})]}),(0,r.jsxs)("div",{className:"mb-8 p-6 bg-gradient-to-r from-blue-50 to-[#5cc8ff]/10 rounded-xl border border-blue-100",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-[#3d405b] mb-3 flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-2"}),"For Existing Users:"]}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed mb-3",children:"Your account and data have been preserved. If you experience any login issues, please contact our support team and we'll help you get back in quickly."}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",className:"text-blue-600 border-blue-200 hover:bg-blue-50 rounded-full",onClick:()=>x(!0),children:"Contact Support"})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-[#3d405b] mb-3 flex items-center",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-[#5cc8ff] mr-2"}),"What's Next:"]}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Explore the new interface, connect with fellow tourism professionals, and discover enhanced tools to grow your business. We're just getting started!"})]}),(0,r.jsx)("div",{className:"text-center p-6 bg-gradient-to-r from-[#5cc8ff]/10 to-[#3d405b]/10 rounded-xl",children:(0,r.jsx)("p",{className:"text-[#3d405b] font-medium leading-relaxed",children:"Thank you for being part of our community. Here's to building the future of tourism together! \uD83D\uDE80"})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-3 mt-8",children:[(0,r.jsxs)(i.$,{onClick:f,className:"px-8 py-3 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/90 font-bold rounded-full transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center group",children:[(0,r.jsx)("span",{children:"Let's Explore!"}),(0,r.jsx)(m.A,{className:"h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"})]}),(0,r.jsx)(i.$,{variant:"outline",onClick:f,className:"px-8 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 font-semibold rounded-full transition-all duration-200",children:"Maybe Later"})]})]})]})}),(0,r.jsx)(h.R,{isOpen:p,onClose:()=>x(!1)})]})}},54278:(e,t,s)=>{"use strict";s.d(t,{Q:()=>c,UpvoteProvider:()=>l});var r=s(60687),a=s(43210),n=s(18332);let i=(0,a.createContext)(void 0),o={};function l({children:e}){let[t,s]=(0,a.useState)({}),{isLoggedIn:l}=(0,n.w1)(),c=(0,a.useRef)({}),d=e=>{let s=t[e];return!s||Date.now()-s.lastFetched>36e5?null:{upvoted:s.upvoted,count:s.count}},u=async e=>{let t=d(e);if(t)return t;if(e in o)return await o[e]||{upvoted:!1,count:0};let r=(async()=>{try{let t=await fetch(`/api/wp-proxy/posts/${e}/upvotes`,{credentials:"include",headers:{"Cache-Control":"max-age=300"}});if(t.ok){let r=await t.json();return s(t=>({...t,[e]:{upvoted:r.upvoted,count:r.count,lastFetched:Date.now()}})),{upvoted:r.upvoted,count:r.count}}throw Error("Failed to fetch upvote status")}catch(e){return console.error("Error fetching upvote status:",e),{upvoted:!1,count:0}}finally{delete o[e]}})();return o[e]=r,r},m=(0,a.useRef)(0),h=async e=>{let t=e.sort().join(",");if(c.current[t])return;let r=Date.now();if(r-m.current<1e4)return;m.current=r;let a=e.filter(e=>!d(e)&&!o[e]);if(0===a.length){c.current[t]=!0;return}try{let e=await fetch("/api/wp-proxy/posts/batch-upvotes",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json","Cache-Control":"max-age=300"},body:JSON.stringify({postIds:a})});if(e.ok){let t=await e.json(),r=Date.now();s(e=>{let s={...e};return Object.entries(t).forEach(([e,t])=>{s[Number(e)]={upvoted:t.upvoted,count:t.count,lastFetched:r}}),s})}else console.error("Error fetching batch upvotes:",await e.text())}catch(e){console.error("Error in batch fetch:",e)}c.current[t]=!0},p=async e=>{let r=t[e]||{upvoted:!1,count:0,lastFetched:0},a=!r.upvoted,n=r.count+(a?1:-1);s(t=>({...t,[e]:{...r,upvoted:a,count:n,lastFetched:Date.now()}}));try{let t=await fetch(`/api/wp-proxy/posts/${e}/upvote`,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to toggle upvote");let r=await t.json();return s(t=>({...t,[e]:{upvoted:r.upvoted,count:r.count,lastFetched:Date.now()}})),{upvoted:r.upvoted,count:r.count}}catch(t){return console.error("Error toggling upvote:",t),s(t=>({...t,[e]:r})),{upvoted:r.upvoted,count:r.count}}};return(0,r.jsx)(i.Provider,{value:{getUpvoteStatus:d,fetchUpvoteStatus:u,toggleUpvote:p,batchFetchUpvotes:h},children:e})}function c(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useUpvotes must be used within an UpvoteProvider");return e}},54413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(37413),a=s(4536),n=s.n(a);function i(){return(0,r.jsx)("div",{className:"min-h-screen bg-[#eff1f4] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-6xl font-bold text-gray-900 mb-4",children:"404"}),(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Page Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Sorry, we couldn't find the page you're looking for."}),(0,r.jsx)(n(),{href:"/",className:"inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors",children:"Return Home"})]})})}},55121:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23))},59507:(e,t,s)=>{"use strict";s.d(t,{MessagingProvider:()=>n,useMessaging:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call useMessaging() from the server but useMessaging is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/messaging/messaging-provider.tsx","useMessaging"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call MessagingProvider() from the server but MessagingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/messaging/messaging-provider.tsx","MessagingProvider")},61135:()=>{},63503:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>h,L3:()=>p,c7:()=>m,lG:()=>o,rr:()=>x,zM:()=>l});var r=s(60687);s(43210);var a=s(26134),n=s(11860),i=s(4780);function o({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,r.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,r.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",e),...t})}function u({className:e,children:t,...s}){return(0,r.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,r.jsx)(d,{}),(0,r.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-[0_32px_65px_-15px_rgba(0,0,0,0.9)] duration-200 sm:max-w-lg",e),...s,children:[t,(0,r.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function h({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function p({className:e,...t}){return(0,r.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,r.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},65885:(e,t,s)=>{"use strict";s.d(t,{f:()=>a});class r{constructor(){this.baseUrl="http://tourismiq-headless.local"}async getLeaderboard(e=20,t=!1){try{let s=t?`&force_refresh=1&_=${Date.now()}`:`&_=${Date.now()}`,r=await fetch(`/api/wp-proxy/iq-score/leaderboard?limit=${e}${s}`,{credentials:"include",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error(`HTTP error! status: ${r.status}`);return await r.json()}catch(e){throw console.error("Error fetching leaderboard:",e),e}}async getCurrentUserIQ(){try{let e=await fetch("/api/wp-proxy/iq-score/me",{credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(e){throw console.error("Error fetching current user IQ score:",e),e}}async getUserIQ(e){try{if(!e||isNaN(e)||e<=0)throw Error(`Invalid user ID: ${e}`);let t=await fetch(`/api/wp-proxy/iq-score/${e}`,{credentials:"include",headers:{"Content-Type":"application/json"},signal:AbortSignal.timeout(1e4)});if(!t.ok){let e="";try{let s=await t.json();e=s.error||s.message||""}catch{}throw Error(`HTTP error! status: ${t.status}${e?` - ${e}`:""}`)}let s=await t.json();if(!s||"object"!=typeof s)throw Error("Invalid response format");return s}catch(t){if(t instanceof Error&&"AbortError"===t.name)throw console.error(`Timeout fetching IQ score for user ${e}`),Error("Request timeout - please try again");throw console.error(`Error fetching IQ score for user ${e}:`,t),t}}async getRanks(){try{let e=await fetch("/api/wp-proxy/iq-score/ranks",{credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(e){throw console.error("Error fetching ranks:",e),e}}async awardPoints(e,t,s="manual"){try{let r=await fetch(`${this.baseUrl}/wp-json/tourismiq/v1/iq-score/award`,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:e,points:t,activity_type:s})});if(!r.ok)throw Error(`HTTP error! status: ${r.status}`);return await r.json()}catch(e){throw console.error("Error awarding points:",e),e}}getBadgeForUser(e,t){return t?e.founder_badge:e.member_badge}formatScore(e){return e.toLocaleString()}getRankColor(e){let t={Novice:"#94a3b8",Contributor:"#06b6d4",Engager:"#10b981",Influencer:"#f59e0b",Expert:"#f97316",Master:"#dc2626"};return t[e]||t.Novice||"#94a3b8"}getNextRankInfo(e,t){let s=t.find(t=>e>=t.min_points&&e<=t.max_points);if(!s)return{nextRank:null,pointsNeeded:0,progress:0};let r=t.find(e=>e.min_points>s.max_points);if(!r)return{nextRank:null,pointsNeeded:0,progress:100};let a=r.min_points-e,n=s.max_points-s.min_points+1;return{nextRank:r,pointsNeeded:a,progress:Math.min((e-s.min_points)/n*100,100)}}}let a=new r},66180:(e,t,s)=>{"use strict";s.d(t,{UpvoteProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call UpvoteProvider() from the server but UpvoteProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/UpvoteContext.tsx","UpvoteProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useUpvotes() from the server but useUpvotes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/UpvoteContext.tsx","useUpvotes")},66816:(e,t,s)=>{"use strict";s.d(t,{z:()=>n,oj:()=>d.oj,Lx:()=>i,y4:()=>o,eg:()=>l});var r=s(37371);async function a(){if(!r.oc)return null;try{let e=await fetch(`${r.tE}/user/me`,{method:"GET",credentials:"include"});if(!e.ok){if(401===e.status)return console.warn("User not authenticated (401 from /user/me)"),null;console.warn(`Failed to fetch user data from Next.js API: ${e.status}, trying WordPress API directly...`);let t=await fetch(`${r.tE}/wp-proxy/auth/status`,{method:"GET",credentials:"include"});if(!t.ok)return console.warn("Failed to fetch user data from WordPress API:",t.status),null;let s=await t.json();if(s&&s.user)return s.user;return null}return await e.json()}catch(e){return console.error("Error fetching current user:",e),null}}async function n(){try{let e=await fetch(`${r.tE}/wp-proxy/auth/status`,{method:"GET",credentials:"include"});if(!e.ok)return console.warn("Auth status check failed with status:",e.status),{isAuthenticated:!1,user:null};let t=await e.json();if(!(t.isAuthenticated||t.isLoggedIn||t.loggedIn)||!t.user)return{isAuthenticated:!1,user:null};let s=c(t.user);return{isAuthenticated:!0,user:s}}catch(e){return console.error("Error checking auth status:",e),{isAuthenticated:!1,user:null}}}async function i(e,t){try{let s=await fetch(`${r.tE}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t}),credentials:"include"});if(!s.ok){let e=await s.json().catch(()=>({}));return console.error("Login failed with status:",s.status,e),{success:!1,error:e.message||`Login failed: ${s.status}`}}let n=await s.json();if(!n.user){let e=await a();if(e){let t=c(e);return{success:!0,user:t}}return{success:!0,error:"Login successful but no user data returned"}}let i=c(n.user);return{success:!0,user:i}}catch(e){return console.error("Login error:",e),{success:!1,error:"An error occurred"}}}async function o(){try{if(!(await fetch(`${r.tE}/auth/logout`,{method:"POST",credentials:"include"})).ok)return!1;return!0}catch{return!1}}async function l(e){try{let t=await fetch(`${r.tE}/user/profile`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),credentials:(0,r.q5)()});if(!t.ok){let e=await t.json().catch(()=>({}));return{success:!1,error:e.message||`Update failed: ${t.status}`}}let s=await t.json();return{success:!0,user:s.user}}catch(e){return console.error("Error updating user profile:",e),{success:!1,error:e instanceof Error?e.message:"An error occurred"}}}function c(e){var t;let s=e.first_name||null,r=e.last_name||null,a=e.display_name||e.name||"",n=e.acf?.bio||null,i=e.acf?.job_title||null,o=e.acf?.company||null,l=e.acf?.location||null,c=e.acf?.phone||null,d=e.acf?.website||null,u=e.acf?.twitter||null,m=e.acf?.facebook||null,h=e.acf?.linkedin||null,p=e.acf?.instagram||null;return{id:e.id,username:e.username||e.user_login||e.slug,name:a,email:e.email||e.user_email||"",firstName:s,lastName:r,avatarUrl:function(e,t=96){if(!e)return"";if(e.acf?.profile_picture){if("string"==typeof e.acf.profile_picture)return e.acf.profile_picture;if("object"==typeof e.acf.profile_picture&&e.acf.profile_picture.url)return e.acf.profile_picture.url;if("object"==typeof e.acf.profile_picture&&e.acf.profile_picture.sizes){let s=`${t}x${t}`;return e.acf.profile_picture.sizes[s]?e.acf.profile_picture.sizes[s]:e.acf.profile_picture.url||""}}if(e.avatar_urls){let s=t.toString();if(e.avatar_urls[s])return e.avatar_urls[s];if(e.avatar_urls["96"])return e.avatar_urls["96"];if(e.avatar_urls["48"])return e.avatar_urls["48"];if(e.avatar_urls["24"])return e.avatar_urls["24"]}return""}(e),coverImageUrl:(t=e.coverImage)?"string"==typeof t?t:"object"==typeof t&&t.url?t.url:"":"",bio:"string"==typeof n?n:"",roles:function(e){if(Array.isArray(e.roles))return e.roles;if(e.roles&&"object"==typeof e.roles&&!Array.isArray(e.roles))return Object.values(e.roles);if(Array.isArray(e._user_roles))return e._user_roles;if("string"==typeof e.roles)try{let t=JSON.parse(e.roles);return Array.isArray(t)?t:[]}catch{return[e.roles]}if("string"==typeof e._user_roles)try{let t=JSON.parse(e._user_roles);return Array.isArray(t)?t:[]}catch{return[e._user_roles]}return e.capabilities?Object.keys("string"==typeof e.capabilities?JSON.parse(e.capabilities):e.capabilities).filter(e=>e.includes("um_")||"administrator"===e||"subscriber"===e||"editor"===e):["subscriber"]}(e),jobTitle:"string"==typeof i?i:"",company:"string"==typeof o?o:"",location:"string"==typeof l?l:"",phone:"string"==typeof c?c:"",website:"string"==typeof d?d:"",socialLinks:{twitter:u??null,facebook:m??null,linkedin:h??null,instagram:p??null},acf:e.acf?{...e.acf}:{bio:"string"==typeof n?n:"",job_title:"string"==typeof i?i:"",company:"string"==typeof o?o:"",location:"string"==typeof l?l:"",phone:"string"==typeof c?c:"",website:"string"==typeof d?d:"",twitter:u??"",facebook:m??"",linkedin:h??"",instagram:p??""}}}var d=s(9877)},67146:(e,t,s)=>{"use strict";s.d(t,{CG:()=>c,Fm:()=>x,Qs:()=>g,cj:()=>l,h:()=>p,kN:()=>d,qp:()=>f});var r=s(60687),a=s(43210),n=s(26134),i=s(24224),o=s(4780);let l=n.bL,c=n.l9,d=n.bm,u=({...e})=>(0,r.jsx)(n.ZL,{...e});u.displayName=n.ZL.displayName;let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.hJ,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:s}));m.displayName=n.hJ.displayName;let h=(0,i.F)("fixed z-50 gap-4 bg-background p-6 transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b shadow-lg data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t shadow-lg data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r shadow-[0_32px_65px_-15px_rgba(0,0,0,0.9)] data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l shadow-[0_32px_65px_-15px_rgba(0,0,0,0.9)] data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),p=a.forwardRef(({side:e="right",className:t,children:s,hideOverlay:a=!1,...i},l)=>(0,r.jsxs)(u,{children:[!a&&(0,r.jsx)(m,{}),(0,r.jsx)(n.UC,{ref:l,className:(0,o.cn)(h({side:e}),t),...i,children:s})]}));p.displayName=n.UC.displayName;let x=({className:e,...t})=>(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});x.displayName="SheetHeader";let f=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.hE,{ref:s,className:(0,o.cn)("text-lg font-semibold text-foreground",e),...t}));f.displayName=n.hE.displayName;let g=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.VY,{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=n.VY.displayName},68457:(e,t,s)=>{"use strict";s.d(t,{ZY:()=>u,n5:()=>d,qV:()=>c});var r=s(51423),a=s(8693),n=s(54050),i=s(30007),o=s(18332),l=s(7112);function c(e){let{isAuthenticated:t}=(0,l.A)(),{isAuthenticated:s}=(0,o.iZ)(),{data:a,isLoading:n,refetch:c}=(0,r.I)({queryKey:i.lH.connections.status(e),queryFn:async()=>{let t=await fetch(`/api/wp-proxy/connections/status/${e}`,{credentials:"include"});if(!t.ok)throw Error("Failed to get connection status");return t.json()},enabled:(t??s)&&!!e,staleTime:0,gcTime:3e5});return{status:a||null,loading:n,refreshStatus:c}}function d(e){let{isAuthenticated:t}=(0,l.A)(),{isAuthenticated:s}=(0,o.iZ)(),{data:a,isLoading:n,error:c,refetch:d}=(0,r.I)({queryKey:i.lH.connections.list(e),queryFn:async()=>{let t=e?`/api/wp-proxy/connections?userId=${e}`:"/api/wp-proxy/connections",s=await fetch(t,{credentials:"include"});if(!s.ok)throw Error("Failed to get connections");let r=await s.json();return Array.isArray(r)?r:r&&Array.isArray(r.connections)?r.connections:r&&r.data&&Array.isArray(r.data)?r.data:(console.warn("Unexpected connections response format:",r),[])},enabled:t??s,staleTime:6e5,gcTime:9e5,refetchOnWindowFocus:!1});return{connections:a||[],loading:n,error:c?.message||null,refreshConnections:d}}function u(){let e=function(){let e=(0,a.jE)();return(0,n.n)({mutationFn:async e=>{let t=await fetch("/api/wp-proxy/connections/request",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e}),credentials:"include"});if(!t.ok)throw Error((await t.json()).error||"Failed to send connection request");return!0},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:i.lH.connections.status(s)}),e.invalidateQueries({queryKey:i.lH.connections.pending()}),e.invalidateQueries({queryKey:["bulk-connections"],predicate:e=>{let t=e.queryKey;return!!(Array.isArray(t)&&"bulk-connections"===t[0]&&Array.isArray(t[1]))&&t[1].includes(s)}})}})}(),t=function(){let e=(0,a.jE)();return(0,n.n)({mutationFn:async e=>{let t=await fetch("/api/wp-proxy/connections/accept",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e}),credentials:"include"});if(!t.ok)throw Error((await t.json()).error||"Failed to accept connection request");return!0},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:i.lH.connections.status(s)}),e.invalidateQueries({queryKey:i.lH.connections.pending()}),e.invalidateQueries({queryKey:i.lH.connections.all}),e.invalidateQueries({queryKey:["bulk-connections"],predicate:e=>{let t=e.queryKey;return!!(Array.isArray(t)&&"bulk-connections"===t[0]&&Array.isArray(t[1]))&&t[1].includes(s)}})}})}(),s=function(){let e=(0,a.jE)();return(0,n.n)({mutationFn:async e=>{let t=await fetch("/api/wp-proxy/connections/decline",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e}),credentials:"include"});if(!t.ok)throw Error((await t.json()).error||"Failed to decline connection request");return!0},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:i.lH.connections.status(s)}),e.invalidateQueries({queryKey:i.lH.connections.pending()}),e.invalidateQueries({queryKey:["bulk-connections"],predicate:e=>{let t=e.queryKey;return!!(Array.isArray(t)&&"bulk-connections"===t[0]&&Array.isArray(t[1]))&&t[1].includes(s)}})}})}(),r=function(){let e=(0,a.jE)();return(0,n.n)({mutationFn:async e=>{let t=await fetch("/api/wp-proxy/connections/remove",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e}),credentials:"include"});if(!t.ok)throw Error((await t.json()).error||"Failed to remove connection");return!0},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:i.lH.connections.status(s)}),e.invalidateQueries({queryKey:i.lH.connections.all}),e.invalidateQueries({queryKey:["bulk-connections"],predicate:e=>{let t=e.queryKey;return!!(Array.isArray(t)&&"bulk-connections"===t[0]&&Array.isArray(t[1]))&&t[1].includes(s)}})}})}(),o=async t=>{try{return await e.mutateAsync(t),!0}catch(e){return console.error("Failed to send connection request:",e),!1}},l=async e=>{try{return await t.mutateAsync(e),!0}catch(e){return console.error("Failed to accept connection request:",e),!1}},c=async e=>{try{return await s.mutateAsync(e),!0}catch(e){return console.error("Failed to decline connection request:",e),!1}},d=async e=>{try{return await r.mutateAsync(e),!0}catch(e){return console.error("Failed to remove connection:",e),!1}},u=async e=>{try{let t=await fetch(`/api/wp-proxy/connections/status/${e}`,{credentials:"include"});if(!t.ok)throw Error("Failed to get connection status");return await t.json()}catch(e){return console.error("Failed to get connection status:",e),null}},m=async()=>{try{let e=await fetch("/api/wp-proxy/connections/pending",{credentials:"include"});if(!e.ok)throw Error("Failed to get pending requests");return await e.json()}catch(e){return console.error("Failed to get pending requests:",e),null}},h=async e=>{try{let t=e?`/api/wp-proxy/connections?userId=${e}`:"/api/wp-proxy/connections",s=await fetch(t,{credentials:"include"});if(!s.ok)throw Error("Failed to get connections");let r=await s.json();if(Array.isArray(r))return r;if(r&&Array.isArray(r.connections))return r.connections;if(r&&r.data&&Array.isArray(r.data))return r.data;return console.warn("Unexpected connections response format:",r),[]}catch(e){return console.error("Failed to get connections:",e),null}};return{loading:e.isPending||t.isPending||s.isPending||r.isPending,error:e.error?.message||t.error?.message||s.error?.message||r.error?.message||null,sendConnectionRequest:o,acceptConnectionRequest:l,declineConnectionRequest:c,removeConnection:d,getConnectionStatus:u,getPendingRequests:m,getConnections:h,clearError:()=>{e.reset(),t.reset(),s.reset(),r.reset()}}}},68926:(e,t,s)=>{"use strict";s.d(t,{Header:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx","Header")},69857:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Sidebar.tsx","Sidebar")},71114:(e,t,s)=>{"use strict";s.d(t,{J:()=>r});let r={async sendMessage(e){let t=await fetch("/api/wp-proxy/messages/send",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to send message");return t.json()},async getConversations(){let e=await fetch("/api/wp-proxy/messages/conversations",{method:"GET",credentials:"include"});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch conversations");return e.json()},async getConversationMessages(e,t=1,s=50){let r=await fetch(`/api/wp-proxy/messages/conversation/${e}?page=${t}&per_page=${s}`,{method:"GET",credentials:"include"});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch messages");return r.json()},async markMessagesAsRead(e){let t=await fetch("/api/wp-proxy/messages/mark-read",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({conversation_with:e})});if(!t.ok)throw Error((await t.json()).message||"Failed to mark messages as read")},async getUnreadCount(){let e=await fetch("/api/wp-proxy/messages/unread-count",{method:"GET",credentials:"include"});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch unread count");return e.json()},async canMessageUser(e){let t=await fetch(`/api/wp-proxy/messages/can-message/${e}`,{method:"GET",credentials:"include"});if(!t.ok)throw Error((await t.json()).message||"Failed to check messaging permissions");return t.json()}}},71248:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(60687),a=s(43210),n=s(29523),i=s(63503),o=s(43649);function l({isOpen:e,onClose:t,onConfirm:s,title:l,message:c,confirmText:d="Confirm",cancelText:u="Cancel",variant:m="default",loading:h=!1}){let[p,x]=(0,a.useState)(!1),f=async()=>{x(!0);try{await s()}finally{x(!1)}},g=h||p;return(0,r.jsx)(i.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(i.Cf,{className:"sm:max-w-md",children:[(0,r.jsxs)(i.c7,{children:[(0,r.jsxs)(i.L3,{className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-orange-500"}),l]}),(0,r.jsx)(i.rr,{className:"text-left",children:c})]}),(0,r.jsxs)(i.Es,{className:"flex flex-col sm:flex-row gap-2",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:t,disabled:g,className:"w-full sm:w-auto border-2 border-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold",children:u}),(0,r.jsx)(n.$,{onClick:f,disabled:g,className:`w-full sm:w-auto font-extrabold ${"destructive"===m?"bg-red-600 hover:bg-red-700 text-white":"bg-[#5cc8ff] hover:bg-[#5cc8ff]/80 text-[#3d405b]"}`,children:g?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"}),d,"..."]}):d})]})]})})}function c(){let[e,t]=(0,a.useState)({isOpen:!1,title:"",message:"",onConfirm:()=>{}});return{confirm:(e,s="Confirm Action",r)=>new Promise(a=>{t({isOpen:!0,title:s,message:e,confirmText:r?.confirmText||"Confirm",cancelText:r?.cancelText||"Cancel",variant:r?.variant||"default",onConfirm:()=>{t(e=>({...e,isOpen:!1})),a(!0)},onCancel:()=>{t(e=>({...e,isOpen:!1})),a(!1)}})}),ConfirmationDialogComponent:()=>(0,r.jsx)(l,{isOpen:e.isOpen,onClose:()=>{t(e=>({...e,isOpen:!1})),e.onCancel?.()},onConfirm:e.onConfirm,title:e.title,message:e.message,confirmText:e.confirmText,cancelText:e.cancelText,variant:e.variant})}}},71858:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/auth-context.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/auth-context.tsx","useAuth")},73276:(e,t,s)=>{"use strict";s.d(t,{Header:()=>ez});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n),o=s(29523),l=s(99270),c=s(58869),d=s(84027),u=s(65668),m=s(58887),h=s(40083),p=s(7112),x=s(78201),f=s(32584),g=s(96834),b=s(67146),v=s(46952),y=s(92512),j=s(8693),w=s(30007),N=s(23026),C=s(67760),k=s(33872),A=s(81620),S=s(92363),_=s(45583),P=s(67268),E=s(40228),F=s(97051),q=s(41862),$=s(13964),I=s(11860),T=s(88233),L=s(96362),M=s(30474),R=s(71248);function z({isOpen:e,onClose:t,onNotificationsRead:s}){let{notifications:n,unreadCount:i,markAsRead:l,markAllAsRead:c,removeNotification:d,updateNotification:m}=(0,v.E)(),{acceptConnectionRequest:h,declineConnectionRequest:p}=(0,y.ZY)(),z=(0,j.jE)(),[D,U]=(0,a.useState)(null),{confirm:Q,ConfirmationDialogComponent:O}=(0,R.Z)(),W=(0,a.useMemo)(()=>{let e=new Date,t=new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime(),s=t-864e5,r=t-6048e5,a=n.filter(e=>"message"!==e.type);return{today:a.filter(e=>new Date(e.timestamp).getTime()>=t),yesterday:a.filter(e=>{let r=new Date(e.timestamp).getTime();return r>=s&&r<t}),thisWeek:a.filter(e=>{let t=new Date(e.timestamp).getTime();return t>=r&&t<s}),earlier:a.filter(e=>new Date(e.timestamp).getTime()<r)}},[n,"all"]),H=e=>{try{return(0,x.m)(new Date(e),{addSuffix:!0})}catch{return"some time ago"}},B=async()=>{if(await Q("Are you sure you want to delete all notifications? This action cannot be undone.","Delete All Notifications"))for(let e of n.map(e=>e.id))await d(e)},V=async()=>{c(),s?.()},J=async(e,t)=>{U(`accept-${e}`);try{await h(t)?(m(e,{type:"connection_accepted",content:"Connection request accepted",read:!0}),z.invalidateQueries({queryKey:w.lH.connections.all}),z.invalidateQueries({queryKey:w.lH.connections.status(t)})):console.error("Failed to accept connection request")}catch(e){console.error("Error accepting connection:",e)}finally{U(null)}},K=async(e,t)=>{U(`decline-${e}`);try{await p(t)?(d(e),z.invalidateQueries({queryKey:w.lH.connections.all}),z.invalidateQueries({queryKey:w.lH.connections.status(t)})):console.error("Failed to decline connection request")}catch(e){console.error("Error declining connection:",e)}finally{U(null)}},G=(e,t)=>{switch(e){case"connection_request":return"rgba(236, 72, 153, 0.1)";case"message":default:return"rgba(92, 200, 255, 0.1)";case"upvote":return"rgba(239, 68, 68, 0.1)";case"comment":case"event":return"rgba(249, 115, 22, 0.1)";case"share":return"rgba(132, 204, 22, 0.1)";case"iq_points_earned":return t?.rankedUp,"rgba(245, 158, 11, 0.1)";case"iq_update":case"qa_answer":case"qa_comment":return"rgba(59, 130, 246, 0.1)"}},Y=(e,t)=>{switch(e){case"connection_request":return(0,r.jsx)(N.A,{className:"h-5 w-5 text-pink-500"});case"message":return(0,r.jsx)(M.default,{src:"/images/icons/icon-messages.svg",alt:"Messages",width:20,height:20,className:"h-5 w-5",style:{filter:"hue-rotate(180deg) saturate(2) brightness(1.2)"}});case"upvote":return(0,r.jsx)(C.A,{className:"h-5 w-5 text-red-500"});case"comment":return(0,r.jsx)(k.A,{className:"h-5 w-5 text-orange-500"});case"share":return(0,r.jsx)(A.A,{className:"h-5 w-5 text-lime-500"});case"iq_points_earned":if(t?.rankedUp)return(0,r.jsx)(S.A,{className:"h-5 w-5 text-amber-500"});return(0,r.jsx)(_.A,{className:"h-5 w-5 text-amber-500"});case"iq_update":return(0,r.jsx)(P.A,{className:"h-5 w-5 text-blue-500"});case"qa_answer":case"qa_comment":return(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-500"});case"event":return(0,r.jsx)(E.A,{className:"h-5 w-5 text-orange-500"});default:return(0,r.jsx)(F.A,{className:"h-5 w-5 text-[#5cc8ff]"})}},Z=e=>{if("connection_request"===e.type){let t=D===`accept-${e.id}`,s=D===`decline-${e.id}`,a=t||s,n=e.senderId,i=e.senderName||"Someone",l=e.senderAvatar;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsxs)(f.eu,{className:"h-8 w-8",children:[(0,r.jsx)(f.BK,{src:l||"",alt:i}),(0,r.jsx)(f.q5,{className:"text-xs",children:i?i.charAt(0).toUpperCase():"U"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:i}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"wants to connect with you"})]})]}),(0,r.jsxs)("div",{className:"flex gap-2 mt-3",children:[(0,r.jsx)(o.$,{size:"sm",className:"flex-1",onClick:()=>J(e.id,n),disabled:a,children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(q.A,{className:"h-3 w-3 mr-1 animate-spin"}),"Accepting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)($.A,{className:"h-3 w-3 mr-1"}),"Accept"]})}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",className:"flex-1",onClick:()=>K(e.id,n),disabled:a,children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(q.A,{className:"h-3 w-3 mr-1 animate-spin"}),"Declining..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(I.A,{className:"h-3 w-3 mr-1"}),"Decline"]})})]})]})}return"iq_points_earned"===e.type?(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm text-gray-700",children:e.content})}):(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-700",children:e.content}),e.senderName&&(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["from ",e.senderName]})]})},X=(e,t)=>0===t.length?null:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-500 mb-2",children:e}),(0,r.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,r.jsxs)("div",{className:`flex gap-3 p-3 rounded-lg transition-colors duration-200 ${!e.read?"bg-[rgba(92,200,255,0.1)] border-l-2 border-[#5cc8ff]":"bg-white hover:bg-gray-50"}`,onClick:()=>l(e.id),children:[(0,r.jsx)("div",{className:"h-10 w-10 rounded-full flex items-center justify-center flex-shrink-0",style:{backgroundColor:G(e.type,e)},children:Y(e.type,e)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:H(e.timestamp)}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[!e.read&&(0,r.jsx)(g.E,{className:"bg-[#22c55e] text-white text-xs py-0 px-1.5 h-4",children:"New"}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-6 w-6 text-gray-400 hover:text-red-500 hover:bg-red-50",onClick:async t=>{t.stopPropagation(),await d(e.id)},children:(0,r.jsx)(T.A,{className:"h-3 w-3"})})]})]}),Z({...e,content:e.content??""})]})]},e.id))})]}),ee=Object.values(W).some(e=>e.length>0);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.cj,{open:e,onOpenChange:e=>!e&&t(),children:(0,r.jsxs)(b.h,{className:"w-[400px] rounded-none p-0 overflow-y-auto",side:"right",hideOverlay:!0,children:[(0,r.jsx)(b.Fm,{className:"sr-only",children:(0,r.jsx)(b.qp,{children:"Notifications"})}),(0,r.jsxs)("div",{className:"sticky top-0 bg-white z-10 px-6 py-4 border-b",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(F.A,{className:"h-5 w-5 text-[#5cc8ff]"}),(0,r.jsx)("h2",{className:"text-xl font-bold",children:"Notifications"}),i>0&&(0,r.jsxs)(g.E,{className:"bg-[#22c55e] text-white ml-2",children:[i," new"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-8 w-8 rounded-full hover:bg-red-50 hover:text-red-500",onClick:B,disabled:0===n.length,title:"Delete all notifications",children:(0,r.jsx)(L.A,{className:"h-4 w-4"})}),(0,r.jsx)(b.kN,{asChild:!0,children:(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-8 w-8 rounded-full hover:bg-gray-100",onClick:t,children:(0,r.jsx)(I.A,{className:"h-4 w-4"})})})]})]}),i>0&&(0,r.jsx)("div",{className:"mt-3",children:(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:V,className:"w-full",children:"Mark all as read"})})]}),(0,r.jsx)("div",{className:"px-6 py-4",children:ee?(0,r.jsxs)("div",{className:"space-y-6",children:[X("Today",W.today),X("Yesterday",W.yesterday),X("This Week",W.thisWeek),X("Earlier",W.earlier)]}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-16 text-center",children:[(0,r.jsx)("div",{className:"bg-gray-100 rounded-full p-4 mb-4",children:(0,r.jsx)(F.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-1",children:"No notifications"}),(0,r.jsx)("p",{className:"text-gray-500 max-w-xs",children:"You're all caught up! Check back later for new activity."})]})})]})}),(0,r.jsx)(O,{})]})}var D=s(14089),U=s(96882),Q=s(9989),O=s(4780);function W({delayDuration:e=0,...t}){return(0,r.jsx)(Q.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function H({...e}){return(0,r.jsx)(W,{children:(0,r.jsx)(Q.bL,{"data-slot":"tooltip",...e})})}function B({...e}){return(0,r.jsx)(Q.l9,{"data-slot":"tooltip-trigger",...e})}function V({className:e,sideOffset:t=0,children:s,...a}){return(0,r.jsx)(Q.ZL,{children:(0,r.jsxs)(Q.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,O.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...a,children:[s,(0,r.jsx)(Q.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}var J=s(29103),K=s(65885);function G({isOpen:e,onClose:t}){let[s,n]=(0,a.useState)([]),[l,c]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null),m=async(e=!0)=>{try{c(!0),u(null);let t=await K.f.getLeaderboard(20,e);t.success?n(t.data):u("Failed to load leaderboard")}catch(e){console.error("Error fetching leaderboard:",e),u("Failed to load leaderboard")}finally{c(!1)}},h=e=>e.display_name||e.username,p=e=>{if(e.profile_picture&&"string"==typeof e.profile_picture&&""!==e.profile_picture.trim()&&!e.profile_picture.includes("gravatar.com")&&!e.profile_picture.includes("secure.gravatar.com"))return e.profile_picture},x=e=>1===e?"border-amber-200":2===e?"border-zinc-200":3===e?"border-amber-700/30":"border-gray-200",f=e=>1===e?"bg-amber-400":2===e?"bg-zinc-300":3===e?"bg-amber-700":"bg-gray-300";return(0,r.jsx)(b.cj,{open:e,onOpenChange:e=>!e&&t(),children:(0,r.jsxs)(b.h,{className:"w-[400px] rounded-none p-0 overflow-y-auto",side:"right",hideOverlay:!0,children:[(0,r.jsxs)(b.Fm,{className:"sr-only",children:[(0,r.jsx)(b.qp,{children:"TourismIQ Leaderboard"}),(0,r.jsx)(b.Qs,{children:"Top users ranked by IQ score"})]}),(0,r.jsxs)("div",{className:"sticky top-0 bg-white z-10 px-6 py-4 border-b",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(_.A,{className:"h-6 w-6 text-primary"}),(0,r.jsx)("h2",{className:"text-xl font-bold",children:"TourismIQ Leaderboard"})]}),(0,r.jsx)(b.kN,{asChild:!0,children:(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-8 w-8 rounded-full hover:bg-gray-100",onClick:t,children:(0,r.jsx)(I.A,{className:"h-4 w-4"})})})]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground mt-2",children:["Top users ranked by IQ score. Engage more to increase your score!",(0,r.jsx)(W,{children:(0,r.jsxs)(H,{children:[(0,r.jsx)(B,{asChild:!0,children:(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-5 w-5 p-0 ml-1",children:(0,r.jsx)(U.A,{className:"h-4 w-4"})})}),(0,r.jsx)(V,{children:(0,r.jsx)("p",{className:"max-w-xs",children:"IQ Scores are earned through various activities: creating content, engaging with others, daily logins, and community participation."})})]})})]})]}),l&&(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading leaderboard..."})]})}),d&&!l&&(0,r.jsx)("div",{className:"px-6 py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-red-600 mb-4",children:d}),(0,r.jsx)(o.$,{onClick:()=>m(),variant:"outline",size:"sm",children:"Try Again"})]})}),!l&&!d&&s.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-gradient-to-br from-[#fef7cd] via-[#e0e7ff] via-[#fce7f3] to-[#dbeafe] opacity-90 px-6 py-8",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Top Contributors"}),(0,r.jsx)("div",{className:"space-y-6",children:s.slice(0,3).map((e,s)=>{let a=s+1;return(0,r.jsxs)("div",{className:`flex items-center gap-4 p-4 bg-white rounded-[20px] border-2 ${x(a)}`,style:{boxShadow:"0 10px 30px rgba(0,0,0,0.12), 0 4px 12px rgba(0,0,0,0.08)"},children:[(0,r.jsxs)("div",{className:"relative flex-shrink-0",children:[(0,r.jsx)(J.UserAvatarWithRank,{userId:e.id,avatarUrl:p(e)||"/images/avatar-placeholder.svg",displayName:h(e),size:"h-16 w-16",containerSize:"w-20 h-20",showRankBadge:!0,userRoles:e.is_founder?["founder"]:[],iqScoreData:{score:e.iq_score,rank:e.rank.name,rank_data:e.rank}}),(0,r.jsx)("div",{className:`absolute -bottom-2 -right-2 ${f(a)} rounded-full p-1`,children:(0,r.jsx)(P.A,{className:"h-5 w-5 text-white"})})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,r.jsxs)("span",{className:`text-sm font-semibold ${1===a?"text-amber-500":2===a?"text-zinc-500":"text-amber-700"}`,children:[1===a?"1st":2===a?"2nd":"3rd"," Place"]})}),(0,r.jsxs)(i(),{href:`/profile/${e.username}`,className:"group",onClick:t,children:[(0,r.jsx)("div",{className:"font-bold group-hover:underline",children:h(e)}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["@",e.username]})]}),(0,r.jsxs)("div",{className:"mt-1 text-base font-bold text-primary",children:[K.f.formatScore(e.iq_score)," points"]})]})]},e.id)})})]}),s.length>3&&(0,r.jsxs)("div",{className:"px-6 py-4",children:[(0,r.jsx)("h3",{className:"text-md font-semibold mb-4",children:"Leaderboard"}),(0,r.jsx)("div",{className:"space-y-3",children:s.slice(3).map(e=>(0,r.jsxs)(i(),{href:`/profile/${e.username}`,className:"flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors",onClick:t,children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 text-center font-semibold text-muted-foreground",children:e.position}),(0,r.jsx)(J.UserAvatarWithRank,{userId:e.id,avatarUrl:p(e)||"/images/avatar-placeholder.svg",displayName:h(e),size:"h-10 w-10",containerSize:"w-14 h-14",showRankBadge:!0,userRoles:e.is_founder?["founder"]:[],iqScoreData:{score:e.iq_score,rank:e.rank.name,rank_data:e.rank}}),(0,r.jsxs)("div",{className:"flex-1 min-w-0 ml-3",children:[(0,r.jsx)("div",{className:"font-medium truncate",children:h(e)}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground truncate",children:["@",e.username]})]}),(0,r.jsx)("div",{className:"ml-2 font-bold text-primary",children:K.f.formatScore(e.iq_score)})]},e.id))})]})]}),!l&&!d&&0===s.length&&(0,r.jsx)("div",{className:"px-6 py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(_.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"No leaderboard data available yet."})]})})]})})}var Y=s(12941),Z=s(28559),X=s(32192),ee=s(80659),et=s(41312),es=s(49653),er=s(20798),ea=s(63654),en=s(44689),ei=s(82080),eo=s(10022),el=s(8403),ec=s(27351),ed=s(550),eu=s(78005),em=s(28440),eh=s(39233),ep=s(16189);function ex({onNavigate:e=()=>{},onResourcesClick:t,currentMenu:s="main"}={}){let n=(0,ep.usePathname)(),o=(0,ep.useRouter)(),[l,c]=(0,a.useState)({resources:n.startsWith("/resources")}),[d,u]=(0,a.useState)(null),[h,p]=(0,a.useState)(null),x=e=>{c(t=>({...t,[e]:!t[e]}))},f=(e,t=!0)=>{if(e.startsWith("/category/")){let t=e.split("/category/")[1];return"/"===n&&d===t}return"/"===e?"/"===n&&!d:t?n===e:n.startsWith(e)},g=e=>!!e.startsWith("/category/")&&h===e.split("/category/")[1],b=(t,s)=>{if(s.children&&s.children.length>0&&"Resources"!==s.label){t.preventDefault(),x(s.href);return}if("Resources"===s.label||"#"===s.href)return void t.preventDefault();if("/"===s.href&&"Home"===s.label){t.preventDefault(),u(null),window.setFeedCategory&&window.setFeedCategory(null),o.push("/"),e?.();return}if(s.isFeedFilter){t.preventDefault();let r=s.href.split("/category/")[1];if(!r)return;if("/"!==n)o.push(`/?category=${r}`),e?.();else{if(d===r)return;p(r),u(r),window.setFeedCategory?(window.setFeedCategory(r),window.scrollTo({top:0,behavior:"smooth"}),setTimeout(()=>{p(null)},800)):(o.push(`/?category=${r}`),e?.()),e?.()}}},v=[{href:"/",label:"Home",icon:X.A},{href:"/jobs",label:"Jobs Hub",icon:ee.A},{href:"/people",label:"People on the Move",icon:et.A},{href:"/rfps",label:"RFPs",icon:es.A},{href:"/vendors",label:"Vendor Directory",icon:ee.A},{href:"/member-directory",label:"Member Directory",icon:et.A},{href:"/category/news",label:"News",icon:er.A,isFeedFilter:!0},{href:"/category/thought-leadership",label:"Thought Leadership",icon:ea.A,isFeedFilter:!0},{href:"/category/podcast",label:"Podcasts",icon:en.A,isFeedFilter:!0},{href:"/resources",label:"Resources",icon:ei.A,children:[{href:"/category/blog-post",label:"Blog Posts",icon:eo.A,isFeedFilter:!0},{href:"/category/book",label:"Books",icon:el.A,isFeedFilter:!0},{href:"/category/case-study",label:"Case Studies",icon:es.A,isFeedFilter:!0},{href:"/category/course",label:"Courses",icon:ec.A,isFeedFilter:!0},{href:"/category/presentation",label:"Presentations",icon:ed.A,isFeedFilter:!0},{href:"/category/press-release",label:"Press Releases",icon:er.A,isFeedFilter:!0},{href:"/category/template",label:"Templates",icon:eu.A,isFeedFilter:!0},{href:"/category/video",label:"Videos",icon:em.A,isFeedFilter:!0},{href:"/category/webinar",label:"Webinars",icon:eh.A,isFeedFilter:!0},{href:"/category/whitepaper",label:"Whitepapers",icon:es.A,isFeedFilter:!0}]},{href:"/forum",label:"Community Q&A",icon:m.A},{href:"/category/event",label:"Events",icon:E.A,isFeedFilter:!0}],y=(a,n=0)=>{let o=a.children&&a.children.length>0,c=f(a.href,!o),d=g(a.href),u="Resources"===a.label,m=o&&a.children.length>0&&!u,h=l[a.href]??c;return u&&o?"resources"===s&&t?(0,r.jsx)("div",{className:"space-y-1",children:a.children.map(t=>(0,r.jsx)("div",{className:"space-y-1",children:(0,r.jsxs)(i(),{href:t.href,onClick:s=>{b(s,t),e?.()},className:(0,O.cn)("flex items-center gap-3 rounded-full px-4 text-base transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 cursor-pointer","h-10",f(t.href)?"text-gray-600 font-bold bg-[rgba(92,200,255,0.2)]":"text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.2)]"),children:[(0,r.jsx)(t.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:t.label}),g(t.href)&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5 animate-spin",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]})},t.href))},"resources-children"):"main"===s?(0,r.jsx)("div",{className:"space-y-1",children:(0,r.jsxs)("button",{onClick:t,className:(0,O.cn)("flex items-center gap-3 rounded-full px-4 text-base transition-all duration-200 ease-in-out w-full focus:outline-none focus:ring-0 border-0 cursor-pointer",0===n?"h-10":"h-10 pl-6 text-sm",c?"text-gray-600 font-extrabold":"text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.15)]"),children:[(0,r.jsx)(a.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:a.label}),(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})},a.href):null:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)(i(),{href:a.href,onClick:t=>{b(t,a),a.isFeedFilter||a.children?.length||"#"===a.href||e?.()},className:(0,O.cn)("flex items-center gap-3 rounded-full px-4 text-base transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 cursor-pointer w-full",0===n?"h-10":"h-10 pl-6 text-sm",c?"text-gray-600 font-bold bg-[rgba(92,200,255,0.2)]":"text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.2)]","#"===a.href&&"text-gray-400 cursor-not-allowed"),children:[(0,r.jsx)(a.icon,{className:"h-5 w-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:a.label}),d&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:"h-5 w-5 animate-spin",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),m&&!d&&(0,r.jsx)("span",{className:"ml-auto",children:(0,r.jsx)("svg",{className:`h-5 w-5 transition-transform ${h?"rotate-90":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),h&&o&&(0,r.jsx)("div",{className:"mt-1 space-y-1",children:a.children.map(e=>y(e,n+1))})]},a.href)};return(0,r.jsxs)("div",{className:"flex flex-col w-full h-full",children:[(0,r.jsxs)("div",{className:"flex-1 p-4",children:[(0,r.jsx)("nav",{className:`flex flex-col gap-1 ${"main"===s?"block":"hidden"}`,children:v.map(e=>y(e))}),(0,r.jsx)("nav",{className:`flex flex-col gap-1 ${"resources"===s?"block":"hidden"}`,children:v.find(e=>"Resources"===e.label)?.children?.map(e=>y(e))})]}),(0,r.jsxs)("div",{className:"p-4 pt-2 border-t border-[#3d405b]/20 mt-4",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-2",children:(0,r.jsx)("span",{className:"text-sm font-bold text-gray-700",children:"Subscribe to our Newsletter"})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mb-3",children:"Stay updated with the latest news and insights"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("input",{type:"email",placeholder:"Email Address*",className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-full focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"}),(0,r.jsx)("button",{className:"w-full px-3 py-2 text-sm font-semibold bg-primary text-foreground rounded-full hover:bg-primary/90 transition-colors",children:"Sign Up"})]})]}),(0,r.jsxs)("div",{className:"text-[10px] text-gray-500 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,r.jsx)("a",{href:"/sponsorships",className:"hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200",children:"Sponsorships"}),(0,r.jsx)("span",{children:"|"}),(0,r.jsx)("a",{href:"/terms-of-use",className:"hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200",children:"Terms & Use"}),(0,r.jsx)("span",{children:"|"}),(0,r.jsx)("a",{href:"/privacy-policy",className:"hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200",children:"Privacy Policy"})]}),(0,r.jsx)("div",{children:"\xa9 TourismIQ 2025. All rights reserved."})]})]})]})}function ef(){let[e,t]=(0,a.useState)(!1),[s,n]=(0,a.useState)("main");return(0,r.jsxs)(b.cj,{open:e,onOpenChange:e=>{t(e),e||n("main")},children:[(0,r.jsx)(b.CG,{asChild:!0,children:(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"min-[1280px]:hidden",children:[(0,r.jsx)(Y.A,{className:"size-[1.4rem]"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})}),(0,r.jsxs)(b.h,{side:"left",className:"w-[280px] p-0 z-50",children:[(0,r.jsxs)(b.Fm,{className:"sr-only",children:[(0,r.jsx)(b.qp,{children:"resources"===s?"Resources Menu":"Navigation Menu"}),(0,r.jsx)(b.Qs,{children:"resources"===s?"Browse resources and content categories":"Navigate to different sections of the site"})]}),"resources"===s&&(0,r.jsx)("div",{className:"px-4 py-3 border-b",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"icon",onClick:()=>{n("main")},className:"h-8 w-8 transition-transform duration-200 hover:scale-110 animate-in fade-in-0 slide-in-from-left-2",children:(0,r.jsx)(Z.A,{className:"h-4 w-4"})}),(0,r.jsx)("h2",{className:"text-center flex-1 text-lg font-semibold",children:"Resources"})]})}),(0,r.jsx)("div",{className:"h-[calc(100vh-60px)] overflow-y-auto",children:(0,r.jsx)(ex,{onNavigate:()=>t(!1),onResourcesClick:()=>{n("resources")},currentMenu:s})})]})]})}var eg=s(21342),eb=s(25151),ev=s(63503),ey=s(89667),ej=s(34729),ew=s(15079),eN=s(80013);function eC({isOpen:e,onClose:t}){let[s,n]=(0,a.useState)({name:"",email:"",category:"",rating:"",feedback:""}),[i,l]=(0,a.useState)(!1),c=async e=>{e.preventDefault(),l(!0);try{(await fetch("/api/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).ok?(alert("Feedback submitted successfully! Thank you for your input."),n({name:"",email:"",category:"",rating:"",feedback:""}),t()):alert("Failed to submit feedback. Please try again.")}catch(e){console.error("Error submitting feedback:",e),alert("An error occurred. Please try again.")}finally{l(!1)}};return(0,r.jsx)(ev.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(ev.Cf,{className:"sm:max-w-[600px] max-h-[90vh] p-0 bg-white rounded-2xl shadow-2xl border-0 flex flex-col overflow-hidden",children:[(0,r.jsx)(ev.c7,{children:(0,r.jsx)(ev.L3,{className:"sr-only",children:"Share Feedback"})}),(0,r.jsx)("div",{className:"relative bg-gradient-to-r from-[#5cc8ff]/30 to-[#5cc8ff]/20 p-6 rounded-t-2xl",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-white rounded-xl shadow-sm",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-[#5cc8ff]"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-[#3d405b] leading-tight",children:"Share Feedback"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Help us improve by sharing your thoughts and suggestions"})]})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,r.jsxs)("form",{onSubmit:c,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eN.J,{htmlFor:"name",className:"text-sm font-semibold text-gray-700",children:"Full Name *"}),(0,r.jsx)(ey.p,{id:"name",type:"text",required:!0,value:s.name,onChange:e=>n(t=>({...t,name:e.target.value})),className:"h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200",placeholder:"Enter your full name"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eN.J,{htmlFor:"email",className:"text-sm font-semibold text-gray-700",children:"Email Address *"}),(0,r.jsx)(ey.p,{id:"email",type:"email",required:!0,value:s.email,onChange:e=>n(t=>({...t,email:e.target.value})),className:"h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200",placeholder:"Enter your email"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eN.J,{htmlFor:"category",className:"text-sm font-semibold text-gray-700",children:"Feedback Category *"}),(0,r.jsxs)(ew.l6,{value:s.category,onValueChange:e=>n(t=>({...t,category:e})),required:!0,children:[(0,r.jsx)(ew.bq,{className:"h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200",children:(0,r.jsx)(ew.yv,{placeholder:"Select category"})}),(0,r.jsxs)(ew.gC,{children:[(0,r.jsx)(ew.eb,{value:"general",children:"General Feedback"}),(0,r.jsx)(ew.eb,{value:"feature",children:"Feature Request"}),(0,r.jsx)(ew.eb,{value:"bug",children:"Bug Report"}),(0,r.jsx)(ew.eb,{value:"ui",children:"User Interface"}),(0,r.jsx)(ew.eb,{value:"performance",children:"Performance"}),(0,r.jsx)(ew.eb,{value:"content",children:"Content Quality"}),(0,r.jsx)(ew.eb,{value:"other",children:"Other"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eN.J,{htmlFor:"rating",className:"text-sm font-semibold text-gray-700",children:"Overall Rating *"}),(0,r.jsxs)(ew.l6,{value:s.rating,onValueChange:e=>n(t=>({...t,rating:e})),required:!0,children:[(0,r.jsx)(ew.bq,{className:"h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200",children:(0,r.jsx)(ew.yv,{placeholder:"Select rating"})}),(0,r.jsxs)(ew.gC,{children:[(0,r.jsx)(ew.eb,{value:"5",children:"⭐⭐⭐⭐⭐ Excellent"}),(0,r.jsx)(ew.eb,{value:"4",children:"⭐⭐⭐⭐ Very Good"}),(0,r.jsx)(ew.eb,{value:"3",children:"⭐⭐⭐ Good"}),(0,r.jsx)(ew.eb,{value:"2",children:"⭐⭐ Fair"}),(0,r.jsx)(ew.eb,{value:"1",children:"⭐ Poor"})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eN.J,{htmlFor:"feedback",className:"text-sm font-semibold text-gray-700",children:"Your Feedback *"}),(0,r.jsx)(ej.T,{id:"feedback",required:!0,value:s.feedback,onChange:e=>n(t=>({...t,feedback:e.target.value})),className:"min-h-[140px] px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200 resize-none",placeholder:"Please share your detailed feedback, suggestions, or concerns. What did you like? What could be improved? Any specific features you'd like to see?"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Your feedback helps us make TourismIQ better for everyone. We appreciate your input!"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(o.$,{type:"button",variant:"outline",onClick:t,className:"px-8 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 font-semibold rounded-full transition-all duration-200",children:"Cancel"}),(0,r.jsx)(o.$,{type:"submit",disabled:i,className:"px-8 py-3 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-bold rounded-full transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed",children:i?"Submitting...":"Submit Feedback"})]})]})})]})})}var ek=s(79410),eA=s(57800),eS=s(5446),e_=s(48730),eP=s(97992),eE=s(70334),eF=s(51215);function eq({isOpen:e,onClose:t,children:s}){let n=(0,a.useRef)(null);return e?(0,eF.createPortal)((0,r.jsx)("div",{className:"fixed inset-0 z-[9999] flex items-start justify-center",onClick:e=>{e.target===e.currentTarget&&t()},style:{backgroundColor:"rgba(0, 0, 0, 0.4)",backdropFilter:"blur(8px)",WebkitBackdropFilter:"blur(8px)"},children:(0,r.jsx)("div",{ref:n,className:"relative mt-[8vh] mx-4 w-full max-w-4xl lg:max-w-5xl",style:{background:"rgba(255, 255, 255, 0.95)",backdropFilter:"blur(16px)",WebkitBackdropFilter:"blur(16px)",borderRadius:"16px",boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.2)",border:"1px solid rgba(255, 255, 255, 0.2)",maxHeight:"85vh",overflow:"hidden"},onClick:e=>e.stopPropagation(),children:s})}),document.body):null}let e$=[{id:"posts",label:"Posts",icon:eo.A},{id:"users",label:"People",icon:c.A},{id:"vendors",label:"Vendors",icon:ek.A},{id:"forum",label:"Forum",icon:m.A},{id:"rfps",label:"RFPs",icon:eA.A},{id:"jobs",label:"Jobs",icon:eS.A}];function eI({isOpen:e,onClose:t}){let[s,n]=(0,a.useState)(""),[d,u]=(0,a.useState)({posts:[],users:[],vendors:[],forum:[],rfps:[],jobs:[],total:0}),[h,p]=(0,a.useState)("posts"),[x,f]=(0,a.useState)(!1),[b,v]=(0,a.useState)(-1),[y,j]=(0,a.useState)([]),w=(0,a.useRef)(null),N=(0,a.useRef)(null);(0,a.useRef)(null);let C=(0,a.useRef)(null),k=(0,a.useCallback)(e=>{let t=[e,...y.filter(t=>t!==e)].slice(0,5);j(t),localStorage.setItem("search-recent",JSON.stringify(t))},[y]);(0,a.useCallback)(async e=>{if(!e.trim()||e.length<2){u({posts:[],users:[],vendors:[],forum:[],rfps:[],jobs:[],total:0}),f(!1);return}C.current&&C.current.abort();try{C.current=new AbortController;let t=await fetch(`/api/search?q=${encodeURIComponent(e)}&limit=25`,{signal:C.current.signal,credentials:"include"});if(t.ok){let e=await t.json();u(e)}}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;console.error("Search error:",e)}finally{f(!1)}},[]);let A=(0,a.useMemo)(()=>d[h]||[],[d,h]),S=e=>{switch(e){case"post":default:return eo.A;case"user":return c.A;case"vendor":return ek.A;case"forum":return m.A;case"rfp":return eA.A;case"job":return eS.A}},_=()=>{k(s),t()},P=e=>{n(e),v(-1)};return(0,r.jsx)(eq,{isOpen:e,onClose:t,children:(0,r.jsxs)("div",{className:"overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center px-6 py-4 border-b border-gray-100",children:[(0,r.jsx)(l.A,{className:"h-5 w-5 text-gray-400 mr-3 flex-shrink-0"}),(0,r.jsx)(ey.p,{ref:w,type:"text",placeholder:"Search posts, people, vendors, forum, and RFPs...",value:s,onChange:e=>n(e.target.value),className:"flex-1 border-0 bg-transparent text-lg placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none",style:{fontSize:"18px"}}),x&&(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-blue-500 mr-3 flex-shrink-0"}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",onClick:t,className:"h-8 w-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full flex-shrink-0",children:(0,r.jsx)(I.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex bg-gray-50/50 overflow-x-auto scrollbar-hide",children:e$.map(e=>{let t=e.icon,s="posts"===e.id?d.totalPosts||d.posts.length:d[e.id]?.length||0;return(0,r.jsxs)("button",{onClick:()=>{p(e.id),v(-1)},className:`flex items-center space-x-2 px-3 lg:px-4 py-3 text-sm font-medium transition-all flex-shrink-0 justify-center min-w-fit ${h===e.id?"text-blue-600 bg-white border-b-2 border-blue-500 shadow-sm":"text-gray-600 hover:text-gray-900 hover:bg-white/50"}`,children:[(0,r.jsx)(t,{className:"h-4 w-4 flex-shrink-0"}),(0,r.jsx)("span",{className:"whitespace-nowrap",children:e.label}),s>0&&(0,r.jsx)(g.E,{variant:"secondary",className:"ml-1 text-xs bg-gray-100 text-gray-600 flex-shrink-0",children:s})]},e.id)})}),(0,r.jsxs)("div",{ref:N,className:"max-h-96 overflow-y-auto bg-white",children:[!s.trim()&&y.length>0&&(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("h3",{className:"text-sm font-semibold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(e_.A,{className:"h-4 w-4 mr-2 text-gray-500"}),"Recent Searches"]}),(0,r.jsx)("div",{className:"space-y-1",children:y.map((e,t)=>(0,r.jsx)("button",{onClick:()=>P(e),className:"w-full text-left px-3 py-2 rounded-lg hover:bg-gray-50 text-sm text-gray-700 transition-colors",children:e},t))})]}),s.trim()&&0===A.length&&!x&&(0,r.jsxs)("div",{className:"p-12 text-center",children:[(0,r.jsx)(l.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-6"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No results found"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Try adjusting your search terms or check a different category."})]}),A.length>0&&(0,r.jsx)("div",{className:"p-2",children:A.map((e,t)=>{let s=S(e.type),a=t===b;return(0,r.jsx)(i(),{href:e.link,onClick:_,"data-result-index":t,className:`block p-4 mx-2 rounded-xl transition-all duration-150 ${a?"bg-blue-50 border border-blue-200 shadow-sm scale-[1.02]":"hover:bg-gray-50 border border-transparent"}`,children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:"user"===e.type&&e.avatar_url?(0,r.jsx)(M.default,{src:e.avatar_url,alt:e.name||"",width:40,height:40,className:"w-10 h-10 rounded-full object-cover"}):"vendor"===e.type&&e.featured_media?(0,r.jsx)(M.default,{src:e.featured_media,alt:e.title||"",width:40,height:40,className:"w-10 h-10 rounded-lg object-contain bg-white p-1"}):"post"===e.type&&e.featured_media?(0,r.jsx)(M.default,{src:e.featured_media,alt:e.title||"",width:40,height:40,className:"w-10 h-10 rounded-lg object-cover"}):(0,r.jsx)("div",{className:"w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center",children:(0,r.jsx)(s,{className:"h-5 w-5 text-gray-600"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:["job"===e.type&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 line-clamp-2",children:e.title}),(0,r.jsx)("div",{className:"flex gap-1 ml-2 flex-shrink-0",children:"NEW"===e.status&&(0,r.jsx)(g.E,{className:"bg-[#22c55e] text-white text-xs",children:"New"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs text-gray-500 mb-1",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[e.company&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(ek.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.company})]}),e.location&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(eP.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.location})]})]}),(0,r.jsxs)("div",{className:"space-y-1 text-right",children:[e.date&&(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,r.jsx)(E.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsxs)("span",{children:["Posted ",new Date(e.date).toLocaleDateString()]})]}),e.deadline&&(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,r.jsx)(e_.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsx)("span",{children:e.deadline})]})]})]})]}),"rfp"===e.type&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 line-clamp-2",children:e.title}),(0,r.jsx)("div",{className:"flex gap-1 ml-2 flex-shrink-0",children:"NEW"===e.status&&(0,r.jsx)(g.E,{className:"bg-[#22c55e] text-white text-xs",children:"New"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs text-gray-500 mb-1",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[e.company&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(ek.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.company})]}),e.location&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(eP.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.location})]}),e.category&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(eo.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.category})]})]}),(0,r.jsxs)("div",{className:"space-y-1 text-right",children:[e.date&&(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,r.jsx)(E.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsxs)("span",{children:["Posted ",new Date(e.date).toLocaleDateString()]})]}),e.deadline&&(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,r.jsx)(e_.A,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsx)("span",{children:e.deadline})]})]})]})]}),"job"!==e.type&&"rfp"!==e.type&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.title||e.name||e.question}),e.is_paid&&(0,r.jsx)(g.E,{variant:"default",className:"text-xs bg-yellow-100 text-yellow-800",children:"Paid"})]}),(e.excerpt||e.content||e.description)&&(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-1 overflow-hidden",style:{display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical"},children:e.excerpt||e.content||e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[e.author&&(0,r.jsxs)("span",{children:["by ",e.author]}),e.job_title&&(0,r.jsx)("span",{children:e.job_title}),e.company&&(0,r.jsxs)("span",{children:["at ",e.company]}),e.location&&(0,r.jsx)("span",{children:e.location}),e.job_type&&(0,r.jsx)("span",{children:e.job_type}),e.salary_range&&(0,r.jsx)("span",{children:e.salary_range}),void 0!==e.votes&&(0,r.jsxs)("span",{children:[e.votes," votes"]}),void 0!==e.comments_count&&(0,r.jsxs)("span",{children:[e.comments_count," comments"]}),e.categories&&e.categories.length>0&&(0,r.jsx)("span",{children:e.categories.slice(0,2).join(", ")})]})]})]}),(0,r.jsx)(eE.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0"})]})},`${e.type}-${e.id}`)})})]}),(0,r.jsx)("div",{className:"border-t border-gray-100 px-6 py-3 bg-gray-50/50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-6",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)("kbd",{className:"px-1.5 py-0.5 text-xs font-mono bg-gray-200 rounded mr-1",children:"↑↓"}),"Navigate"]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)("kbd",{className:"px-1.5 py-0.5 text-xs font-mono bg-gray-200 rounded mr-1",children:"↵"}),"Open"]}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)("kbd",{className:"px-1.5 py-0.5 text-xs font-mono bg-gray-200 rounded mr-1",children:"Esc"}),"Close"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 md:ml-auto",children:[A.length>0&&A.length>=25&&(0,r.jsx)("button",{onClick:()=>{k(s),t(),window.location.href=`/search?q=${encodeURIComponent(s)}`},className:"text-blue-600 hover:text-blue-700 font-medium transition-colors",children:"View all results"}),d.total>0&&(0,r.jsxs)("span",{className:"font-medium text-gray-600",children:[A.length,A.length>=25?"+":""," of ",d.total," result",1!==d.total?"s":""]})]})]})})]})})}var eT=s(88920),eL=s(92576),eM=s(2975);function eR(){let[e,t]=(0,a.useState)(!1);return(0,r.jsx)(eT.N,{children:e&&(0,r.jsxs)(eL.P.button,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},onClick:()=>{let e=window.pageYOffset,t=performance.now(),s=r=>{let a=Math.min((r-t)/400,1),n=e*(1-(a<.5?4*a*a*a:(a-1)*(2*a-2)*(2*a-2)+1));window.scrollTo(0,n),a<1&&requestAnimationFrame(s)};requestAnimationFrame(s)},className:"fixed bottom-4 left-[34%] transform -translate-x-1/2 md:absolute md:top-[110px] lg:top-[130px] lg:left-[31%] xl:left-[45%] md:bottom-auto bg-[#5cc8ff] text-[#3d405b] px-3 py-2 rounded-full shadow-lg flex items-center gap-1.5 transition-colors duration-200 text-sm font-bold hover:bg-[#5cc8ff]/90 z-40","aria-label":"Back to top",children:[(0,r.jsx)(eM.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Back to top"})]})})}function ez(){let{user:e,isAuthenticated:t,isLoading:s,logout:n}=(0,p.A)(),{unreadCount:x}=(0,v.E)(),{openMessageList:f,hasUnreadMessages:g,totalUnreadCount:b}=(0,D.I$)(),[y,j]=(0,a.useState)(!1),[w,N]=(0,a.useState)(!1),[C,k]=(0,a.useState)(!1),[A,S]=(0,a.useState)(!1),[_,P]=(0,a.useState)(!1),[E,F]=(0,a.useState)(!1),q=async()=>{try{await n()}catch(e){console.error("Logout error:",e)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("header",{className:"border-b bg-white sticky top-0 z-50 h-[80px] md:h-[90px]",children:(0,r.jsxs)("div",{className:"w-full h-full flex items-center justify-center px-3 md:px-4 relative",children:[(0,r.jsxs)("div",{className:"w-full max-w-[1360px] flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2 md:space-x-8",children:[(0,r.jsx)(ef,{}),(0,r.jsxs)(i(),{href:"/",className:"flex items-center h-[36px] md:h-[42px] object-contain",onClick:()=>{window.setFeedCategory&&window.setFeedCategory(null)},children:[(0,r.jsx)(M.default,{src:"/images/icons/icon-tourism.svg",alt:"TourismIQ",height:28,width:28,className:"h-7 w-7 md:hidden object-contain"}),(0,r.jsx)(M.default,{src:"/images/logo.svg",alt:"TourismIQ",height:42,width:168,className:"hidden md:block h-[42px] max-w-[168px] object-contain"})]}),E&&!s&&t&&(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"min-[1024px]:hidden text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff]/20 h-8 w-8 sm:h-9 sm:w-9",onClick:()=>P(!0),title:"Search",children:(0,r.jsx)(l.A,{className:"size-[1.4rem] sm:h-[18px] sm:w-[18px]"})})]}),(0,r.jsx)("div",{className:"flex-1"}),E&&!s&&(0,r.jsx)("div",{className:"flex items-center ml-auto space-x-2 md:space-x-4 pr-2 md:pr-4",children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"relative text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10",onClick:f,title:"Messages",children:[(0,r.jsx)(M.default,{src:"/images/icons/icon-messages.svg",alt:"Messages",width:18,height:18,className:"h-[18px] w-[18px] md:h-5 md:w-5"}),g&&(0,r.jsx)("span",{className:"absolute -top-0.5 -right-0.5 md:-top-1 md:-right-1 flex h-4 w-4 md:h-5 md:w-5 items-center justify-center rounded-full bg-red-500 text-[9px] md:text-[10px] text-white font-bold",children:b>9?"9+":b})]}),(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"relative text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10",onClick:()=>j(!0),children:[(0,r.jsx)(M.default,{src:"/images/icons/icon-notification.svg",alt:"Notifications",width:18,height:18,className:"h-[18px] w-[18px] md:h-5 md:w-5"}),x>0&&(0,r.jsx)("span",{className:"absolute -top-0.5 -right-0.5 md:-top-1 md:-right-1 flex h-4 w-4 md:h-5 md:w-5 items-center justify-center rounded-full bg-red-500 text-[9px] md:text-[10px] text-white font-bold",children:x>9?"9+":x})]}),(0,r.jsx)(z,{isOpen:y,onClose:()=>j(!1),onNotificationsRead:()=>{}}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10",onClick:()=>N(!0),title:"View Leaderboard",children:(0,r.jsx)(M.default,{src:"/images/icons/icon-leaderboard.svg",alt:"Leaderboard",width:18,height:18,className:"h-[18px] w-[18px] md:h-5 md:w-5"})}),(0,r.jsx)(G,{isOpen:w,onClose:()=>N(!1)}),(0,r.jsxs)(eg.rI,{modal:!1,children:[(0,r.jsx)(eg.ty,{asChild:!0,children:(0,r.jsx)(o.$,{variant:"ghost",className:"relative h-10 w-10 md:h-12 md:w-12 rounded-full p-0 hover:bg-[#5cc8ff]/10 cursor-pointer",asChild:!0,children:(0,r.jsx)("div",{children:(0,r.jsx)(J.UserAvatarWithRank,{userId:e?.id||0,avatarUrl:e?.avatarUrl||("string"==typeof e?.acf?.profile_picture?e.acf.profile_picture:e?.acf?.profile_picture?.url)||e?.acf?.avatar_url||"/images/avatar-placeholder.svg",displayName:e?.name||"User",size:"h-7 w-7 md:h-8 md:w-8",containerSize:"w-10 h-10 md:w-12 md:h-12",showRankBadge:!0,userRoles:e?.roles})})})}),(0,r.jsxs)(eg.SQ,{className:"w-56 rounded-[30px] shadow-[0_20px_60px_-12px_rgba(0,0,0,0.25),0_8px_24px_-4px_rgba(0,0,0,0.15)] p-4",align:"end",forceMount:!0,children:[(0,r.jsx)(eg.lp,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:e?.name||"User"}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:e?.email||""})]})}),(0,r.jsx)(eg.mB,{}),(0,r.jsx)(eg._2,{asChild:!0,className:"flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)] cursor-pointer",children:(0,r.jsxs)(i(),{href:e&&e.username?`/profile/${e.username}`:"/profile",className:"w-full flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profile"})]})}),(0,r.jsx)(eg._2,{asChild:!0,className:"flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)] cursor-pointer",children:(0,r.jsxs)(i(),{href:"/settings",className:"w-full flex items-center",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]})}),(0,r.jsxs)(eg._2,{className:"cursor-pointer flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]",onClick:()=>k(!0),children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Support"})]}),(0,r.jsxs)(eg._2,{className:"cursor-pointer flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]",onClick:()=>S(!0),children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Feedback"})]}),(0,r.jsxs)(eg._2,{className:"cursor-pointer flex items-center rounded-full px-4 py-2 text-red-600 font-medium transition-all duration-200 ease-in-out hover:bg-red-50 focus:bg-red-50 focus:text-red-700",onClick:q,children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.$,{variant:"outline",asChild:!0,className:"text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold h-8 px-3 text-sm md:h-10 md:px-4 md:text-base",children:(0,r.jsx)(i(),{href:"/login",children:"Log In"})}),(0,r.jsx)(o.$,{asChild:!0,className:"bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-extrabold border-2 border-[#5cc8ff] h-8 px-3 text-sm md:h-10 md:px-4 md:text-base",children:(0,r.jsx)(i(),{href:"/register",children:"Sign Up"})})]})})]}),(0,r.jsx)("div",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-[350px] lg:max-w-[400px] px-4 hidden min-[1024px]:block",children:(0,r.jsx)("button",{onClick:()=>P(!0),className:"relative w-full h-10 lg:h-12 bg-gray-100 border border-gray-200 rounded-full hover:bg-gray-50 transition-colors group",children:(0,r.jsxs)("div",{className:"flex items-center h-full",children:[(0,r.jsx)(l.A,{className:"absolute left-3 h-4 w-4 lg:h-5 lg:w-5 text-gray-400 group-hover:text-gray-600"}),(0,r.jsx)("span",{className:"pl-9 lg:pl-10 pr-10 lg:pr-12 text-sm lg:text-base text-gray-500 group-hover:text-gray-700",children:"Search posts, people, vendors..."}),(0,r.jsx)("div",{className:"absolute right-3 text-xs text-muted-foreground",children:"⌘K"})]})})}),(0,r.jsx)(eR,{})]})}),(0,r.jsx)(eb.R,{isOpen:C,onClose:()=>k(!1)}),(0,r.jsx)(eC,{isOpen:A,onClose:()=>S(!1)}),(0,r.jsx)(eI,{isOpen:_,onClose:()=>P(!1)})]})}},78973:(e,t,s)=>{"use strict";s.d(t,{MessagingProvider:()=>m,useMessaging:()=>u});var r=s(60687),a=s(43210),n=s(82949),i=s(5972),o=s(7112),l=s(46952),c=s(71114);let d=(0,a.createContext)(void 0),u=()=>{let e=(0,a.useContext)(d);if(!e)throw Error("useMessaging must be used within a MessagingProvider");return e};function m({children:e}){let{user:t}=(0,o.A)(),{socket:s}=(0,l.E)(),[u,m]=(0,a.useState)(!1),[h,p]=(0,a.useState)([]),[x,f]=(0,a.useState)(!1),[g,b]=(0,a.useState)(0),v=(0,a.useCallback)(e=>({bottom:0,right:8+328*e}),[]),y=(0,a.useCallback)(()=>{m(e=>e)},[]),j=(0,a.useCallback)(()=>{m(!0)},[]),w=(0,a.useCallback)(()=>{m(!1)},[]),N=(0,a.useCallback)(e=>{p(t=>{let s=e.participants[0]?.id?.toString(),r=t.findIndex(e=>e.conversation.participants[0]?.id?.toString()===s);if(-1!==r)return t.map((e,t)=>t===r?{...e,isMinimized:!1}:e);let a={conversation:e,position:v(t.length),isMinimized:!1,isMaximized:!1};return[...t,a]}),m(!1);let t=e.participants[0];if(t){let e=parseInt(t.id);c.J.markMessagesAsRead(e).catch(console.error)}},[v]),C=e=>{let t=h.findIndex(t=>t.conversation.id===e);-1!==t&&p(s=>s.filter(t=>t.conversation.id!==e).map((e,s)=>s>=t?{...e,position:v(s)}:e))},k=e=>{p(t=>t.map(t=>t.conversation.id===e?{...t,isMinimized:!t.isMinimized,isMaximized:!1}:t))},A=e=>{p(t=>t.map(t=>t.conversation.id===e?{...t,isMaximized:!t.isMaximized,isMinimized:!1}:{...t,isMaximized:!1}))},S=(0,a.useCallback)(e=>{let t=e.toString();return h.some(e=>e.conversation.participants[0]?.id?.toString()===t)},[h]);return(0,r.jsxs)(d.Provider,{value:{openMessageList:j,closeMessageList:w,openChat:N,closeChat:C,minimizeChat:k,maximizeChat:A,isMessageListOpen:u,hasUnreadMessages:x,totalUnreadCount:g,refreshConversations:y,isChatOpen:S},children:[e,(0,r.jsx)(n.MessageList,{isOpen:u,onClose:w,onOpenChat:N}),h.map(e=>(0,r.jsx)(i.ChatWindow,{conversation:e.conversation,position:e.position,isMinimized:e.isMinimized,isMaximized:e.isMaximized,onClose:()=>C(e.conversation.id),onMinimize:()=>k(e.conversation.id),onMaximize:()=>A(e.conversation.id)},e.conversation.id))]})}},79630:(e,t,s)=>{"use strict";s.d(t,{CacheInvalidationProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call CacheInvalidationProvider() from the server but CacheInvalidationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/providers/CacheInvalidationProvider.tsx","CacheInvalidationProvider")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var r=s(60687);s(43210);var a=s(78148),n=s(4780);function i({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80875:(e,t,s)=>{"use strict";s.d(t,{IQScoreProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call IQScoreProvider() from the server but IQScoreProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/iq-score-context.tsx","IQScoreProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useIQScore() from the server but useIQScore is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/iq-score-context.tsx","useIQScore")},81326:(e,t,s)=>{"use strict";s.d(t,{NotificationProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/NotificationContext.tsx","NotificationProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/contexts/NotificationContext.tsx","useNotifications")},81410:(e,t,s)=>{"use strict";s.d(t,{WelcomeModal:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call WelcomeModal() from the server but WelcomeModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/ui/WelcomeModal.tsx","WelcomeModal")},81475:(e,t,s)=>{"use strict";s.d(t,{IQPointsToastContainer:()=>u});var r=s(60687),a=s(43210),n=s(64398),i=s(67268),o=s(45583),l=s(92363),c=s(46952);function d({notification:e,onDismiss:t}){let[s,c]=(0,a.useState)(!1),[d,u]=(0,a.useState)(!1),m=(0,a.useCallback)(()=>{u(!0),setTimeout(()=>{t(e.id)},300)},[t,e.id]);return(0,r.jsx)("div",{className:`
        fixed right-4 z-50 max-w-sm w-full
        transform transition-all duration-300 ease-in-out
        ${s&&!d?"translate-x-0 opacity-100 scale-100":"translate-x-full opacity-0 scale-95"}
      `,style:{top:"118px",zIndex:9999},children:(0,r.jsxs)("div",{className:`
          rounded-lg border-l-4 border-[#5cc8ff] p-4 cursor-pointer
          max-h-[80px] overflow-hidden
          ${e.rankedUp?"bg-gradient-to-r from-amber-50/95 to-yellow-50/95":""}
        `,style:{background:e.rankedUp?"linear-gradient(to right, rgba(251, 191, 36, 0.15), rgba(254, 240, 138, 0.15))":"rgba(255, 255, 255, 0.85)",backdropFilter:"blur(12px)",WebkitBackdropFilter:"blur(12px)",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(255, 255, 255, 0.1)",border:"none",borderLeft:"4px solid #5cc8ff"},onClick:m,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:`p-1.5 rounded-full flex-shrink-0 ${e.rankedUp?"bg-gradient-to-r from-amber-100 to-yellow-100":"bg-amber-100"}`,children:e.rankedUp?(0,r.jsx)(l.A,{className:"h-4 w-4 text-amber-500"}):(e=>{switch(e){case"create_account":case"profile_image":case"social_media_link":return(0,r.jsx)(n.A,{className:"h-4 w-4 text-amber-500"});case"post_publish":case"comment":return(0,r.jsx)(i.A,{className:"h-4 w-4 text-amber-500"});default:return(0,r.jsx)(o.A,{className:"h-4 w-4 text-amber-500"})}})(e.activityType)}),(0,r.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsxs)("span",{className:"font-bold text-amber-600 text-sm",children:["+",e.pointsEarned]}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"IQ Points"})]}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"•"}),(0,r.jsx)("span",{className:"text-xs text-gray-700 truncate",children:e.rankedUp?`Rank up! You're now ${e.newRank}`:e.content.includes("upvoting")?"Great participation!":e.content.includes("post")?"Post published!":e.content.includes("comment")?"Comment added!":e.content.includes("connection")?"New connection made!":"Well done!"})]})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 flex-shrink-0 ml-2",children:["Total: ",e.newTotal]})]}),e.rankedUp&&(0,r.jsxs)("div",{className:"absolute inset-0 pointer-events-none",children:[(0,r.jsx)("div",{className:"absolute top-1 right-1 animate-bounce text-xs",children:"✨"}),(0,r.jsx)("div",{className:"absolute bottom-1 left-8 animate-pulse text-xs",children:"\uD83C\uDF8A"})]})]})})}function u(){let{notifications:e}=(0,c.E)(),[t,s]=(0,a.useState)(null),[n,i]=(0,a.useState)(new Set);return t?(0,r.jsx)("div",{style:{top:"118px",zIndex:9999},className:"fixed right-4",children:(0,r.jsx)(d,{notification:t,onDismiss:()=>{s(null)}})}):null}},82949:(e,t,s)=>{"use strict";s.d(t,{MessageList:()=>y});var r=s(60687),a=s(43210),n=s(14952),i=s(96474),o=s(11860),l=s(99270),c=s(65822),d=s(30474),u=s(32584),m=s(29523),h=s(89667),p=s(67146),x=s(96834),f=s(85763),g=s(63503),b=s(71114),v=s(68457);function y({isOpen:e,onClose:t,onOpenChat:s}){let[y,j]=(0,a.useState)([]),[w,N]=(0,a.useState)(""),[C,k]=(0,a.useState)(!1),[A,S]=(0,a.useState)(null),[_,P]=(0,a.useState)(!1),{connections:E,loading:F}=(0,v.n5)(),q=e=>e.firstName&&e.lastName?`${e.firstName} ${e.lastName}`:e.username,$=e=>e.firstName&&e.lastName?`${e.firstName.charAt(0)}${e.lastName.charAt(0)}`:e.username.substring(0,2).toUpperCase(),I=e=>{let t=new Date(e),s=(new Date().getTime()-t.getTime())/36e5;if(s<1){let e=Math.floor(60*s);return e<=1?"Just now":`${e}m ago`}{if(s<24)return`${Math.floor(s)}h ago`;if(s<48)return"Yesterday";let e=Math.floor(s/24);return`${e}d ago`}},T=y.filter(e=>{let t=e.participants[0];return!!t&&q(t).toLowerCase().includes(w.toLowerCase())}),L=T.filter(e=>e.unreadCount>0),M=async e=>{let t=y.find(t=>t.id===e);if(t)try{let s=t.participants[0];if(!s)return;let r=parseInt(s.id);await b.J.markMessagesAsRead(r),j(t=>t.map(t=>t.id===e?{...t,unreadCount:0}:t))}catch(e){console.error("Failed to mark messages as read:",e)}},R=e=>{M(e.id),s(e)},z=e=>{let t=y.find(t=>t.participants.some(t=>t.id===e.user_id.toString()));t?R(t):s({id:e.user_id.toString(),participants:[{id:e.user_id.toString(),username:e.username||e.slug||`user-${e.user_id}`,firstName:e.name?.split(" ")[0]||null,lastName:e.name?.split(" ").slice(1).join(" ")||null,avatar:e.profile_picture?"string"==typeof e.profile_picture?{url:e.profile_picture}:e.profile_picture.url?{url:e.profile_picture.url}:null:e.avatar?{url:e.avatar}:null,isOnline:!1}],unreadCount:0}),P(!1)},D=e=>{let t=e.participants[0];if(!t)return null;let s=q(t),a=$(t),i=e.lastMessage;return(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0",onClick:()=>R(e),children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(u.eu,{className:"h-12 w-12",children:[t.avatar?.url&&(0,r.jsx)(u.BK,{src:t.avatar.url,alt:s}),(0,r.jsx)(u.q5,{className:"bg-blue-100 text-blue-600 font-medium",children:a})]}),t.isOnline&&(0,r.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 border-2 border-white rounded-full"})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("h3",{className:"font-medium text-dark-text truncate",children:s}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[i&&(0,r.jsx)("span",{className:"text-xs text-gray-500",children:I(i.timestamp)}),e.unreadCount>0&&(0,r.jsx)(x.E,{variant:"destructive",className:"h-5 min-w-[20px] text-xs",children:e.unreadCount>9?"9+":e.unreadCount})]})]}),i&&(0,r.jsx)("p",{className:"text-sm text-gray-600 truncate",children:i.content})]}),(0,r.jsx)(n.A,{className:"h-4 w-4 text-gray-400"})]},e.id)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.cj,{open:e,onOpenChange:t,children:(0,r.jsxs)(p.h,{side:"right",className:"w-full sm:w-96 p-0",hideOverlay:!0,children:[(0,r.jsx)(p.Fm,{className:"p-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(p.qp,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.default,{src:"/images/icons/icon-messages.svg",alt:"Messages",width:20,height:20,className:"h-5 w-5"}),"Messages"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>P(!0),title:"Start new conversation",children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}),(0,r.jsx)(p.kN,{asChild:!0,children:(0,r.jsx)(m.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})})]})]})}),(0,r.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(h.p,{placeholder:"Search conversations...",value:w,onChange:e=>N(e.target.value),className:"pl-10"})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:C?(0,r.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Loading conversations..."})}):A?(0,r.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,r.jsx)("div",{className:"text-sm text-red-500",children:A})}):0===T.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-32 text-center p-4",children:[(0,r.jsx)(d.default,{src:"/images/icons/icon-messages.svg",alt:"Messages",width:32,height:32,className:"h-8 w-8 text-gray-400 mb-2"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:w?"No conversations found":"No messages yet"}),!w&&(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Start a conversation with someone!"})]}):(0,r.jsxs)(f.tU,{defaultValue:"all",className:"h-full",children:[(0,r.jsxs)(f.j7,{className:"grid w-full grid-cols-2 mx-4 mt-2",children:[(0,r.jsxs)(f.Xi,{value:"all",className:"text-xs",children:["All (",T.length,")"]}),(0,r.jsxs)(f.Xi,{value:"unread",className:"text-xs",children:["Unread (",L.length,")"]})]}),(0,r.jsx)(f.av,{value:"all",className:"mt-2 h-full overflow-y-auto",children:(0,r.jsx)("div",{className:"space-y-0",children:T.map(D)})}),(0,r.jsx)(f.av,{value:"unread",className:"mt-2 h-full overflow-y-auto",children:0===L.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-32 text-center p-4",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"No unread messages"}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"You're all caught up!"})]}):(0,r.jsx)("div",{className:"space-y-0",children:L.map(D)})})]})})]})}),(0,r.jsx)(g.lG,{open:_,onOpenChange:P,children:(0,r.jsxs)(g.Cf,{className:"sm:max-w-md",children:[(0,r.jsx)(g.c7,{children:(0,r.jsx)(g.L3,{children:"Start a conversation"})}),(0,r.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:F?(0,r.jsx)("div",{className:"py-8 text-center text-sm text-gray-500",children:"Loading connections..."}):0===E.length?(0,r.jsx)("div",{className:"py-8 text-center text-sm text-gray-500",children:"No connections yet. Connect with other members to start messaging."}):E.map(e=>{let t=e.name||e.display_name||e.username||"Unknown",s=("string"==typeof e.profile_picture?e.profile_picture:e.profile_picture?.url)||e.avatar||"/images/avatar-placeholder.svg";return(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left",onClick:()=>z(e),children:[(0,r.jsxs)(u.eu,{className:"h-10 w-10",children:[(0,r.jsx)(u.BK,{src:s,alt:t}),(0,r.jsx)(u.q5,{children:t.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"font-medium text-sm truncate",children:t}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.job_title||"Member"})]}),(0,r.jsx)(n.A,{className:"h-4 w-4 text-gray-400"})]},e.user_id)})})]})})]})}},85380:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},85763:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>d,j7:()=>l,tU:()=>o});var r=s(60687),a=s(43210),n=s(55146),i=s(4780);let o=n.bL,l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.B8,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=n.B8.displayName;let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.l9,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));c.displayName=n.l9.displayName;let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.UC,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));d.displayName=n.UC.displayName},88095:(e,t,s)=>{"use strict";s.d(t,{RightSidebar:()=>q});var r=s(60687),a=s(85814),n=s.n(a),i=s(16189),o=s(29523),l=s(41862),c=s(13964),d=s(23026),u=s(19068),m=s(40228),h=s(97992),p=s(7112),x=s(43210),f=s(66816),g=s(68457),b=s(29103),v=s(8693),y=s(51423),j=s(30007),w=s(63503),N=s(3589),C=s(78272),k=s(44493),A=s(4780);function S({items:e,useAccordionMode:t,className:s}){let[a,n]=(0,x.useState)(new Set),i=(0,x.useRef)(null);(0,x.useCallback)(()=>{if(!t){let t=e.filter(e=>e.defaultOpen).map(e=>e.id);return 0===t.length?e.map(e=>e.id):t}if(!i.current)return e.length>0?[e[0].id]:[];let s=i.current.clientHeight,r=e=>"my-connections"===e.id||"suggested-connections"===e.id||"upcoming-events"===e.id?240:180,a=[...e].sort((t,s)=>(t.priority??e.indexOf(t))-(s.priority??e.indexOf(s))),n=0,o=[];for(let e of a){let t=60+50*!!e.cta+12+r(e);if(n+t<=s-100)o.push(e.id),n+=t;else{0===o.length&&o.push(e.id);break}}return 0===o.length&&a.length>0&&o.push(a[0].id),o},[t,e]);let o=e=>{if(t)n(a.has(e)?new Set:new Set([e]));else{let t=new Set(a);t.has(e)?t.delete(e):t.add(e),n(t)}};return(0,r.jsx)("div",{ref:i,className:(0,A.cn)("space-y-3 h-full overflow-hidden",s),children:e.map(e=>{let s=a.has(e.id);return(0,r.jsxs)(k.Zp,{className:"border-gray-200 rounded-[26px]",children:[(0,r.jsx)(k.aR,{className:(0,A.cn)("pb-2 transition-colors rounded-t-[26px]",t&&"cursor-pointer select-none hover:bg-gray-50",!s&&t&&e.cta&&"pb-4"),onClick:()=>t&&o(e.id),children:(0,r.jsxs)("div",{className:(0,A.cn)("flex items-center justify-between",s&&"pb-2.5"),children:[(0,r.jsx)("h3",{className:"text-lg font-extrabold text-brand-text",children:e.title}),t&&(0,r.jsx)("button",{onClick:t=>{t.stopPropagation(),o(e.id)},className:"p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":s?`Collapse ${e.title}`:`Expand ${e.title}`,children:s?(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}):(0,r.jsx)(C.A,{className:"h-4 w-4 text-gray-500"})})]})}),(0,r.jsx)("div",{className:(0,A.cn)("transition-all duration-300 ease-in-out overflow-hidden",s?"max-h-[800px] opacity-100":"max-h-0 opacity-0"),children:(0,r.jsx)(k.Wu,{className:"pt-2.5",children:e.content})}),e.cta&&(0,r.jsx)("div",{className:"px-6 pb-6 pt-2.5",children:e.cta})]},e.id)})})}var _=s(18636);function P({memberId:e,memberName:t}){let{status:s,refreshStatus:a,loading:n}=(0,g.qV)(e),{sendConnectionRequest:i,removeConnection:m}=(0,g.ZY)(),[h,p]=(0,x.useState)(!1),[f,b]=(0,x.useState)(!1),y=(0,v.jE)(),N=async(e,t)=>{p(!0);try{"request"===e&&(await i(t),y.setQueryData(j.lH.connections.status(t),{status:"pending",pending_type:"sent",can_request:!1,can_accept:!1,can_decline:!1,can_remove:!1}),await a())}catch(e){console.error("Connection action error:",e),await a()}finally{p(!1)}},C=async()=>{p(!0);try{await m(e),y.setQueryData(j.lH.connections.status(e),{status:"none",can_request:!0,can_accept:!1,can_decline:!1,can_remove:!1}),await a()}catch(e){console.error("Disconnect error:",e),await a()}finally{p(!1),b(!1)}},k=!!(h||n);if(n&&!s)return(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"h-8 text-xs",disabled:!0,children:(0,r.jsx)(l.A,{className:"h-3 w-3 animate-spin"})});if(!s||"none"===s.status)return(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"h-8 text-xs text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",onClick:()=>N("request",e),disabled:k,children:k?"...":"Connect"});if("pending"===s.status){if("sent"===s.pending_type)return(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"rounded-full w-8 h-8 p-0 text-[#5cc8ff] border-[#5cc8ff] bg-white hover:bg-[#5cc8ff]/10",disabled:!0,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})});else if("received"===s.pending_type)return(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"rounded-full w-8 h-8 p-0 text-orange-600 border-orange-200 bg-white",disabled:!0,children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}return"accepted"===s.status?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"rounded-full w-8 h-8 p-0 bg-[#5cc8ff]/10 text-[#5cc8ff] border-[#5cc8ff] hover:bg-[#5cc8ff]/20",onClick:()=>b(!0),disabled:k,children:k?(0,r.jsx)(u.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(c.A,{className:"h-4 w-4"})}),(0,r.jsx)(w.lG,{open:f,onOpenChange:b,children:(0,r.jsxs)(w.Cf,{children:[(0,r.jsxs)(w.c7,{children:[(0,r.jsxs)(w.L3,{children:["Disconnect from ",t,"?"]}),(0,r.jsxs)(w.rr,{children:["Are you sure you want to disconnect from ",t,"? This will remove your connection and you'll need to send a new connection request to reconnect."]})]}),(0,r.jsxs)(w.Es,{children:[(0,r.jsx)(o.$,{variant:"outline",onClick:()=>b(!1),disabled:h,children:"Cancel"}),(0,r.jsx)(o.$,{variant:"destructive",onClick:C,disabled:h,children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Disconnecting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Disconnect"]})})]})]})})]}):null}let E=e=>(0,y.I)({queryKey:["newest-members",e?.id],queryFn:async()=>{let t=await fetch("/api/wp-proxy/members/custom?per_page=20&page=1",{headers:{"Content-Type":"application/json"},credentials:"include"});if(!t.ok)throw Error(`Failed to fetch users: ${t.status}`);let s=await t.json();return[...s.founders||[],...s.members||[]].filter(t=>!(t.id===e.id||t.id===parseInt(e.id?.toString()||"0")||t.username===e.username||t.slug===e.username||t.name===e.name||t.username&&e.username&&t.username.toLowerCase()===e.username.toLowerCase()||t.slug&&e.username&&t.slug.toLowerCase()===e.username.toLowerCase())).slice(0,4).map(e=>({id:e.id.toString(),name:e.name,username:e.slug||e.username||e.id.toString(),role:e.role||"Member",job_title:e.job_title||e.jobTitle||void 0,image:e.avatar||"/images/avatar-placeholder.svg"}))},enabled:!!e?.id,staleTime:3e5,gcTime:18e5}),F=e=>(0,y.I)({queryKey:["upcoming-events"],queryFn:async()=>await (0,f.oj)(3)||[],enabled:!e,staleTime:6e5,gcTime:36e5});function q(){let{isAuthenticated:e,user:t}=(0,p.A)(),s=(0,i.useRouter)(),a=(0,i.usePathname)(),[c,d]=(0,x.useState)(!1),{data:u=[],isLoading:f}=E(t),{data:v=[],isLoading:y}=F(c),{connections:j,loading:w,error:N}=(0,g.n5)(),{shouldUseAccordion:C}=(0,_.l)(),k=[];return e&&k.push({id:"my-connections",title:"My Connections",defaultOpen:!0,priority:1,content:(0,r.jsx)("div",{className:"space-y-4",children:w?(0,r.jsxs)("div",{className:"py-4 flex flex-col items-center justify-center text-sm text-muted-foreground",children:[(0,r.jsx)(l.A,{className:"h-6 w-6 animate-spin mb-2"}),"Loading connections..."]}):N?(0,r.jsx)("div",{className:"py-2 text-center text-sm text-red-500",children:"Error loading connections"}):j.length>0?j.slice(0,3).map(e=>{let t=e.name||e.display_name||e.username||"Unknown",s=("string"==typeof e.profile_picture?e.profile_picture:e.profile_picture?.url)||e.avatar||"/images/avatar-placeholder.svg";return(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)(n(),{href:`/profile/${e.slug||e.username}`,className:"flex items-center space-x-3 hover:opacity-80 transition-opacity",children:[(0,r.jsx)(b.UserAvatarWithRank,{userId:"string"==typeof e.user_id?parseInt(e.user_id):e.user_id,avatarUrl:s,displayName:t,size:"h-10 w-10",containerSize:"w-12 h-12",userRoles:e.roles||[]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:t}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.job_title||"Member"})]})]})},e.user_id)}):(0,r.jsxs)("div",{className:"py-2 text-center text-sm text-muted-foreground",children:[(0,r.jsx)("p",{className:"mb-3",children:"No connections yet"}),(0,r.jsx)(n(),{href:"/member-directory",passHref:!0,children:(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",children:"Find Connections"})})]})}),cta:e&&!w&&!N&&(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",onClick:()=>{let e=`/profile/${t?.username||t?.id}`,r=`${e}?tab=connections`;a===e?(window.history.pushState({},"",r),window.dispatchEvent(new CustomEvent("tabChange",{detail:{tab:"connections"}}))):s.push(r)},children:"My Connections"})}),k.push({id:"suggested-connections",title:"Newest Users",defaultOpen:!0,priority:2,content:(0,r.jsx)("div",{className:"space-y-4",children:e?f||!t?(0,r.jsxs)("div",{className:"py-4 flex flex-col items-center justify-center text-sm text-muted-foreground",children:[(0,r.jsx)(l.A,{className:"h-6 w-6 animate-spin mb-2"}),"Loading members..."]}):u.length>0?u.slice(0,4).map(e=>(0,r.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,r.jsxs)(n(),{href:`/profile/${e.username||e.id}`,className:"flex items-start space-x-3 hover:opacity-80 transition-opacity flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(b.UserAvatarWithRank,{userId:parseInt(e.id),avatarUrl:e.image||"/images/avatar-placeholder.svg",displayName:e.name,size:"h-10 w-10",containerSize:"w-12 h-12",userRoles:e.role?[e.role.toLowerCase()]:[]})}),(0,r.jsxs)("div",{className:"min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium truncate",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-1",children:e.job_title||e.role||"Member"})]})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(P,{memberId:parseInt(e.id),memberName:e.name})})]},e.id)):(0,r.jsx)("div",{className:"py-2 text-center text-sm text-muted-foreground",children:"No members found"}):(0,r.jsxs)("div",{className:"py-4 flex flex-col items-center justify-center text-sm text-muted-foreground",children:[(0,r.jsx)("p",{className:"mb-3",children:"Log in to find your connections"}),(0,r.jsx)(n(),{href:"/login",passHref:!0,children:(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",children:"Log In"})})]})}),cta:(0,r.jsx)(n(),{href:"/member-directory",passHref:!0,children:(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",children:"View More"})})}),k.push({id:"upcoming-events",title:"Upcoming Events",defaultOpen:!0,priority:3,content:(0,r.jsx)("div",{className:"space-y-3",children:y?(0,r.jsxs)("div",{className:"py-4 flex flex-col items-center justify-center text-sm text-muted-foreground",children:[(0,r.jsx)(l.A,{className:"h-6 w-6 animate-spin mb-2"}),"Loading events..."]}):v.length>0?v.slice(0,3).map(e=>(0,r.jsxs)(n(),{href:`/posts/${e.slug}`,className:"block space-y-1 p-2 rounded-md transition-colors hover:bg-gray-50 cursor-pointer",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-brand-text",children:e.title.rendered}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,r.jsx)(m.A,{className:"h-3 w-3 mr-1"}),(0,r.jsx)("span",{children:e.acf?.event_start_date||e.formatted_date||new Date(e.date).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,r.jsx)(h.A,{className:"h-3 w-3 mr-1"}),(0,r.jsx)("span",{children:e.acf?.event_location||"Location not specified"})]})]},e.id.toString())):(0,r.jsx)("div",{className:"py-2 text-center text-sm text-muted-foreground",children:"No upcoming events"})}),cta:!y&&v.length>0&&(0,r.jsx)(n(),{href:"/?category=event",passHref:!0,children:(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",children:"View All Events"})})}),(0,r.jsx)("aside",{className:"flex w-full h-full","data-sidebar":"right",children:(0,r.jsx)("div",{className:(0,A.cn)("w-full p-4","overflow-hidden","[&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"),children:(0,r.jsx)(S,{items:k,useAccordionMode:C})})})}},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687);s(43210);var a=s(4780);function n({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},89969:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},92308:(e,t,s)=>{"use strict";s.d(t,{CacheInvalidationProvider:()=>a});var r=s(60687);function a({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}s(43210),s(93468)},92512:(e,t,s)=>{"use strict";s.d(t,{ZY:()=>r.ZY});var r=s(68457)},93468:(e,t,s)=>{"use strict";s.d(t,{e:()=>n});var r=s(30007);class a{static getInstance(){return a.instance||(a.instance=new a),a.instance}startPolling(e=5e3){this.pollInterval||(this.lastCheck=Math.floor(Date.now()/1e3),this.pollInterval=setInterval(()=>{this.checkInvalidations()},e),this.checkInvalidations())}stopPolling(){this.pollInterval&&(clearInterval(this.pollInterval),this.pollInterval=null)}async checkInvalidations(){if(!this.isPolling){this.isPolling=!0;try{let e=await fetch(`/api/wp-proxy/cache/invalidations?since=${this.lastCheck}`,{headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok)return void console.error("Failed to check cache invalidations:",e.status);let t=await e.json();t.invalidations&&t.invalidations.length>0&&(console.log("Processing cache invalidations:",t.invalidations),this.processInvalidations(t.invalidations)),this.lastCheck=t.timestamp}catch(e){console.error("Error checking cache invalidations:",e)}finally{this.isPolling=!1}}}processInvalidations(e){e.forEach(e=>{switch(e.type){case"post":this.invalidatePostCache(e.id);break;case"category":this.invalidateCategoryCache(e.id);break;case"all":this.invalidateAllCache()}})}async triggerServerRevalidation(e,t){try{await fetch("/api/revalidate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:e,id:t||0})}),console.log(`[Cache] Server-side revalidation triggered for ${e} ${t?`(ID: ${t})`:""}`)}catch(e){console.error("Failed to trigger server-side revalidation:",e)}}invalidatePostCache(e){r.qQ.invalidateQueries({queryKey:r.lH.posts.detail(e)}),r.qQ.invalidateQueries({queryKey:r.lH.posts.all}),r.qQ.invalidateQueries({predicate:e=>{let t=e.queryKey;return Array.isArray(t)&&"posts"===t[0]&&"user"===t[1]}})}invalidateCategoryCache(e){r.qQ.invalidateQueries({predicate:e=>{let t=e.queryKey;return Array.isArray(t)&&"posts"===t[0]&&("by-category"===t[1]||"list"===t[1]&&t[2]?.category)}}),r.qQ.invalidateQueries({queryKey:r.lH.categories.all})}invalidateAllCache(){r.qQ.invalidateQueries()}async invalidateCache(e,t){let s={type:e,id:t||0,timestamp:Math.floor(Date.now()/1e3)};this.processInvalidations([s]),await this.triggerServerRevalidation(e,t)}constructor(){this.pollInterval=null,this.lastCheck=0,this.isPolling=!1}}let n=a.getInstance()},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(24224),n=s(4780);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:t}),e),...s})}}};