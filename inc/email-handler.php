<?php
/**
 * Email Handler for TourismIQ
 * Handles all email notifications for the platform
 */

class TourismIQ_Email_Handler {
    
    private static $instance = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // User registration
        add_action( 'user_register', array( $this, 'send_welcome_email' ), 10, 1 );
        
        // Password reset with high priority to override other plugins
        add_filter( 'retrieve_password_message', array( $this, 'custom_password_reset_email' ), 999, 4 );
        add_filter( 'retrieve_password_title', array( $this, 'custom_password_reset_subject' ), 999, 3 );
        
        // Force HTML for password reset emails with high priority
        add_filter( 'wp_mail', array( $this, 'force_html_password_reset' ), 999, 1 );
        
        // Account deletion
        add_action( 'delete_user', array( $this, 'send_account_deleted_email' ), 10, 1 );
        
        // Vendor approval is now only handled via Stripe payment success
        // Removed automatic approval email on admin publish
        
        // Vendor upgrade to paid
        add_action( 'tourismiq_vendor_upgraded', array( $this, 'send_vendor_upgrade_email' ), 10, 2 );
        
        // Support request
        add_action( 'tourismiq_support_request', array( $this, 'send_support_email' ), 10, 1 );
        
        // Feedback submission
        add_action( 'tourismiq_feedback_submission', array( $this, 'send_feedback_email' ), 10, 1 );
        
        // Vendor suggestion
        add_action( 'tourismiq_vendor_suggestion', array( $this, 'send_vendor_suggestion_email' ), 10, 1 );
        
        // Vendor upgrade link request
        add_action( 'tourismiq_vendor_upgrade_link', array( $this, 'send_vendor_upgrade_link_email' ), 10, 1 );

        // Sponsorship request
        add_action( 'tourismiq_sponsorship_request', array( $this, 'send_sponsorship_email' ), 10, 1 );
        
        // Add custom email headers with higher priority to override SMTP plugins
        add_filter( 'wp_mail_content_type', array( $this, 'set_html_content_type' ), 999 );
        // Don't override from email/name since WP Mail SMTP handles this
        // add_filter( 'wp_mail_from', array( $this, 'custom_mail_from' ), 999 );
        // add_filter( 'wp_mail_from_name', array( $this, 'custom_mail_from_name' ), 999 );
    }
    
    /**
     * Load email template
     */
    private function load_template( $template_name ) {
        $template_path = get_template_directory() . '/emails/' . $template_name;
        
        if ( ! file_exists( $template_path ) ) {
            error_log( "Email template not found: " . $template_path );
            return false;
        }
        
        return file_get_contents( $template_path );
    }
    
    /**
     * Replace placeholders in template
     */
    private function replace_placeholders( $template, $data ) {
        foreach ( $data as $key => $value ) {
            $template = str_replace( '{' . $key . '}', $value, $template );
        }
        return $template;
    }
    
    /**
     * Get common email data
     */
    private function get_common_email_data() {
        // Use the explicit frontend URL for headless setup
        $frontend_url = 'https://mytourismiq.com';
        
        return array(
            'site_name' => get_bloginfo( 'name' ),
            'site_url' => $frontend_url,
            'admin_email' => get_option( 'admin_email' ),
            'year' => date( 'Y' ),
        );
    }
    
    /**
     * Send welcome email
     */
    public function send_welcome_email( $user_id ) {
        $user = get_user_by( 'id', $user_id );
        if ( ! $user ) return;
        
        $template = $this->load_template( 'welcome.html' );
        if ( ! $template ) return;
        
        // Use explicit frontend URL for headless setup
        $frontend_url = 'https://mytourismiq.com';
        
        $data = array_merge(
            $this->get_common_email_data(),
            array(
                'username' => $user->user_login,
                'email' => $user->user_email,
                'action_url' => $frontend_url,
                'action_title' => 'Visit Site',
            )
        );
        
        // Debug logging for welcome email
        error_log('TourismIQ Welcome Email: action_url = ' . $data['action_url']);
        error_log('TourismIQ Welcome Email: site_url = ' . $data['site_url']);
        
        $message = $this->replace_placeholders( $template, $data );
        
        wp_mail(
            $user->user_email,
            'Welcome to ' . get_bloginfo( 'name' ),
            $message
        );
    }
    
    /**
     * Custom password reset email
     */
    public function custom_password_reset_email( $message, $key, $user_login, $user_data ) {
        $template = $this->load_template( 'password-reset.html' );
        if ( ! $template ) {
            return $message; // Fall back to default
        }
        
        // Build the reset URL - ensuring it uses the WordPress backend URL
        $site_url = defined('WP_SITEURL') ? WP_SITEURL : get_option('siteurl');
        $reset_url = $site_url . "/wp-login.php?action=rp&key=$key&login=" . rawurlencode( $user_login );
        
        $data = array_merge(
            $this->get_common_email_data(),
            array(
                'username' => $user_login,
                'email' => $user_data->user_email,
                'password_reset_link' => $reset_url,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                'browser' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'date_time' => current_time( 'F j, Y \a\t g:i a' ),
            )
        );
        
        return $this->replace_placeholders( $template, $data );
    }
    
    /**
     * Custom password reset email subject
     */
    public function custom_password_reset_subject( $title, $user_login, $user_data ) {
        return sprintf( '[%s] Password Reset Request', get_bloginfo( 'name' ) );
    }
    
    /**
     * Send account deleted email
     */
    public function send_account_deleted_email( $user_id ) {
        $user = get_user_by( 'id', $user_id );
        if ( ! $user ) return;
        
        $template = $this->load_template( 'account-deleted.html' );
        if ( ! $template ) return;
        
        $data = array_merge(
            $this->get_common_email_data(),
            array(
                'username' => $user->user_login,
                'email' => $user->user_email,
                'deletion_date' => current_time( 'F j, Y' ),
            )
        );
        
        $message = $this->replace_placeholders( $template, $data );
        
        wp_mail(
            $user->user_email,
            'Your account has been deleted',
            $message
        );
    }
    
    // check_vendor_approval method removed - approval emails now only sent via Stripe payment
    
    /**
     * Send vendor approval email
     */
    public function send_vendor_approval_email( $vendor_id, $is_upgrade = false ) {
        $vendor = get_post( $vendor_id );
        if ( ! $vendor ) return;
        
        $author_id = $vendor->post_author;
        $user = get_user_by( 'id', $author_id );
        if ( ! $user ) return;
        
        $template = $this->load_template( 'vendor-approval.html' );
        if ( ! $template ) return;
        
        $vendor_status = get_post_meta( $vendor_id, 'vendor_is_paid', true ) === '1' ? 'Premium Vendor' : 'Standard Vendor';
        
        // Use explicit frontend URL for headless setup
        $frontend_url = 'https://mytourismiq.com';
        
        $data = array_merge(
            $this->get_common_email_data(),
            array(
                'username' => $user->user_login,
                'email' => $user->user_email,
                'vendor_name' => get_the_title( $vendor_id ),
                'vendor_url' => $frontend_url . '/vendors/' . $vendor->post_name,
                'vendor_edit_url' => $frontend_url . '/vendors/' . $vendor->post_name,
                'vendor_status' => $vendor_status,
            )
        );
        
        $message = $this->replace_placeholders( $template, $data );
        
        // Use different subject based on whether it's an upgrade
        $subject = $is_upgrade ? 
            'Your vendor account has been upgraded!' : 
            'Your vendor profile has been approved!';
        
        wp_mail(
            $user->user_email,
            $subject,
            $message
        );
        
        // Also notify assigned team members
        $assigned_members = get_post_meta( $vendor_id, 'vendor_assigned_members', true );
        if ( is_array( $assigned_members ) ) {
            foreach ( $assigned_members as $member_id ) {
                if ( $member_id != $author_id ) { // Don't send duplicate to author
                    $member = get_user_by( 'id', $member_id );
                    if ( $member ) {
                        wp_mail(
                            $member->user_email,
                            'You\'ve been added to vendor: ' . get_the_title( $vendor_id ),
                            $message
                        );
                    }
                }
            }
        }
    }
    
    /**
     * Send vendor upgrade email (uses vendor-approval template)
     */
    public function send_vendor_upgrade_email( $vendor_id, $user_id ) {
        // Use the vendor approval email for upgrades with upgrade flag
        $this->send_vendor_approval_email( $vendor_id, true );
    }
    
    /**
     * Send support request email to admins
     */
    public function send_support_email( $data ) {
        $template = $this->load_template( 'support-request.html' );
        if ( ! $template ) return;
        
        // Get priority styling
        $priority_style = '';
        switch ( $data['priority'] ) {
            case 'urgent':
                $priority_style = 'background-color: #dc2626; color: white;';
                break;
            case 'high':
                $priority_style = 'background-color: #f59e0b; color: white;';
                break;
            case 'medium':
                $priority_style = 'background-color: #3b82f6; color: white;';
                break;
            case 'low':
                $priority_style = 'background-color: #10b981; color: white;';
                break;
        }
        
        // Handle attachments section
        $attachments_section = '';
        if ( ! empty( $data['attachments'] ) ) {
            $attachments_section = '<div style="padding: 20px; background: #f8f9fa; border-radius: 10px; text-align: left; font-size: 14px; margin-bottom: 20px;">';
            $attachments_section .= '<div style="font-weight: bold; margin-bottom: 10px;">Attachments (' . count( $data['attachments'] ) . '):</div>';
            $attachments_section .= '<ul style="margin: 0; padding-left: 20px;">';
            foreach ( $data['attachments'] as $attachment ) {
                $attachments_section .= '<li style="margin-bottom: 5px;">' . esc_html( $attachment ) . '</li>';
            }
            $attachments_section .= '</ul></div>';
        }
        
        $email_data = array_merge(
            $this->get_common_email_data(),
            array(
                'name' => sanitize_text_field( $data['name'] ),
                'email' => sanitize_email( $data['email'] ),
                'subject' => sanitize_text_field( $data['subject'] ),
                'priority' => strtoupper( sanitize_text_field( $data['priority'] ) ),
                'priority_style' => $priority_style,
                'description' => wp_kses_post( $data['description'] ),
                'attachments_section' => $attachments_section,
                'date_time' => current_time( 'F j, Y \a\t g:i a' ),
                'admin_url' => admin_url( 'admin.php?page=tourismiq-support' ),
            )
        );
        
        $message = $this->replace_placeholders( $template, $email_data );
        
        // Default admin recipients
        $admin_emails = array(
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        );
        
        // Send to each admin
        foreach ( $admin_emails as $admin_email ) {
            $sent = wp_mail(
                $admin_email,
                '[Support] ' . $email_data['subject'],
                $message
            );
            error_log( "TourismIQ: Sending support email to $admin_email - Result: " . ($sent ? 'SUCCESS' : 'FAILED') );
        }
        
        // Also send confirmation to user
        $this->send_support_confirmation_email( $data );
    }
    
    /**
     * Send support confirmation email to user
     */
    private function send_support_confirmation_email( $data ) {
        // Simple confirmation using welcome template structure
        $template = $this->load_template( 'welcome.html' );
        if ( ! $template ) return;
        
        // Use explicit frontend URL for headless setup
        $frontend_url = 'https://mytourismiq.com';
        
        $email_data = array_merge(
            $this->get_common_email_data(),
            array(
                'username' => sanitize_text_field( $data['name'] ),
                'email' => sanitize_email( $data['email'] ),
                'action_url' => $frontend_url,
                'action_title' => 'Return to Site',
            )
        );
        
        // Replace the welcome message
        $message = $this->replace_placeholders( $template, $email_data );
        $message = str_replace(
            'Thank you for signing up!',
            'Support Request Received',
            $message
        );
        $message = str_replace(
            'Your account is now active.',
            'We\'ve received your support request and will respond shortly.',
            $message
        );
        
        wp_mail(
            $data['email'],
            'Support Request Received - ' . get_bloginfo( 'name' ),
            $message
        );
    }
    
    /**
     * Send feedback email to admins
     */
    public function send_feedback_email( $data ) {
        $template = $this->load_template( 'feedback-submission.html' );
        if ( ! $template ) return;
        
        // Convert rating to stars
        $rating = intval( $data['rating'] );
        $rating_stars = str_repeat( '⭐', $rating );
        
        // Format category
        $category_display = ucwords( str_replace( '_', ' ', $data['category'] ) );
        
        $email_data = array_merge(
            $this->get_common_email_data(),
            array(
                'name' => sanitize_text_field( $data['name'] ),
                'email' => sanitize_email( $data['email'] ),
                'category' => $category_display,
                'rating' => $rating,
                'rating_stars' => $rating_stars,
                'feedback' => wp_kses_post( $data['feedback'] ),
                'date_time' => current_time( 'F j, Y \a\t g:i a' ),
                'admin_url' => admin_url( 'admin.php?page=tourismiq-feedback' ),
            )
        );
        
        $message = $this->replace_placeholders( $template, $email_data );
        
        // Default admin recipients
        $admin_emails = array(
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        );
        
        // Send to each admin
        foreach ( $admin_emails as $admin_email ) {
            wp_mail(
                $admin_email,
                '[Feedback] ' . $category_display . ' - Rating: ' . $rating . '/5',
                $message
            );
        }
        
        // Also send confirmation to user
        $this->send_feedback_confirmation_email( $data );
    }
    
    /**
     * Send feedback confirmation email to user
     */
    private function send_feedback_confirmation_email( $data ) {
        // Simple confirmation using welcome template structure
        $template = $this->load_template( 'welcome.html' );
        if ( ! $template ) return;
        
        // Use explicit frontend URL for headless setup
        $frontend_url = 'https://mytourismiq.com';
        
        $email_data = array_merge(
            $this->get_common_email_data(),
            array(
                'username' => sanitize_text_field( $data['name'] ),
                'email' => sanitize_email( $data['email'] ),
                'action_url' => $frontend_url,
                'action_title' => 'Return to Site',
            )
        );
        
        // Replace the welcome message
        $message = $this->replace_placeholders( $template, $email_data );
        $message = str_replace(
            'Thank you for signing up!',
            'Thank You for Your Feedback',
            $message
        );
        $message = str_replace(
            'Your account is now active.',
            'We appreciate your feedback and will use it to improve our platform.',
            $message
        );
        
        wp_mail(
            $data['email'],
            'Thank you for your feedback - ' . get_bloginfo( 'name' ),
            $message
        );
    }
    
    /**
     * Send vendor upgrade link email
     */
    public function send_vendor_upgrade_link_email( $data ) {
        error_log('TourismIQ Email Handler: send_vendor_upgrade_link_email() called with data: ' . print_r($data, true));
        
        $template = $this->load_template( 'vendor-upgrade-link.html' );
        if ( ! $template ) {
            error_log('TourismIQ Email Handler: vendor-upgrade-link.html template not found!');
            return false;
        }
        
        // Use explicit frontend URL for headless setup
        $frontend_url = 'https://mytourismiq.com';
        
        $email_data = array_merge(
            $this->get_common_email_data(),
            array(
                'vendor_id' => $data['vendor_id'],
                'vendor_name' => $data['vendor_name'],
                'email' => $data['email'],
                'upgrade_link' => $data['upgrade_link'],
            )
        );
        
        $message = $this->replace_placeholders( $template, $email_data );
        
        $sent = wp_mail(
            $data['email'],
            'Upgrade Your Vendor Profile - ' . get_bloginfo( 'name' ),
            $message
        );
        
        error_log( "TourismIQ: Sending vendor upgrade link to {$data['email']} - Result: " . ($sent ? 'SUCCESS' : 'FAILED') );
        
        return $sent;
    }
    
    /**
     * Send sponsorship request email to admins
     */
    public function send_sponsorship_email( $data ) {
        error_log('TourismIQ Email Handler: send_sponsorship_email() called with data: ' . print_r($data, true));

        $template = $this->load_template( 'sponsorship-request.html' );
        if ( ! $template ) {
            error_log('TourismIQ Email Handler: sponsorship-request.html template not found!');
            return;
        }

        error_log('TourismIQ Email Handler: Sponsorship template loaded successfully');

        $email_data = array_merge(
            $this->get_common_email_data(),
            array(
                'company_name' => sanitize_text_field( $data['companyName'] ),
                'contact_name' => sanitize_text_field( $data['contactName'] ),
                'email' => sanitize_email( $data['email'] ),
                'phone' => ! empty( $data['phone'] ) ? sanitize_text_field( $data['phone'] ) : 'Not provided',
                'budget' => sanitize_text_field( $data['budget'] ),
                'message' => wp_kses_post( $data['message'] ),
                'date_time' => current_time( 'F j, Y \\a\\t g:i a' ),
                'admin_url' => admin_url( 'admin.php' ),
            )
        );

        $message = $this->replace_placeholders( $template, $email_data );

        // Send to sponsors email
        $sponsors_email = '<EMAIL>';

        $sent = wp_mail(
            $sponsors_email,
            '[Sponsorship Inquiry] ' . $email_data['company_name'] . ' - ' . $email_data['budget'],
            $message
        );
        error_log( "TourismIQ: Sending sponsorship email to $sponsors_email - Result: " . ($sent ? 'SUCCESS' : 'FAILED') );

        // Also send confirmation to sponsor
        $this->send_sponsorship_confirmation_email( $data );
    }

    /**
     * Send sponsorship confirmation email to sponsor
     */
    private function send_sponsorship_confirmation_email( $data ) {
        // Simple confirmation using welcome template structure
        $template = $this->load_template( 'welcome.html' );
        if ( ! $template ) return;

        // Use explicit frontend URL for headless setup
        $frontend_url = 'https://mytourismiq.com';

        $email_data = array_merge(
            $this->get_common_email_data(),
            array(
                'username' => sanitize_text_field( $data['contactName'] ),
                'email' => sanitize_email( $data['email'] ),
                'action_url' => $frontend_url,
                'action_title' => 'Return to Site',
            )
        );

        // Replace the welcome message
        $message = $this->replace_placeholders( $template, $email_data );
        $message = str_replace(
            'Thank you for signing up!',
            'Sponsorship Inquiry Received',
            $message
        );
        $message = str_replace(
            'Your account is now active.',
            'Thank you for your interest in sponsoring TourismIQ. We\'ve received your inquiry and will respond within 24 hours.',
            $message
        );

        wp_mail(
            $data['email'],
            'Sponsorship Inquiry Received - ' . get_bloginfo( 'name' ),
            $message
        );
    }

    /**
     * Send vendor suggestion email to support
     */
    public function send_vendor_suggestion_email( $data ) {
        error_log('TourismIQ Email Handler: send_vendor_suggestion_email() called with data: ' . print_r($data, true));
        
        $template = $this->load_template( 'vendor-suggestion.html' );
        if ( ! $template ) {
            error_log('TourismIQ Email Handler: vendor-suggestion.html template not found!');
            return;
        }
        
        error_log('TourismIQ Email Handler: Template loaded successfully');
        
        $email_data = array_merge(
            $this->get_common_email_data(),
            array(
                'first_name' => sanitize_text_field( $data['firstName'] ),
                'last_name' => sanitize_text_field( $data['lastName'] ),
                'email' => sanitize_email( $data['email'] ),
                'organization' => sanitize_text_field( $data['organization'] ),
                'website' => esc_url( $data['website'] ),
                'message' => wp_kses_post( $data['message'] ),
                'date_time' => current_time( 'F j, Y \\a\\t g:i a' ),
                'admin_url' => admin_url( 'edit.php?post_type=vendor' ),
            )
        );
        
        $message = $this->replace_placeholders( $template, $email_data );
        
        // Default admin recipients
        $admin_emails = array(
            '<EMAIL>'
        );
        
        // Send to each admin
        foreach ( $admin_emails as $admin_email ) {
            $sent = wp_mail(
                $admin_email,
                '[Vendor Suggestion] ' . $email_data['organization'],
                $message
            );
            error_log( "TourismIQ: Sending vendor suggestion email to $admin_email - Result: " . ($sent ? 'SUCCESS' : 'FAILED') );
        }
    }
    
    /**
     * Set email content type to HTML
     */
    public function set_html_content_type() {
        return 'text/html';
    }
    
    /**
     * Custom mail from address
     */
    public function custom_mail_from( $email ) {
        // <NAME_EMAIL> for consistency
        return '<EMAIL>';
    }
    
    /**
     * Custom mail from name
     */
    public function custom_mail_from_name( $name ) {
        // Use "MyTourismIQ" or the site name without lowercase prefix
        $site_name = get_bloginfo( 'name' );
        
        // Remove lowercase "tourismiq" prefix if it exists
        if ( strtolower( substr( $site_name, 0, 9 ) ) === 'tourismiq' ) {
            return 'MyTourismIQ';
        }
        
        return $site_name;
    }
    
    /**
     * Force HTML content type for password reset emails
     */
    public function force_html_password_reset( $args ) {
        // Check if this is a password reset email
        if ( isset( $args['subject'] ) && strpos( $args['subject'], 'Password Reset' ) !== false ) {
            // Ensure headers is an array
            if ( ! is_array( $args['headers'] ) ) {
                $args['headers'] = array();
            }
            
            // Add HTML content type
            $has_content_type = false;
            foreach ( $args['headers'] as $header ) {
                if ( strpos( strtolower( $header ), 'content-type' ) !== false ) {
                    $has_content_type = true;
                    break;
                }
            }
            
            if ( ! $has_content_type ) {
                $args['headers'][] = 'Content-Type: text/html; charset=UTF-8';
            }
        }
        
        return $args;
    }
}

// Initialize the email handler early to catch password reset emails
add_action( 'after_setup_theme', function() {
    TourismIQ_Email_Handler::get_instance();
}, 5 );

// Helper function to trigger vendor upgrade email
function tourismiq_send_vendor_upgrade_email( $vendor_id, $user_id ) {
    do_action( 'tourismiq_vendor_upgraded', $vendor_id, $user_id );
}

// Helper function to trigger support request email
function tourismiq_send_support_email( $data ) {
    do_action( 'tourismiq_support_request', $data );
}

// Helper function to trigger feedback submission email
function tourismiq_send_feedback_email( $data ) {
    do_action( 'tourismiq_feedback_submission', $data );
}

// Helper function to trigger vendor suggestion email
function tourismiq_send_vendor_suggestion_email( $data ) {
    error_log('TourismIQ Helper: tourismiq_send_vendor_suggestion_email() called, about to fire action');
    do_action( 'tourismiq_vendor_suggestion', $data );
    error_log('TourismIQ Helper: tourismiq_vendor_suggestion action fired');
}

// Helper function to trigger vendor upgrade link email
function tourismiq_send_vendor_upgrade_link_email( $data ) {
    error_log('TourismIQ Helper: tourismiq_send_vendor_upgrade_link_email() called');
    do_action( 'tourismiq_vendor_upgrade_link', $data );
    return apply_filters( 'tourismiq_vendor_upgrade_link_sent', true, $data );
}

// Helper function to trigger sponsorship request email
function tourismiq_send_sponsorship_email( $data ) {
    error_log('TourismIQ Helper: tourismiq_send_sponsorship_email() called, about to fire action');
    do_action( 'tourismiq_sponsorship_request', $data );
    error_log('TourismIQ Helper: tourismiq_sponsorship_request action fired');
}