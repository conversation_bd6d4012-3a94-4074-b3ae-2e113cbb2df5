(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5300],{26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(95155);t(12115);var n=t(74466),a=t(59434);let c=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...n}=e;return(0,r.jsx)("div",{className:(0,a.cn)(c({variant:t}),s),...n})}},37818:(e,s,t)=>{Promise.resolve().then(t.bind(t,70502))},70502:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(95155),n=t(12115),a=t(35695),c=t(71644),l=t(6874),i=t.n(l),o=t(30285),d=t(66695),u=t(26126),x=t(71366),f=t(6509);function m(e){let{initialQuestions:s=[],totalPages:t=1,currentPage:a=1,currentSort:c="new"}=e,[l,d]=(0,n.useState)(!1),[u,m]=(0,n.useState)(s),[g,j]=(0,n.useState)(!1),[p,y]=(0,n.useState)(!1),[v,b]=(0,n.useState)(a),[N,w]=(0,n.useState)(c),[S,A]=(0,n.useState)(v<t),[q,E]=(0,n.useState)(""),k=(0,n.useRef)(null),L=(0,n.useRef)(null);(0,n.useEffect)(()=>{d(!0)},[]),(0,n.useEffect)(()=>{s.length>0&&(m(s),A(a<t))},[s,a,t]);let P=(0,n.useCallback)(async()=>{if(!p&&S){y(!0);try{let e=await (0,f.yS)({page:v+1,sort:N,search:q}),s=e.questions.map(f.Hs);m(e=>[...e,...s]),b(e=>e+1),A(v+1<e.totalPages)}catch(e){console.error("Error loading more questions:",e)}finally{y(!1)}}},[p,S,v,N,q]);(0,n.useEffect)(()=>p?()=>{}:(k.current&&k.current.disconnect(),k.current=new IntersectionObserver(e=>{var s;(null==(s=e[0])?void 0:s.isIntersecting)&&S&&P()},{threshold:.5}),L.current&&k.current.observe(L.current),()=>{k.current&&k.current.disconnect()}),[P,p,S]);let C=async e=>{if(e!==N){j(!0),w(e),b(1);try{let s=await (0,f.yS)({page:1,sort:e,search:q}),t=s.questions.map(f.Hs);m(t),A(1<s.totalPages)}catch(e){console.error("Error changing sort:",e)}finally{j(!1)}}},R=async e=>{E(e),j(!0),b(1);try{let s=await (0,f.yS)({page:1,sort:N,search:e}),t=s.questions.map(f.Hs);m(t),A(1<s.totalPages)}catch(e){console.error("Error searching questions:",e)}finally{j(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("input",{type:"text",placeholder:"Search questions...",value:q,onChange:e=>R(e.target.value),className:"w-full px-6 py-3 border border-gray-200 rounded-full h-12 bg-white focus:ring-2 focus:ring-[#5cc8ff] focus:border-[#5cc8ff] focus:outline-none text-base"})}),(0,r.jsxs)("div",{className:"flex bg-white border border-gray-200 rounded-full p-1 h-12",children:[(0,r.jsx)("button",{onClick:()=>C("new"),className:"flex-1 px-6 rounded-full text-sm transition-all duration-200 flex items-center justify-center ".concat("new"===N?"bg-[#5cc8ff] text-[#3d405b] !font-extrabold":"text-gray-600 hover:bg-[#5cc8ff]/10 font-medium"),children:"Recent"}),(0,r.jsx)("button",{onClick:()=>C("trending"),className:"flex-1 px-6 rounded-full text-sm transition-all duration-200 flex items-center justify-center ".concat("trending"===N?"bg-[#5cc8ff] text-[#3d405b] !font-extrabold":"text-gray-600 hover:bg-[#5cc8ff]/10 font-medium"),children:"Trending"})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:l?0!==u.length||g?u.map(e=>(0,r.jsx)(h,{question:e,mounted:l},e.id)):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-dark-text mb-2",children:q?"No results found":"No questions yet"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:q?"Try adjusting your search terms":"Be the first to ask a question!"}),!q&&(0,r.jsx)(i(),{href:"/forum/new",children:(0,r.jsx)(o.$,{className:"bg-[#5cc8ff] hover:bg-[#5cc8ff]/80 text-white font-bold",children:"Ask Question"})})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading questions..."})]})}),p&&(0,r.jsx)("div",{ref:L,className:"flex justify-center mt-8 py-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"}),(0,r.jsx)("span",{className:"text-[#3d405b] font-medium",children:"Loading more questions..."})]})}),!S&&u.length>0&&(0,r.jsx)("div",{className:"text-center mt-8 py-4",children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:q?"End of search results":"You've reached the end of the questions"})})]})}function h(e){let{question:s,mounted:t}=e;return(0,r.jsx)(i(),{href:"/forum/".concat(s.id),className:"block",children:(0,r.jsx)(d.Zp,{className:"hover:shadow-md hover:border-[#5cc8ff] transition-all duration-200 group cursor-pointer",children:(0,r.jsxs)(d.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[s.isResolved&&(0,r.jsx)(u.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"Resolved"}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:t&&new Date(s.createdAt).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),s.commentCount]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 group-hover:text-[#5cc8ff] transition-colors mb-2",children:s.title}),(0,r.jsx)("div",{className:"text-gray-600 line-clamp-3",dangerouslySetInnerHTML:{__html:s.content}})]})]})})})}var g=t(27600),j=t(51154),p=t(32919);function y(){let{isAuthenticated:e,isLoading:s}=(0,g.A)(),t=(0,a.useRouter)(),l=(0,a.useSearchParams)(),[u,x]=(0,n.useState)([]),[h,y]=(0,n.useState)(1),v=l.get("sort")||"new";return((0,n.useEffect)(()=>{s||e||t.push("/login?redirect=/forum")},[e,s,t]),(0,n.useEffect)(()=>{e&&(async()=>{try{let{questions:e,totalPages:s}=await (0,f.yS)({page:1,sort:v}),t=e.map(f.Hs);x(t),y(s)}catch(e){console.error("Error loading forum questions:",e),x([]),y(1)}finally{}})()},[e,1,v]),s)?(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-6 w-6 animate-spin"}),(0,r.jsx)("p",{children:"Loading..."})]})})}):e?(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(c.A,{children:(0,r.jsx)(m,{initialQuestions:u,totalPages:h,currentPage:1,currentSort:v})})}):(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)(d.Zp,{className:"w-full max-w-md",children:[(0,r.jsxs)(d.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100",children:(0,r.jsx)(p.A,{className:"h-6 w-6 text-gray-600"})}),(0,r.jsx)(d.ZB,{children:"Authentication Required"})]}),(0,r.jsxs)(d.Wu,{className:"text-center space-y-4",children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Please log in to access the community forum and share knowledge with tourism professionals."}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i(),{href:"/login?redirect=/forum",passHref:!0,children:(0,r.jsx)(o.$,{className:"w-full",children:"Log In"})}),(0,r.jsx)(i(),{href:"/register?redirect=/forum",passHref:!0,children:(0,r.jsx)(o.$,{variant:"outline",className:"w-full",children:"Create Account"})})]})]})]})})})}function v(){return(0,r.jsx)(n.Suspense,{fallback:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:"Loading..."}),children:(0,r.jsx)(y,{})})}},71366:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6874,2757,7600,2705,8441,1684,7358],()=>s(37818)),_N_E=e.O()}]);