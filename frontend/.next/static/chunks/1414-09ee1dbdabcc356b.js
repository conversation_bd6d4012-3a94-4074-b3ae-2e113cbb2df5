"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1414],{1243:(e,n,t)=>{t.d(n,{A:()=>r});let r=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},15452:(e,n,t)=>{t.d(n,{G$:()=>J,Hs:()=>O,UC:()=>et,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>er,hJ:()=>en,l9:()=>X});var r=t(12115),o=t(85185),a=t(6101),l=t(46081),i=t(61285),s=t(5845),u=t(19178),d=t(25519),c=t(34378),p=t(28905),f=t(63655),m=t(92293),g=t(93795),v=t(38168),N=t(99708),y=t(95155),h="Dialog",[D,O]=(0,l.A)(h),[R,w]=D(h),C=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:h});return(0,y.jsx)(R,{scope:n,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:t})};C.displayName=h;var I="DialogTrigger",b=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,l=w(I,t),i=(0,a.s)(n,l.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":H(l.open),...r,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});b.displayName=I;var j="DialogPortal",[E,x]=D(j,{forceMount:void 0}),A=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:a}=e,l=w(j,n);return(0,y.jsx)(E,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,y.jsx)(p.C,{present:t||l.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};A.displayName=j;var M="DialogOverlay",T=r.forwardRef((e,n)=>{let t=x(M,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,a=w(M,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:r||a.open,children:(0,y.jsx)(F,{...o,ref:n})}):null});T.displayName=M;var _=(0,N.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=w(M,t);return(0,y.jsx)(g.A,{as:_,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":H(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",k=r.forwardRef((e,n)=>{let t=x(P,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,a=w(P,e.__scopeDialog);return(0,y.jsx)(p.C,{present:r||a.open,children:a.modal?(0,y.jsx)(U,{...o,ref:n}):(0,y.jsx)(L,{...o,ref:n})})});k.displayName=P;var U=r.forwardRef((e,n)=>{let t=w(P,e.__scopeDialog),l=r.useRef(null),i=(0,a.s)(n,t.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(S,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null==(n=t.triggerRef.current)||n.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,n)=>{let t=w(P,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,y.jsx)(S,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,n),n.defaultPrevented||(o.current||null==(l=t.triggerRef.current)||l.focus(),n.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:n=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"===n.detail.originalEvent.type&&(a.current=!0));let i=n.target;(null==(l=t.triggerRef.current)?void 0:l.contains(i))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&a.current&&n.preventDefault()}})}),S=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=w(P,t),p=r.useRef(null),f=(0,a.s)(n,p);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:c.titleId}),(0,y.jsx)($,{contentRef:p,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",G=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=w(W,t);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...r,ref:n})});G.displayName=W;var q="DialogDescription",B=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=w(q,t);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:n})});B.displayName=q;var V="DialogClose",Z=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,a=w(V,t);return(0,y.jsx)(f.sG.button,{type:"button",...r,ref:n,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function H(e){return e?"open":"closed"}Z.displayName=V;var z="DialogTitleWarning",[J,K]=(0,l.q)(z,{contentName:P,titleName:W,docsSlug:"dialog"}),Y=e=>{let{titleId:n}=e,t=K(z),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&(document.getElementById(n)||console.error(o))},[o,n]),null},$=e=>{let{contentRef:n,descriptionId:t}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=n.current)?void 0:e.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(a))},[a,n,t]),null},Q=C,X=b,ee=A,en=T,et=k,er=G,eo=B,ea=Z},28905:(e,n,t)=>{t.d(n,{C:()=>l});var r=t(12115),o=t(6101),a=t(52712),l=e=>{let{present:n,children:t}=e,l=function(e){var n,t;let[o,l]=r.useState(),s=r.useRef(null),u=r.useRef(e),d=r.useRef("none"),[c,p]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=i(s.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let n=s.current,t=u.current;if(t!==e){let r=d.current,o=i(n);e?p("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?p("UNMOUNT"):t&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let n,t=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(s.current).includes(e.animationName);if(e.target===o&&r&&(p("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=i(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,l(e)},[])}}(n),s="function"==typeof t?t({present:l.isPresent}):r.Children.only(t),u=(0,o.s)(l.ref,function(e){var n,t;let r=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(t=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof t||l.isPresent?r.cloneElement(s,{ref:u}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"}}]);