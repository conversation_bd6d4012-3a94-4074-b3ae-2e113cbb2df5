/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wp-proxy/auth/status/route";
exports.ids = ["app/api/wp-proxy/auth/status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute&page=%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute&page=%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_auth_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wp-proxy/auth/status/route.ts */ \"(rsc)/./src/app/api/wp-proxy/auth/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wp-proxy/auth/status/route\",\n        pathname: \"/api/wp-proxy/auth/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/wp-proxy/auth/status/route\"\n    },\n    resolvedPagePath: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/api/wp-proxy/auth/status/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_auth_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute&page=%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wp-proxy/auth/status/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/wp-proxy/auth/status/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_rate_limit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/rate-limit */ \"(rsc)/./src/lib/rate-limit.ts\");\n\n\n\nconst limiter = (0,_lib_rate_limit__WEBPACK_IMPORTED_MODULE_2__.rateLimit)(_lib_rate_limit__WEBPACK_IMPORTED_MODULE_2__.rateLimitConfigs.auth);\nasync function GET(request) {\n    // Apply rate limiting\n    const rateLimitResult = limiter.check(request);\n    if (!rateLimitResult.success) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Too many requests. Please try again later.'\n        }, {\n            status: 429,\n            headers: {\n                'X-RateLimit-Limit': _lib_rate_limit__WEBPACK_IMPORTED_MODULE_2__.rateLimitConfigs.auth.max.toString(),\n                'X-RateLimit-Remaining': '0',\n                'X-RateLimit-Reset': rateLimitResult.resetTime?.toString() || ''\n            }\n        });\n    }\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        const allCookies = cookieStore.getAll();\n        // Build cookie header string from all cookies\n        const cookieHeader = allCookies.map((cookie)=>`${cookie.name}=${cookie.value}`).join('; ');\n        // Get WordPress URL from env or use default\n        const WORDPRESS_API_URL = \"http://tourismiq-headless.local\" || 0;\n        // Headers for WordPress request - ONLY the cookie header is needed for cookie auth\n        const headers = {\n            Cookie: cookieHeader\n        };\n        // Use our custom auth status endpoint that includes ACF fields\n        const targetUrl = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/status`;\n        const response = await fetch(targetUrl, {\n            method: 'GET',\n            headers\n        });\n        if (!response.ok) {\n            // If auth/status returns a 401/403, it means the user is not authenticated via cookie.\n            // This is an expected state for a logged-out user.\n            if (response.status === 401 || response.status === 403) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    loggedIn: false,\n                    isLoggedIn: false,\n                    user: null\n                });\n            }\n            // For other errors, return the error from WordPress\n            await response.text();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `WordPress API auth/status returned ${response.status}`,\n                loggedIn: false,\n                isLoggedIn: false,\n                user: null\n            }, {\n                status: response.status\n            });\n        }\n        const authData = await response.json();\n        // The custom endpoint returns loggedIn and user/wpUser separately\n        if (!authData.loggedIn) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                loggedIn: false,\n                isLoggedIn: false,\n                user: null\n            });\n        }\n        // Return the response with the properly formatted user data\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            loggedIn: true,\n            isLoggedIn: true,\n            user: authData.wpUser || authData.user,\n            wpUser: authData.wpUser || authData.user\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to check auth status',\n            loggedIn: false,\n            isLoggedIn: false,\n            user: null,\n            details: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wp-proxy/auth/status/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/rate-limit.ts":
/*!*******************************!*\
  !*** ./src/lib/rate-limit.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit),\n/* harmony export */   rateLimitConfigs: () => (/* binding */ rateLimitConfigs)\n/* harmony export */ });\n// In-memory store for rate limiting (use Redis in production)\nconst store = {};\nfunction rateLimit(options = {\n    max: 100,\n    window: 60000\n}) {\n    return {\n        check: (request, identifier)=>{\n            const key = identifier || getClientIdentifier(request);\n            const now = Date.now();\n            // Clean up expired entries\n            if (store[key] && store[key].resetTime < now) {\n                delete store[key];\n            }\n            // Initialize or get current count\n            if (!store[key]) {\n                store[key] = {\n                    count: 1,\n                    resetTime: now + options.window\n                };\n                return {\n                    success: true,\n                    remaining: options.max - 1\n                };\n            }\n            // Check if limit exceeded\n            if (store[key].count >= options.max) {\n                return {\n                    success: false,\n                    remaining: 0,\n                    resetTime: store[key].resetTime\n                };\n            }\n            // Increment count\n            store[key].count++;\n            return {\n                success: true,\n                remaining: options.max - store[key].count\n            };\n        }\n    };\n}\nfunction getClientIdentifier(request) {\n    // Try to get IP from various headers (for different hosting environments)\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIp = request.headers.get('x-real-ip');\n    const cfConnectingIp = request.headers.get('cf-connecting-ip');\n    const ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';\n    // Include user agent for additional uniqueness\n    const userAgent = request.headers.get('user-agent') || 'unknown';\n    return `${ip}-${userAgent.slice(0, 50)}`;\n}\n// Rate limit configurations for different endpoints\nconst rateLimitConfigs = {\n    auth: {\n        max: 30,\n        window: 60000\n    },\n    api: {\n        max: 100,\n        window: 60000\n    },\n    upload: {\n        max: 10,\n        window: 60000\n    },\n    search: {\n        max: 50,\n        window: 60000\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3JhdGUtbGltaXQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFjQSw4REFBOEQ7QUFDOUQsTUFBTUEsUUFBd0IsQ0FBQztBQUV4QixTQUFTQyxVQUFVQyxVQUE0QjtJQUFFQyxLQUFLO0lBQUtDLFFBQVE7QUFBTSxDQUFDO0lBQy9FLE9BQU87UUFDTEMsT0FBTyxDQUFDQyxTQUFzQkM7WUFDNUIsTUFBTUMsTUFBTUQsY0FBY0Usb0JBQW9CSDtZQUM5QyxNQUFNSSxNQUFNQyxLQUFLRCxHQUFHO1lBRXBCLDJCQUEyQjtZQUMzQixJQUFJVixLQUFLLENBQUNRLElBQUksSUFBSVIsS0FBSyxDQUFDUSxJQUFJLENBQUNJLFNBQVMsR0FBR0YsS0FBSztnQkFDNUMsT0FBT1YsS0FBSyxDQUFDUSxJQUFJO1lBQ25CO1lBRUEsa0NBQWtDO1lBQ2xDLElBQUksQ0FBQ1IsS0FBSyxDQUFDUSxJQUFJLEVBQUU7Z0JBQ2ZSLEtBQUssQ0FBQ1EsSUFBSSxHQUFHO29CQUNYSyxPQUFPO29CQUNQRCxXQUFXRixNQUFNUixRQUFRRSxNQUFNO2dCQUNqQztnQkFDQSxPQUFPO29CQUFFVSxTQUFTO29CQUFNQyxXQUFXYixRQUFRQyxHQUFHLEdBQUc7Z0JBQUU7WUFDckQ7WUFFQSwwQkFBMEI7WUFDMUIsSUFBSUgsS0FBSyxDQUFDUSxJQUFJLENBQUNLLEtBQUssSUFBSVgsUUFBUUMsR0FBRyxFQUFFO2dCQUNuQyxPQUFPO29CQUNMVyxTQUFTO29CQUNUQyxXQUFXO29CQUNYSCxXQUFXWixLQUFLLENBQUNRLElBQUksQ0FBQ0ksU0FBUztnQkFDakM7WUFDRjtZQUVBLGtCQUFrQjtZQUNsQlosS0FBSyxDQUFDUSxJQUFJLENBQUNLLEtBQUs7WUFFaEIsT0FBTztnQkFDTEMsU0FBUztnQkFDVEMsV0FBV2IsUUFBUUMsR0FBRyxHQUFHSCxLQUFLLENBQUNRLElBQUksQ0FBQ0ssS0FBSztZQUMzQztRQUNGO0lBQ0Y7QUFDRjtBQUVBLFNBQVNKLG9CQUFvQkgsT0FBb0I7SUFDL0MsMEVBQTBFO0lBQzFFLE1BQU1VLFlBQVlWLFFBQVFXLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO0lBQ3RDLE1BQU1DLFNBQVNiLFFBQVFXLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO0lBQ25DLE1BQU1FLGlCQUFpQmQsUUFBUVcsT0FBTyxDQUFDQyxHQUFHLENBQUM7SUFFM0MsTUFBTUcsS0FBS0wsV0FBV00sTUFBTSxJQUFJLENBQUMsRUFBRSxJQUFJSCxVQUFVQyxrQkFBa0I7SUFFbkUsK0NBQStDO0lBQy9DLE1BQU1HLFlBQVlqQixRQUFRVyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxpQkFBaUI7SUFFdkQsT0FBTyxHQUFHRyxHQUFHLENBQUMsRUFBRUUsVUFBVUMsS0FBSyxDQUFDLEdBQUcsS0FBSztBQUMxQztBQUVBLG9EQUFvRDtBQUM3QyxNQUFNQyxtQkFBbUI7SUFDOUJDLE1BQU07UUFBRXZCLEtBQUs7UUFBSUMsUUFBUTtJQUFNO0lBQy9CdUIsS0FBSztRQUFFeEIsS0FBSztRQUFLQyxRQUFRO0lBQU07SUFDL0J3QixRQUFRO1FBQUV6QixLQUFLO1FBQUlDLFFBQVE7SUFBTTtJQUNqQ3lCLFFBQVE7UUFBRTFCLEtBQUs7UUFBSUMsUUFBUTtJQUFNO0FBQ25DLEVBQUUiLCJzb3VyY2VzIjpbIi9Wb2x1bWVzL01hYyBTdG9yYWdlL0xvY2FsIFNpdGVzL3RvdXJpc21pcS1oZWFkbGVzcy9hcHAvcHVibGljL3dwLWNvbnRlbnQvdGhlbWVzL3RvdXJpc21xX3dwL2Zyb250ZW5kL3NyYy9saWIvcmF0ZS1saW1pdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcblxuaW50ZXJmYWNlIFJhdGVMaW1pdE9wdGlvbnMge1xuICBtYXg6IG51bWJlcjsgLy8gTWF4aW11bSByZXF1ZXN0c1xuICB3aW5kb3c6IG51bWJlcjsgLy8gVGltZSB3aW5kb3cgaW4gbWlsbGlzZWNvbmRzXG59XG5cbmludGVyZmFjZSBSYXRlTGltaXRTdG9yZSB7XG4gIFtrZXk6IHN0cmluZ106IHtcbiAgICBjb3VudDogbnVtYmVyO1xuICAgIHJlc2V0VGltZTogbnVtYmVyO1xuICB9O1xufVxuXG4vLyBJbi1tZW1vcnkgc3RvcmUgZm9yIHJhdGUgbGltaXRpbmcgKHVzZSBSZWRpcyBpbiBwcm9kdWN0aW9uKVxuY29uc3Qgc3RvcmU6IFJhdGVMaW1pdFN0b3JlID0ge307XG5cbmV4cG9ydCBmdW5jdGlvbiByYXRlTGltaXQob3B0aW9uczogUmF0ZUxpbWl0T3B0aW9ucyA9IHsgbWF4OiAxMDAsIHdpbmRvdzogNjAwMDAgfSkge1xuICByZXR1cm4ge1xuICAgIGNoZWNrOiAocmVxdWVzdDogTmV4dFJlcXVlc3QsIGlkZW50aWZpZXI/OiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbnN0IGtleSA9IGlkZW50aWZpZXIgfHwgZ2V0Q2xpZW50SWRlbnRpZmllcihyZXF1ZXN0KTtcbiAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG5cbiAgICAgIC8vIENsZWFuIHVwIGV4cGlyZWQgZW50cmllc1xuICAgICAgaWYgKHN0b3JlW2tleV0gJiYgc3RvcmVba2V5XS5yZXNldFRpbWUgPCBub3cpIHtcbiAgICAgICAgZGVsZXRlIHN0b3JlW2tleV07XG4gICAgICB9XG5cbiAgICAgIC8vIEluaXRpYWxpemUgb3IgZ2V0IGN1cnJlbnQgY291bnRcbiAgICAgIGlmICghc3RvcmVba2V5XSkge1xuICAgICAgICBzdG9yZVtrZXldID0ge1xuICAgICAgICAgIGNvdW50OiAxLFxuICAgICAgICAgIHJlc2V0VGltZTogbm93ICsgb3B0aW9ucy53aW5kb3csXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIHJlbWFpbmluZzogb3B0aW9ucy5tYXggLSAxIH07XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIGlmIGxpbWl0IGV4Y2VlZGVkXG4gICAgICBpZiAoc3RvcmVba2V5XS5jb3VudCA+PSBvcHRpb25zLm1heCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIHJlbWFpbmluZzogMCxcbiAgICAgICAgICByZXNldFRpbWU6IHN0b3JlW2tleV0ucmVzZXRUaW1lLFxuICAgICAgICB9O1xuICAgICAgfVxuXG4gICAgICAvLyBJbmNyZW1lbnQgY291bnRcbiAgICAgIHN0b3JlW2tleV0uY291bnQrKztcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgcmVtYWluaW5nOiBvcHRpb25zLm1heCAtIHN0b3JlW2tleV0uY291bnQsXG4gICAgICB9O1xuICAgIH0sXG4gIH07XG59XG5cbmZ1bmN0aW9uIGdldENsaWVudElkZW50aWZpZXIocmVxdWVzdDogTmV4dFJlcXVlc3QpOiBzdHJpbmcge1xuICAvLyBUcnkgdG8gZ2V0IElQIGZyb20gdmFyaW91cyBoZWFkZXJzIChmb3IgZGlmZmVyZW50IGhvc3RpbmcgZW52aXJvbm1lbnRzKVxuICBjb25zdCBmb3J3YXJkZWQgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKTtcbiAgY29uc3QgcmVhbElwID0gcmVxdWVzdC5oZWFkZXJzLmdldCgneC1yZWFsLWlwJyk7XG4gIGNvbnN0IGNmQ29ubmVjdGluZ0lwID0gcmVxdWVzdC5oZWFkZXJzLmdldCgnY2YtY29ubmVjdGluZy1pcCcpO1xuXG4gIGNvbnN0IGlwID0gZm9yd2FyZGVkPy5zcGxpdCgnLCcpWzBdIHx8IHJlYWxJcCB8fCBjZkNvbm5lY3RpbmdJcCB8fCAndW5rbm93bic7XG5cbiAgLy8gSW5jbHVkZSB1c2VyIGFnZW50IGZvciBhZGRpdGlvbmFsIHVuaXF1ZW5lc3NcbiAgY29uc3QgdXNlckFnZW50ID0gcmVxdWVzdC5oZWFkZXJzLmdldCgndXNlci1hZ2VudCcpIHx8ICd1bmtub3duJztcblxuICByZXR1cm4gYCR7aXB9LSR7dXNlckFnZW50LnNsaWNlKDAsIDUwKX1gO1xufVxuXG4vLyBSYXRlIGxpbWl0IGNvbmZpZ3VyYXRpb25zIGZvciBkaWZmZXJlbnQgZW5kcG9pbnRzXG5leHBvcnQgY29uc3QgcmF0ZUxpbWl0Q29uZmlncyA9IHtcbiAgYXV0aDogeyBtYXg6IDMwLCB3aW5kb3c6IDYwMDAwIH0sIC8vIDMwIHJlcXVlc3RzIHBlciBtaW51dGUgZm9yIGF1dGggKGluY3JlYXNlZCBmb3IgcHJvZHVjdGlvbilcbiAgYXBpOiB7IG1heDogMTAwLCB3aW5kb3c6IDYwMDAwIH0sIC8vIDEwMCByZXF1ZXN0cyBwZXIgbWludXRlIGZvciBnZW5lcmFsIEFQSVxuICB1cGxvYWQ6IHsgbWF4OiAxMCwgd2luZG93OiA2MDAwMCB9LCAvLyAxMCB1cGxvYWRzIHBlciBtaW51dGVcbiAgc2VhcmNoOiB7IG1heDogNTAsIHdpbmRvdzogNjAwMDAgfSwgLy8gNTAgc2VhcmNoZXMgcGVyIG1pbnV0ZVxufTtcbiJdLCJuYW1lcyI6WyJzdG9yZSIsInJhdGVMaW1pdCIsIm9wdGlvbnMiLCJtYXgiLCJ3aW5kb3ciLCJjaGVjayIsInJlcXVlc3QiLCJpZGVudGlmaWVyIiwia2V5IiwiZ2V0Q2xpZW50SWRlbnRpZmllciIsIm5vdyIsIkRhdGUiLCJyZXNldFRpbWUiLCJjb3VudCIsInN1Y2Nlc3MiLCJyZW1haW5pbmciLCJmb3J3YXJkZWQiLCJoZWFkZXJzIiwiZ2V0IiwicmVhbElwIiwiY2ZDb25uZWN0aW5nSXAiLCJpcCIsInNwbGl0IiwidXNlckFnZW50Iiwic2xpY2UiLCJyYXRlTGltaXRDb25maWdzIiwiYXV0aCIsImFwaSIsInVwbG9hZCIsInNlYXJjaCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/rate-limit.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute&page=%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fauth%2Fstatus%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();