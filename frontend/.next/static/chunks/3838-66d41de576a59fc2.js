"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3838],{6101:(e,t,r)=>{r.d(t,{s:()=>o,t:()=>i});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function o(...e){return n.useCallback(i(...e),e)}},14186:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19946:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:s="",children:d,iconNode:f,...p}=e;return(0,n.createElement)("svg",{ref:t,...c,width:l,height:l,stroke:r,strokeWidth:o?24*Number(i)/Number(l):i,className:a("lucide",s),...!d&&!u(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:u,...c}=r;return(0,n.createElement)(s,{ref:i,iconNode:t,className:a("lucide-".concat(l(o(e))),"lucide-".concat(e),u),...c})});return r.displayName=o(e),r}},22436:(e,t,r)=>{var n=r(12115),l="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,o=n.useEffect,a=n.useLayoutEffect,u=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!l(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),l=n[0].inst,s=n[1];return a(function(){l.value=r,l.getSnapshot=t,c(l)&&s({inst:l})},[e,r,t]),o(function(){return c(l)&&s({inst:l}),e(function(){c(l)&&s({inst:l})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},32919:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},39033:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(12115);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},46081:(e,t,r)=>{r.d(t,{A:()=>o,q:()=>i});var n=r(12115),l=r(95155);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,o=n.useMemo(()=>i,Object.values(i));return(0,l.jsx)(r.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(l){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return i.scopeName=e,[function(t,i){let o=n.createContext(i),a=r.length;r=[...r,i];let u=t=>{let{scope:r,children:i,...u}=t,c=r?.[e]?.[a]||o,s=n.useMemo(()=>u,Object.values(u));return(0,l.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(r,l){let u=l?.[e]?.[a]||o,c=n.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(i,...t)]}},49033:(e,t,r)=>{e.exports=r(22436)},51154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52712:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(12115),l=globalThis?.document?n.useLayoutEffect:()=>{}},54011:(e,t,r)=>{r.d(t,{H4:()=>N,_V:()=>A,bL:()=>E});var n=r(12115),l=r(46081),i=r(39033),o=r(52712),a=r(63655),u=r(49033);function c(){return()=>{}}var s=r(95155),d="Avatar",[f,p]=(0,l.A)(d),[m,y]=f(d),v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...l}=e,[i,o]=n.useState("idle");return(0,s.jsx)(m,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:o,children:(0,s.jsx)(a.sG.span,{...l,ref:t})})});v.displayName=d;var h="AvatarImage",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:l,onLoadingStatusChange:d=()=>{},...f}=e,p=y(h,r),m=function(e,t){let{referrerPolicy:r,crossOrigin:l}=t,i=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),a=n.useRef(null),s=i?(a.current||(a.current=new window.Image),a.current):null,[d,f]=n.useState(()=>k(s,e));return(0,o.N)(()=>{f(k(s,e))},[s,e]),(0,o.N)(()=>{let e=e=>()=>{f(e)};if(!s)return;let t=e("loaded"),n=e("error");return s.addEventListener("load",t),s.addEventListener("error",n),r&&(s.referrerPolicy=r),"string"==typeof l&&(s.crossOrigin=l),()=>{s.removeEventListener("load",t),s.removeEventListener("error",n)}},[s,l,r]),d}(l,f),v=(0,i.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==m&&v(m)},[m,v]),"loaded"===m?(0,s.jsx)(a.sG.img,{...f,ref:t,src:l}):null});w.displayName=h;var g="AvatarFallback",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:l,...i}=e,o=y(g,r),[u,c]=n.useState(void 0===l);return n.useEffect(()=>{if(void 0!==l){let e=window.setTimeout(()=>c(!0),l);return()=>window.clearTimeout(e)}},[l]),u&&"loaded"!==o.imageLoadingStatus?(0,s.jsx)(a.sG.span,{...i,ref:t}):null});function k(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=g;var E=v,A=w,N=x},63655:(e,t,r)=>{r.d(t,{hO:()=>u,sG:()=>a});var n=r(12115),l=r(47650),i=r(99708),o=r(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?r:t,{...i,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function u(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},99708:(e,t,r)=>{r.d(t,{DX:()=>a,Dc:()=>c,TL:()=>o});var n=r(12115),l=r(6101),i=r(95155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var o;let e,a,u=(o=r,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),c=function(e,t){let r={...t};for(let n in t){let l=e[n],i=t[n];/^on[A-Z]/.test(n)?l&&i?r[n]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...i}:"className"===n&&(r[n]=[l,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,l.t)(t,u):u),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,a=n.Children.toArray(l),u=a.find(s);if(u){let e=u.props.children,l=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var a=o("Slot"),u=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);