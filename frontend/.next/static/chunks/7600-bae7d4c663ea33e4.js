"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7600],{27600:(e,t,r)=>{r.d(t,{A:()=>p,AuthProvider:()=>d});var a=r(95155),o=r(12115),n=r(35695),s=r(26715),c=r(47996);let i=(0,o.createContext)(void 0),l="tourismiq_auth",u=()=>{try{let e=localStorage.getItem(l);if(e){let t=JSON.parse(e);if(t.timestamp&&Date.now()-t.timestamp<3e5)return t}}catch(e){}return null},f=(e,t)=>{try{e&&t?localStorage.setItem(l,JSON.stringify({user:e,isAuthenticated:t,timestamp:Date.now()})):localStorage.removeItem(l)}catch(e){}},d=e=>{let{children:t}=e,[r,d]=(0,o.useState)(null),[p,g]=(0,o.useState)(!1),[y,h]=(0,o.useState)(!0),[v,m]=(0,o.useState)(0),[_,w]=(0,o.useState)(!1),b=(0,n.useRouter)(),j=(0,s.jE)();(0,o.useEffect)(()=>{let e=u();e?(d(e.user),g(e.isAuthenticated),m(e.timestamp),h(!1)):h(!0),w(!0)},[]);let E=(0,o.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=Date.now();if(!e&&v&&t-v<3e5)return void h(!1);e&&(localStorage.removeItem(l),j.invalidateQueries({queryKey:["newest-members"]}),j.invalidateQueries({queryKey:["members"]}),(null==r?void 0:r.id)&&(j.invalidateQueries({queryKey:["iq-score","me"]}),j.invalidateQueries({queryKey:["iq-score","user",r.id]}))),h(!0);try{let e=await (0,c.z)();e.isAuthenticated&&e.user?(d(e.user),g(!0),f(e.user,!0)):(d(null),g(!1),f(null,!1)),m(t)}catch(e){d(null),g(!1),f(null,!1)}finally{h(!1)}},[v,j]);(0,o.useEffect)(()=>{_&&E()},[_,E]);let A=async(e,t)=>{h(!0);try{let r=await (0,c.Lx)(e,t);if(!r.success||!r.user)return d(null),g(!1),f(null,!1),{success:!1,error:r.error||"Login failed"};d(r.user),g(!0),f(r.user,!0),m(Date.now());try{await E(!0)}catch(e){console.warn("Failed to refresh auth after login:",e)}return{success:!0}}catch(e){return d(null),g(!1),{success:!1,error:e instanceof Error?e.message:"Login failed"}}finally{h(!1)}},k=async()=>{h(!0);try{await (0,c.y4)()?(d(null),g(!1),f(null,!1),m(0),b.push("/")):console.error("Logout failed")}catch(e){console.error("Logout error:",e instanceof Error?e.message:"Logout failed")}finally{h(!1)}},S=async e=>{try{let s=await (0,c.eg)(e);if(!s.success)throw Error(s.error||"Failed to update profile");if(r){var t,a,o,n;let s={...r,...e,acf:r.acf?{...r.acf,job_title:void 0!==e.jobTitle?e.jobTitle:(null==(t=r.acf)?void 0:t.job_title)||"",location:void 0!==e.location?e.location:(null==(a=r.acf)?void 0:a.location)||"",profile_picture:void 0!==e.avatarUrl?e.avatarUrl:null==(o=r.acf)?void 0:o.profile_picture,cover_image:void 0!==e.coverImageUrl?e.coverImageUrl:null==(n=r.acf)?void 0:n.cover_image}:void 0};d(s)}}catch(e){throw console.error("Error updating profile:",e),e}finally{h(!1)}},P=(0,o.useCallback)(async()=>{await E(!0)},[E]);return(0,a.jsx)(i.Provider,{value:{user:r,isLoading:y,isAuthenticated:p,login:A,logout:k,updateProfile:S,refreshAuth:P},children:t})},p=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},47996:(e,t,r)=>{r.d(t,{z:()=>n,pD:()=>u.pD,bW:()=>f,oj:()=>u.oj,Z1:()=>d,Lx:()=>s,y4:()=>c,eg:()=>i});var a=r(77281);async function o(){if(!a.oc)return null;try{let e=await fetch("".concat(a.tE,"/user/me"),{method:"GET",credentials:"include"});if(!e.ok){if(401===e.status)return console.warn("User not authenticated (401 from /user/me)"),null;console.warn("Failed to fetch user data from Next.js API: ".concat(e.status,", trying WordPress API directly..."));let t=await fetch("".concat(a.tE,"/wp-proxy/auth/status"),{method:"GET",credentials:"include"});if(!t.ok)return console.warn("Failed to fetch user data from WordPress API:",t.status),null;let r=await t.json();if(r&&r.user)return r.user;return null}return await e.json()}catch(e){return console.error("Error fetching current user:",e),null}}async function n(){try{let e=await fetch("".concat(a.tE,"/wp-proxy/auth/status"),{method:"GET",credentials:"include"});if(!e.ok)return console.warn("Auth status check failed with status:",e.status),{isAuthenticated:!1,user:null};let t=await e.json();if(!(t.isAuthenticated||t.isLoggedIn||t.loggedIn)||!t.user)return{isAuthenticated:!1,user:null};let r=l(t.user);return{isAuthenticated:!0,user:r}}catch(e){return console.error("Error checking auth status:",e),{isAuthenticated:!1,user:null}}}async function s(e,t){try{let r=await fetch("".concat(a.tE,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t}),credentials:"include"});if(!r.ok){let e=await r.json().catch(()=>({}));return console.error("Login failed with status:",r.status,e),{success:!1,error:e.message||"Login failed: ".concat(r.status)}}let n=await r.json();if(!n.user){let e=await o();if(e){let t=l(e);return{success:!0,user:t}}return{success:!0,error:"Login successful but no user data returned"}}let s=l(n.user);return{success:!0,user:s}}catch(e){return console.error("Login error:",e),{success:!1,error:"An error occurred"}}}async function c(){try{if(!(await fetch("".concat(a.tE,"/auth/logout"),{method:"POST",credentials:"include"})).ok)return!1;return!0}catch(e){return!1}}async function i(e){try{let t=await fetch("".concat(a.tE,"/user/profile"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),credentials:(0,a.q5)()});if(!t.ok){let e=await t.json().catch(()=>({}));return{success:!1,error:e.message||"Update failed: ".concat(t.status)}}let r=await t.json();return{success:!0,user:r.user}}catch(e){return console.error("Error updating user profile:",e),{success:!1,error:e instanceof Error?e.message:"An error occurred"}}}function l(e){var t,r,a,o,n,s,c,i,l,u,f;let d=e.first_name||null,p=e.last_name||null,g=e.display_name||e.name||"",y=(null==(t=e.acf)?void 0:t.bio)||null,h=(null==(r=e.acf)?void 0:r.job_title)||null,v=(null==(a=e.acf)?void 0:a.company)||null,m=(null==(o=e.acf)?void 0:o.location)||null,_=(null==(n=e.acf)?void 0:n.phone)||null,w=(null==(s=e.acf)?void 0:s.website)||null,b=(null==(c=e.acf)?void 0:c.twitter)||null,j=(null==(i=e.acf)?void 0:i.facebook)||null,E=(null==(l=e.acf)?void 0:l.linkedin)||null,A=(null==(u=e.acf)?void 0:u.instagram)||null;return{id:e.id,username:e.username||e.user_login||e.slug,name:g,email:e.email||e.user_email||"",firstName:d,lastName:p,avatarUrl:function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:96;if(!e)return"";if(null==(t=e.acf)?void 0:t.profile_picture){if("string"==typeof e.acf.profile_picture)return e.acf.profile_picture;if("object"==typeof e.acf.profile_picture&&e.acf.profile_picture.url)return e.acf.profile_picture.url;if("object"==typeof e.acf.profile_picture&&e.acf.profile_picture.sizes){let t="".concat(r,"x").concat(r);return e.acf.profile_picture.sizes[t]?e.acf.profile_picture.sizes[t]:e.acf.profile_picture.url||""}}if(e.avatar_urls){let t=r.toString();if(e.avatar_urls[t])return e.avatar_urls[t];if(e.avatar_urls["96"])return e.avatar_urls["96"];if(e.avatar_urls["48"])return e.avatar_urls["48"];if(e.avatar_urls["24"])return e.avatar_urls["24"]}return""}(e),coverImageUrl:(f=e.coverImage)?"string"==typeof f?f:"object"==typeof f&&f.url?f.url:"":"",bio:"string"==typeof y?y:"",roles:function(e){if(Array.isArray(e.roles))return e.roles;if(e.roles&&"object"==typeof e.roles&&!Array.isArray(e.roles))return Object.values(e.roles);if(Array.isArray(e._user_roles))return e._user_roles;if("string"==typeof e.roles)try{let t=JSON.parse(e.roles);return Array.isArray(t)?t:[]}catch(t){return[e.roles]}if("string"==typeof e._user_roles)try{let t=JSON.parse(e._user_roles);return Array.isArray(t)?t:[]}catch(t){return[e._user_roles]}return e.capabilities?Object.keys("string"==typeof e.capabilities?JSON.parse(e.capabilities):e.capabilities).filter(e=>e.includes("um_")||"administrator"===e||"subscriber"===e||"editor"===e):["subscriber"]}(e),jobTitle:"string"==typeof h?h:"",company:"string"==typeof v?v:"",location:"string"==typeof m?m:"",phone:"string"==typeof _?_:"",website:"string"==typeof w?w:"",socialLinks:{twitter:null!=b?b:null,facebook:null!=j?j:null,linkedin:null!=E?E:null,instagram:null!=A?A:null},acf:e.acf?{...e.acf}:{bio:"string"==typeof y?y:"",job_title:"string"==typeof h?h:"",company:"string"==typeof v?v:"",location:"string"==typeof m?m:"",phone:"string"==typeof _?_:"",website:"string"==typeof w?w:"",twitter:null!=b?b:"",facebook:null!=j?j:"",linkedin:null!=E?E:"",instagram:null!=A?A:""}}}var u=r(94335);async function f(){try{let e=await fetch("".concat(a.tE,"/wp-proxy/categories?per_page=100"),{credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)return console.error("Failed to fetch categories:",e.status),[];let t=await e.json();if(!Array.isArray(t)||0===t.length)return[];return t.map(e=>({id:e.id,name:e.name,slug:e.slug,link:e.link||"/category/".concat(e.slug||e.id),taxonomy:"category"}))}catch(e){return console.error("Error fetching categories:",e),[]}}async function d(){try{let e=await fetch("/api/wp-proxy/user/assigned-vendors",{credentials:"include"});if(!e.ok)throw Error("Failed to fetch assigned vendors: ".concat(e.status));return await e.json()}catch(e){throw console.error("Error fetching assigned vendors:",e),e}}},77281:(e,t,r)=>{r.d(t,{QZ:()=>s,iT:()=>c,oc:()=>o,q5:()=>n,tE:()=>a}),r(49509);let a="/api",o=!0;function n(){return"include"}function s(e){return e.replace(/<[^>]*>?/gm,"").replace(/\[.*?\]/g,"").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#0?39;/g,"'").replace(/&#0?38;/g,"&").replace(/&#8217;/g,"'").replace(/&#8211;/g,"–").replace(/&#8212;/g,"—").replace(/&#8216;/g,"'").replace(/&#8220;/g,'"').replace(/&#8221;/g,'"').replace(/&#8230;/g,"…").trim()}function c(e){return e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#0?39;/g,"'").replace(/&#0?38;/g,"&").replace(/&#8217;/g,"'").replace(/&#8211;/g,"–").replace(/&#8212;/g,"—").replace(/&#8216;/g,"'").replace(/&#8220;/g,'"').replace(/&#8221;/g,'"').replace(/&#8230;/g,"…")}},94335:(e,t,r)=>{r.d(t,{bG:()=>s,oj:()=>c,pD:()=>n,ue:()=>o});var a=r(77281);async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"date",o=arguments.length>3?arguments[3]:void 0,n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];try{let s=new URLSearchParams({per_page:t.toString(),page:e.toString(),_embed:"1",orderby:r,order:"desc"});o&&s.set("meta_key",o),n&&s.set("exclude_vendor_posts","true");let c=await fetch("".concat(a.tE,"/wp-proxy/posts?sticky=true&").concat(s.toString()),{headers:{"Content-Type":"application/json"},credentials:"include"});if(c.ok){let e=await c.json();if(Array.isArray(e)&&e.length>0){let t=parseInt(c.headers.get("X-WP-TotalPages")||"1",10),r=parseInt(c.headers.get("X-WP-Total")||e.length.toString(),10);return{posts:e,totalPages:t,totalPosts:r}}}else console.warn("Failed to fetch sticky posts: ".concat(c.status,". Trying regular posts..."));if(!(c=await fetch("".concat(a.tE,"/wp-proxy/posts?").concat(s.toString()),{headers:{"Content-Type":"application/json"},credentials:"include"})).ok)throw Error("Failed to fetch posts: ".concat(c.status));let i=await c.json(),l=parseInt(c.headers.get("X-WP-TotalPages")||"1",10),u=parseInt(c.headers.get("X-WP-Total")||i.length.toString(),10);return{posts:Array.isArray(i)?i:[],totalPages:l,totalPosts:u}}catch(e){return console.error("Error fetching posts:",e),{posts:[],totalPages:0,totalPosts:0}}}async function n(e){try{console.log("[createPost API] Received data:",{title:e.title,content:e.content.substring(0,50)+"...",vendorId:e.vendorId,categories:e.categories,hasImageFile:!!e.imageFile,acfFields:e.acfFields});let t=new FormData;if(t.append("title",e.title),t.append("content",e.content),t.append("status","publish"),e.categories&&e.categories.length>0&&e.categories.forEach((e,r)=>{t.append("categories[".concat(r,"]"),e.toString())}),e.vendorId&&(console.log("[createPost API] Adding vendor_id to FormData:",e.vendorId),t.append("vendor_id",e.vendorId.toString())),e.imageFile&&t.append("featured_media",e.imageFile),e.acfFields){let r={},a={};Object.entries(e.acfFields).forEach(e=>{let[o,n]=e;n instanceof File?(a[o]=n,t.append("acf_file_".concat(o),n)):r[o]=n}),Object.keys(r).length>0&&t.append("acf_fields",JSON.stringify(r))}let r=await fetch("".concat(a.tE,"/wp-proxy/posts/create"),{method:"POST",body:t,credentials:"include"});if(!r.ok){let e=await r.json().catch(()=>({}));throw console.error("Create post error:",r.status,e),Error("Failed to create post: ".concat(r.status," ").concat(e.message||""))}return await r.json()}catch(e){return console.error("Error creating post:",e),null}}async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"date",n=arguments.length>4?arguments[4]:void 0;try{let s=new URLSearchParams({per_page:t.toString(),page:r.toString(),_embed:"1",orderby:o,order:"desc"});n&&s.set("meta_key",n);let c=await fetch("".concat(a.tE,"/wp-proxy/posts/by-category/").concat(encodeURIComponent(e),"?").concat(s.toString()),{headers:{"Content-Type":"application/json"},credentials:"include"});if(!c.ok)return console.warn("[getPostsByCategory] Failed to fetch posts for category '".concat(e,"': ").concat(c.status)),{posts:[],totalPages:0,totalPosts:0};let i=await c.json(),l=parseInt(c.headers.get("X-WP-TotalPages")||"0",10),u=parseInt(c.headers.get("X-WP-Total")||"0",10);return{posts:Array.isArray(i)?i:[],totalPages:l,totalPosts:u}}catch(t){return console.error("Error fetching posts for category '".concat(e,"':"),t),{posts:[],totalPages:0,totalPosts:0}}}async function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;try{let t=await fetch("".concat(a.tE,"/wp-proxy/categories?slug=event"),{headers:{"Content-Type":"application/json"},credentials:"include"});if(!t.ok)return console.warn("Failed to fetch event category: ".concat(t.status)),[];let r=await t.json();if(!Array.isArray(r)||0===r.length)return console.warn("Event category not found"),[];let o=r[0].id,n=await fetch("".concat(a.tE,"/wp-proxy/posts?categories=").concat(o,"&per_page=").concat(e,"&_embed&orderby=date&order=desc"),{headers:{"Content-Type":"application/json"},credentials:"include"});if(!n.ok)return console.warn("Failed to fetch event posts: ".concat(n.status)),[];let s=await n.json();if(!Array.isArray(s))return[];return s.map(e=>{let t={...e};return e.acf&&(e.acf.event_start_date?e.acf.event_end_date?t.formatted_date="".concat(e.acf.event_start_date," - ").concat(e.acf.event_end_date):t.formatted_date=e.acf.event_start_date:t.formatted_date=new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),e.acf.event_host_logo&&"object"==typeof e.acf.event_host_logo&&!e.acf.event_host_logo.url&&e.acf.event_host_logo.ID&&console.warn("Host logo is missing URL property:",e.acf.event_host_logo)),t})}catch(e){return console.error("Error fetching event posts:",e),[]}}r(49509),r(49641).Buffer}}]);