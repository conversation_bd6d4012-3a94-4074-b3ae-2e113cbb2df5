"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3091],{54165:(e,t,n)=>{n.d(t,{Cf:()=>u,Es:()=>p,L3:()=>f,c7:()=>y,lG:()=>s,rr:()=>g,zM:()=>c});var r=n(95155);n(12115);var a=n(15452),o=n(54416),i=n(59434);function s(e){let{...t}=e;return(0,r.jsx)(a.bL,{"data-slot":"dialog",...t})}function c(e){let{...t}=e;return(0,r.jsx)(a.l9,{"data-slot":"dialog-trigger",...t})}function l(e){let{...t}=e;return(0,r.jsx)(a.<PERSON><PERSON>,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...n}=e;return(0,r.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",t),...n})}function u(e){let{className:t,children:n,...s}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(d,{}),(0,r.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-[0_32px_65px_-15px_rgba(0,0,0,0.9)] duration-200 sm:max-w-lg",t),...s,children:[n,(0,r.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function y(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...n})}function p(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...n})}function f(e){let{className:t,...n}=e;return(0,r.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...n})}function g(e){let{className:t,...n}=e;return(0,r.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...n})}},66695:(e,t,n)=>{n.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>s,wL:()=>u});var r=n(95155),a=n(12115),o=n(59434);let i=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...a})});i.displayName="Card";let s=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",n),...a})});s.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",n),...a})});c.displayName="CardTitle";let l=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",n),...a})});l.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("p-6",n),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6",n),...a})});u.displayName="CardFooter"},93945:(e,t,n)=>{n.d(t,{ZY:()=>u,n5:()=>d,qV:()=>l});var r=n(32960),a=n(26715),o=n(5041),i=n(93853),s=n(64614),c=n(27600);function l(e){let{isAuthenticated:t}=(0,c.A)(),{isAuthenticated:n}=(0,s.iZ)(),{data:a,isLoading:o,refetch:l}=(0,r.I)({queryKey:i.lH.connections.status(e),queryFn:async()=>{let t=await fetch("/api/wp-proxy/connections/status/".concat(e),{credentials:"include"});if(!t.ok)throw Error("Failed to get connection status");return t.json()},enabled:(null!=t?t:n)&&!!e,staleTime:0,gcTime:3e5});return{status:a||null,loading:o,refreshStatus:l}}function d(e){let{isAuthenticated:t}=(0,c.A)(),{isAuthenticated:n}=(0,s.iZ)(),{data:a,isLoading:o,error:l,refetch:d}=(0,r.I)({queryKey:i.lH.connections.list(e),queryFn:async()=>{let t=await fetch(e?"/api/wp-proxy/connections?userId=".concat(e):"/api/wp-proxy/connections",{credentials:"include"});if(!t.ok)throw Error("Failed to get connections");let n=await t.json();return Array.isArray(n)?n:n&&Array.isArray(n.connections)?n.connections:n&&n.data&&Array.isArray(n.data)?n.data:(console.warn("Unexpected connections response format:",n),[])},enabled:null!=t?t:n,staleTime:6e5,gcTime:9e5,refetchOnWindowFocus:!1});return{connections:a||[],loading:o,error:(null==l?void 0:l.message)||null,refreshConnections:d}}function u(){var e,t,n,r;let s=function(){let e=(0,a.jE)();return(0,o.n)({mutationFn:async e=>{let t=await fetch("/api/wp-proxy/connections/request",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e}),credentials:"include"});if(!t.ok)throw Error((await t.json()).error||"Failed to send connection request");return!0},onSuccess:(t,n)=>{e.invalidateQueries({queryKey:i.lH.connections.status(n)}),e.invalidateQueries({queryKey:i.lH.connections.pending()}),e.invalidateQueries({queryKey:["bulk-connections"],predicate:e=>{let t=e.queryKey;return!!(Array.isArray(t)&&"bulk-connections"===t[0]&&Array.isArray(t[1]))&&t[1].includes(n)}})}})}(),c=function(){let e=(0,a.jE)();return(0,o.n)({mutationFn:async e=>{let t=await fetch("/api/wp-proxy/connections/accept",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e}),credentials:"include"});if(!t.ok)throw Error((await t.json()).error||"Failed to accept connection request");return!0},onSuccess:(t,n)=>{e.invalidateQueries({queryKey:i.lH.connections.status(n)}),e.invalidateQueries({queryKey:i.lH.connections.pending()}),e.invalidateQueries({queryKey:i.lH.connections.all}),e.invalidateQueries({queryKey:["bulk-connections"],predicate:e=>{let t=e.queryKey;return!!(Array.isArray(t)&&"bulk-connections"===t[0]&&Array.isArray(t[1]))&&t[1].includes(n)}})}})}(),l=function(){let e=(0,a.jE)();return(0,o.n)({mutationFn:async e=>{let t=await fetch("/api/wp-proxy/connections/decline",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e}),credentials:"include"});if(!t.ok)throw Error((await t.json()).error||"Failed to decline connection request");return!0},onSuccess:(t,n)=>{e.invalidateQueries({queryKey:i.lH.connections.status(n)}),e.invalidateQueries({queryKey:i.lH.connections.pending()}),e.invalidateQueries({queryKey:["bulk-connections"],predicate:e=>{let t=e.queryKey;return!!(Array.isArray(t)&&"bulk-connections"===t[0]&&Array.isArray(t[1]))&&t[1].includes(n)}})}})}(),d=function(){let e=(0,a.jE)();return(0,o.n)({mutationFn:async e=>{let t=await fetch("/api/wp-proxy/connections/remove",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e}),credentials:"include"});if(!t.ok)throw Error((await t.json()).error||"Failed to remove connection");return!0},onSuccess:(t,n)=>{e.invalidateQueries({queryKey:i.lH.connections.status(n)}),e.invalidateQueries({queryKey:i.lH.connections.all}),e.invalidateQueries({queryKey:["bulk-connections"],predicate:e=>{let t=e.queryKey;return!!(Array.isArray(t)&&"bulk-connections"===t[0]&&Array.isArray(t[1]))&&t[1].includes(n)}})}})}(),u=async e=>{try{return await s.mutateAsync(e),!0}catch(e){return console.error("Failed to send connection request:",e),!1}},y=async e=>{try{return await c.mutateAsync(e),!0}catch(e){return console.error("Failed to accept connection request:",e),!1}},p=async e=>{try{return await l.mutateAsync(e),!0}catch(e){return console.error("Failed to decline connection request:",e),!1}},f=async e=>{try{return await d.mutateAsync(e),!0}catch(e){return console.error("Failed to remove connection:",e),!1}},g=async e=>{try{let t=await fetch("/api/wp-proxy/connections/status/".concat(e),{credentials:"include"});if(!t.ok)throw Error("Failed to get connection status");return await t.json()}catch(e){return console.error("Failed to get connection status:",e),null}},m=async()=>{try{let e=await fetch("/api/wp-proxy/connections/pending",{credentials:"include"});if(!e.ok)throw Error("Failed to get pending requests");return await e.json()}catch(e){return console.error("Failed to get pending requests:",e),null}},x=async e=>{try{let t=await fetch(e?"/api/wp-proxy/connections?userId=".concat(e):"/api/wp-proxy/connections",{credentials:"include"});if(!t.ok)throw Error("Failed to get connections");let n=await t.json();if(Array.isArray(n))return n;if(n&&Array.isArray(n.connections))return n.connections;if(n&&n.data&&Array.isArray(n.data))return n.data;return console.warn("Unexpected connections response format:",n),[]}catch(e){return console.error("Failed to get connections:",e),null}};return{loading:s.isPending||c.isPending||l.isPending||d.isPending,error:(null==(e=s.error)?void 0:e.message)||(null==(t=c.error)?void 0:t.message)||(null==(n=l.error)?void 0:n.message)||(null==(r=d.error)?void 0:r.message)||null,sendConnectionRequest:u,acceptConnectionRequest:y,declineConnectionRequest:p,removeConnection:f,getConnectionStatus:g,getPendingRequests:m,getConnections:x,clearError:()=>{s.reset(),c.reset(),l.reset(),d.reset()}}}}}]);