(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1989,8662],{6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>o,t:()=>a});var n=t(12115);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}function o(...e){return n.useCallback(a(...e),e)}},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var n=t(12115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),o=e=>{let r=a(e);return r.charAt(0).toUpperCase()+r.slice(1)},s=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},l=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:u="",children:c,iconNode:f,...p}=e;return(0,n.createElement)("svg",{ref:r,...d,width:i,height:i,stroke:t,strokeWidth:o?24*Number(a)/Number(i):a,className:s("lucide",u),...!c&&!l(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),c=(e,r)=>{let t=(0,n.forwardRef)((t,a)=>{let{className:l,...d}=t;return(0,n.createElement)(u,{ref:a,iconNode:r,className:s("lucide-".concat(i(o(e))),"lucide-".concat(e),l),...d})});return t.displayName=o(e),t}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>s});var n=t(95155);t(12115);var i=t(99708),a=t(74466),o=t(59434);let s=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:a,asChild:l=!1,...d}=e,u=l?i.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,o.cn)(s({variant:t,size:a,className:r})),...d})}},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},39435:(e,r,t)=>{"use strict";t.d(r,{BackToSectionHeader:()=>l});var n=t(95155),i=t(6874),a=t.n(i),o=t(30285),s=t(35169);function l(e){let{href:r,text:t,className:i=""}=e;return(0,n.jsx)("div",{className:"sticky top-[79px] md:top-[90px] z-40 backdrop-blur-md bg-white/90 py-2.5 px-4 border-b shadow-lg ".concat(i),children:(0,n.jsx)(o.$,{variant:"ghost",size:"sm",asChild:!0,className:"text-[#3d405b] hover:bg-[rgba(92,200,255,0.2)] h-10 px-6 min-w-[150px]",children:(0,n.jsxs)(a(),{href:r,className:"flex items-center gap-2",children:[(0,n.jsx)(s.A,{className:"h-5 w-5"}),t]})})})}},46281:(e,r,t)=>{Promise.resolve().then(t.bind(t,39435))},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var n=t(52596),i=t(39688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,i.QP)((0,n.$)(r))}},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>s,Dc:()=>d,TL:()=>o});var n=t(12115),i=t(6101),a=t(95155);function o(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...a}=e;if(n.isValidElement(t)){var o;let e,s,l=(o=t,(s=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(s=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,r){let t={...r};for(let n in r){let i=e[n],a=r[n];/^on[A-Z]/.test(n)?i&&a?t[n]=(...e)=>{let r=a(...e);return i(...e),r}:i&&(t[n]=i):"style"===n?t[n]={...i,...a}:"className"===n&&(t[n]=[i,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==n.Fragment&&(d.ref=r?(0,i.t)(r,l):l),n.cloneElement(t,d)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:i,...o}=e,s=n.Children.toArray(i),l=s.find(u);if(l){let e=l.props.children,i=s.map(r=>r!==l?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,a.jsx)(r,{...o,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}var s=o("Slot"),l=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=l,r}function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{var r=r=>e(e.s=r);e.O(0,[5003,6874,8441,1684,7358],()=>r(46281)),_N_E=e.O()}]);