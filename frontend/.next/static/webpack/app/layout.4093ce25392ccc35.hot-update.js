"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2bf1df6df0ee\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVm9sdW1lcy9NYWMgU3RvcmFnZS9Mb2NhbCBTaXRlcy90b3VyaXNtaXEtaGVhZGxlc3MvYXBwL3B1YmxpYy93cC1jb250ZW50L3RoZW1lcy90b3VyaXNtcV93cC9mcm9udGVuZC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmJmMWRmNmRmMGVlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle,LogOut,MessageSquare,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle,LogOut,MessageSquare,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle,LogOut,MessageSquare,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle,LogOut,MessageSquare,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle,LogOut,MessageSquare,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle,LogOut,MessageSquare,Search,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_NotificationMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/NotificationMenu */ \"(app-pages-browser)/./src/components/ui/NotificationMenu.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"(app-pages-browser)/./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _components_messaging__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/messaging */ \"(app-pages-browser)/./src/components/messaging/index.ts\");\n/* harmony import */ var _components_leaderboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/leaderboard */ \"(app-pages-browser)/./src/components/leaderboard/index.ts\");\n/* harmony import */ var _components_ui_UserAvatarWithRank__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/UserAvatarWithRank */ \"(app-pages-browser)/./src/components/ui/UserAvatarWithRank.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _MobileMenu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./MobileMenu */ \"(app-pages-browser)/./src/components/layout/MobileMenu.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_SupportModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/SupportModal */ \"(app-pages-browser)/./src/components/ui/SupportModal.tsx\");\n/* harmony import */ var _components_ui_FeedbackModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/FeedbackModal */ \"(app-pages-browser)/./src/components/ui/FeedbackModal.tsx\");\n/* harmony import */ var _components_ui_SearchModal__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/SearchModal */ \"(app-pages-browser)/./src/components/ui/SearchModal.tsx\");\n/* harmony import */ var _components_ui_BackToTopButton__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/BackToTopButton */ \"(app-pages-browser)/./src/components/ui/BackToTopButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    var _user_acf, _user_acf_profile_picture, _user_acf1, _user_acf2;\n    _s();\n    const { user, isAuthenticated, isLoading, logout } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { unreadCount } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_6__.useNotifications)();\n    const { openMessageList, hasUnreadMessages, totalUnreadCount } = (0,_components_messaging__WEBPACK_IMPORTED_MODULE_7__.useMessaging)();\n    // State for notification menu\n    const [notificationMenuOpen, setNotificationMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for leaderboard\n    const [leaderboardOpen, setLeaderboardOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for support and feedback modals\n    const [supportModalOpen, setSupportModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [feedbackModalOpen, setFeedbackModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for search modal\n    const [searchModalOpen, setSearchModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Track hydration state to prevent mismatch\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            setIsHydrated(true);\n        }\n    }[\"Header.useEffect\"], []);\n    // Global keyboard shortcut for search (Cmd+K / Ctrl+K)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Header.useEffect.handleKeyDown\": (e)=>{\n                    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {\n                        e.preventDefault();\n                        setSearchModalOpen(true);\n                    }\n                }\n            }[\"Header.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Header.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white sticky top-0 z-50 h-[80px] md:h-[90px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full flex items-center justify-center px-3 md:px-4 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-[1360px] flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileMenu__WEBPACK_IMPORTED_MODULE_11__.MobileMenu, {}, void 0, false, {\n                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"flex items-center h-[36px] md:h-[42px] object-contain\",\n                                            onClick: ()=>{\n                                                // Clear any active category filters when logo is clicked\n                                                if (window.setFeedCategory) {\n                                                    window.setFeedCategory(null);\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    src: \"/images/icons/icon-tourism.svg\",\n                                                    alt: \"TourismIQ\",\n                                                    height: 28,\n                                                    width: 28,\n                                                    className: \"h-7 w-7 md:hidden object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    src: \"/images/logo.svg\",\n                                                    alt: \"TourismIQ\",\n                                                    height: 42,\n                                                    width: 168,\n                                                    className: \"hidden md:block h-[42px] max-w-[168px] object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        isHydrated && !isLoading && isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"min-[1024px]:hidden text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff]/20 h-8 w-8 sm:h-9 sm:w-9\",\n                                            onClick: ()=>setSearchModalOpen(true),\n                                            title: \"Search\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"size-[1.4rem] sm:h-[18px] sm:w-[18px]\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                isHydrated && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center ml-auto space-x-2 md:space-x-4 pr-2 md:pr-4\",\n                                    children: isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"relative text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10\",\n                                                onClick: openMessageList,\n                                                title: \"Messages\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        src: \"/images/icons/icon-messages.svg\",\n                                                        alt: \"Messages\",\n                                                        width: 18,\n                                                        height: 18,\n                                                        className: \"h-[18px] w-[18px] md:h-5 md:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    hasUnreadMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute -top-0.5 -right-0.5 md:-top-1 md:-right-1 flex h-4 w-4 md:h-5 md:w-5 items-center justify-center rounded-full bg-red-500 text-[9px] md:text-[10px] text-white font-bold\",\n                                                        children: totalUnreadCount > 9 ? '9+' : totalUnreadCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"relative text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10\",\n                                                onClick: ()=>setNotificationMenuOpen(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        src: \"/images/icons/icon-notification.svg\",\n                                                        alt: \"Notifications\",\n                                                        width: 18,\n                                                        height: 18,\n                                                        className: \"h-[18px] w-[18px] md:h-5 md:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute -top-0.5 -right-0.5 md:-top-1 md:-right-1 flex h-4 w-4 md:h-5 md:w-5 items-center justify-center rounded-full bg-red-500 text-[9px] md:text-[10px] text-white font-bold\",\n                                                        children: unreadCount > 9 ? '9+' : unreadCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_NotificationMenu__WEBPACK_IMPORTED_MODULE_5__.NotificationMenu, {\n                                                isOpen: notificationMenuOpen,\n                                                onClose: ()=>setNotificationMenuOpen(false),\n                                                onNotificationsRead: ()=>{}\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10\",\n                                                onClick: ()=>setLeaderboardOpen(true),\n                                                title: \"View Leaderboard\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    src: \"/images/icons/icon-leaderboard.svg\",\n                                                    alt: \"Leaderboard\",\n                                                    width: 18,\n                                                    height: 18,\n                                                    className: \"h-[18px] w-[18px] md:h-5 md:w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leaderboard__WEBPACK_IMPORTED_MODULE_8__.Leaderboard, {\n                                                isOpen: leaderboardOpen,\n                                                onClose: ()=>setLeaderboardOpen(false)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenu, {\n                                                modal: false,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"relative h-10 w-10 md:h-12 md:w-12 rounded-full p-0 hover:bg-[#5cc8ff]/10 cursor-pointer\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UserAvatarWithRank__WEBPACK_IMPORTED_MODULE_9__.UserAvatarWithRank, {\n                                                                    userId: (user === null || user === void 0 ? void 0 : user.id) || 0,\n                                                                    avatarUrl: (user === null || user === void 0 ? void 0 : user.avatarUrl) || (typeof (user === null || user === void 0 ? void 0 : (_user_acf = user.acf) === null || _user_acf === void 0 ? void 0 : _user_acf.profile_picture) === 'string' ? user.acf.profile_picture : user === null || user === void 0 ? void 0 : (_user_acf1 = user.acf) === null || _user_acf1 === void 0 ? void 0 : (_user_acf_profile_picture = _user_acf1.profile_picture) === null || _user_acf_profile_picture === void 0 ? void 0 : _user_acf_profile_picture.url) || (user === null || user === void 0 ? void 0 : (_user_acf2 = user.acf) === null || _user_acf2 === void 0 ? void 0 : _user_acf2.avatar_url) || '/images/avatar-placeholder.svg',\n                                                                    displayName: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n                                                                    size: \"h-7 w-7 md:h-8 md:w-8\",\n                                                                    containerSize: \"w-10 h-10 md:w-12 md:h-12\",\n                                                                    showRankBadge: true,\n                                                                    userRoles: user === null || user === void 0 ? void 0 : user.roles\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuContent, {\n                                                        className: \"w-56 rounded-[30px] shadow-[0_20px_60px_-12px_rgba(0,0,0,0.25),0_8px_24px_-4px_rgba(0,0,0,0.15)] p-4\",\n                                                        align: \"end\",\n                                                        forceMount: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuLabel, {\n                                                                className: \"font-normal\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium leading-none\",\n                                                                            children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs leading-none text-muted-foreground\",\n                                                                            children: (user === null || user === void 0 ? void 0 : user.email) || ''\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                                                asChild: true,\n                                                                className: \"flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)] cursor-pointer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: user && user.username ? \"/profile/\".concat(user.username) : '/profile',\n                                                                    className: \"w-full flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                            lineNumber: 256,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Profile\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                            lineNumber: 257,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                                                asChild: true,\n                                                                className: \"flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)] cursor-pointer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/settings\",\n                                                                    className: \"w-full flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                            lineNumber: 266,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                                                className: \"cursor-pointer flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]\",\n                                                                onClick: ()=>setSupportModalOpen(true),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Support\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                                                className: \"cursor-pointer flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]\",\n                                                                onClick: ()=>setFeedbackModalOpen(true),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Feedback\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                                                className: \"cursor-pointer flex items-center rounded-full px-4 py-2 text-red-600 font-medium transition-all duration-200 ease-in-out hover:bg-red-50 focus:bg-red-50 focus:text-red-700\",\n                                                                onClick: handleLogout,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Log out\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                asChild: true,\n                                                className: \"text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold h-8 px-3 text-sm md:h-10 md:px-4 md:text-base\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    children: \"Log In\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                asChild: true,\n                                                className: \"bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-extrabold border-2 border-[#5cc8ff] h-8 px-3 text-sm md:h-10 md:px-4 md:text-base\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/register\",\n                                                    children: \"Sign Up\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-[350px] lg:max-w-[400px] px-4 hidden min-[1024px]:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSearchModalOpen(true),\n                                className: \"relative w-full h-10 lg:h-12 bg-gray-100 border border-gray-200 rounded-full hover:bg-gray-50 transition-colors group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_LogOut_MessageSquare_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"absolute left-3 h-4 w-4 lg:h-5 lg:w-5 text-gray-400 group-hover:text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"pl-9 lg:pl-10 pr-10 lg:pr-12 text-sm lg:text-base text-gray-500 group-hover:text-gray-700\",\n                                            children: \"Search posts, people, vendors...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-3 text-xs text-muted-foreground\",\n                                            children: \"⌘K\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackToTopButton__WEBPACK_IMPORTED_MODULE_16__.BackToTopButton, {}, void 0, false, {\n                            fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SupportModal__WEBPACK_IMPORTED_MODULE_13__.SupportModal, {\n                isOpen: supportModalOpen,\n                onClose: ()=>setSupportModalOpen(false)\n            }, void 0, false, {\n                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FeedbackModal__WEBPACK_IMPORTED_MODULE_14__.FeedbackModal, {\n                isOpen: feedbackModalOpen,\n                onClose: ()=>setFeedbackModalOpen(false)\n            }, void 0, false, {\n                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SearchModal__WEBPACK_IMPORTED_MODULE_15__.SearchModal, {\n                isOpen: searchModalOpen,\n                onClose: ()=>setSearchModalOpen(false)\n            }, void 0, false, {\n                fileName: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/components/layout/Header.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"V2NaUjTGown62Rmr0wBzxJulQaI=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_6__.useNotifications,\n        _components_messaging__WEBPACK_IMPORTED_MODULE_7__.useMessaging\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Header.tsx\n"));

/***/ })

});