/*********************************************
# frontend/src/app/sponsorships/page.tsx
# 02/06/2025 5:45pm Created Sponsorships page with content from mytourismiq.com and sponsorship form
**********************************************/

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { DollarSign } from 'lucide-react';

export default function SponsorshipsPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [formData, setFormData] = useState({
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    budget: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/sponsorships', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setFormData({
          companyName: '',
          contactName: '',
          email: '',
          phone: '',
          budget: '',
          message: '',
        });
        setIsFormOpen(false);
        setShowSuccessDialog(true);
      } else {
        alert('Failed to submit sponsorship inquiry. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting sponsorship inquiry:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Hero Section */}
      <div className="mb-12">
        <h1 className="text-4xl font-bold text-[#3d405b] mb-4">Sponsorships</h1>
        <h2 className="text-2xl font-semibold text-[#3d405b] mb-6">
          Elevate Your Brand with TourismIQ
        </h2>
        <p className="text-lg text-gray-700 mb-8 leading-relaxed">
          Our sponsorship program is designed to help you reach the heart of the tourism marketing
          community. By becoming a sponsor, you position your brand front and center in the feeds
          our members visit the most. You&apos;re also able to position your brand as a featured
          listing at the top of our Vendor Directory. This is your opportunity to be recognized as a
          key supporter of the industry&apos;s newest and most dynamic platform.
        </p>
      </div>

      {/* Why Sponsor Section */}
      <div className="mb-12">
        <h3 className="text-2xl font-semibold text-[#3d405b] mb-6">Why Sponsor on TourismIQ?</h3>
        <ul className="space-y-4 text-gray-700">
          <li className="flex items-start">
            <div className="w-2 h-2 bg-[#5cc8ff] rounded-full mr-3 mt-2 flex-shrink-0"></div>
            <span>
              Sustained visibility with pinned posts that remain at the top of your feed until the
              sponsorship period ends
            </span>
          </li>
          <li className="flex items-start">
            <div className="w-2 h-2 bg-[#5cc8ff] rounded-full mr-3 mt-2 flex-shrink-0"></div>
            <span>Custom branding and design on your sponsored post</span>
          </li>
          <li className="flex items-start">
            <div className="w-2 h-2 bg-[#5cc8ff] rounded-full mr-3 mt-2 flex-shrink-0"></div>
            <span>Founding Sponsor gold bar recognition and thanks</span>
          </li>
          <li className="flex items-start">
            <div className="w-2 h-2 bg-[#5cc8ff] rounded-full mr-3 mt-2 flex-shrink-0"></div>
            <span>Lead generation tools directly integrated into your posts</span>
          </li>
        </ul>
      </div>

      {/* CTA Section */}
      <div className="mb-12">
        <p className="text-lg text-gray-700 mb-6">Fill out the form to get more information</p>
        <Button
          onClick={() => setIsFormOpen(true)}
          className="bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-bold rounded-full px-8 py-3"
        >
          Get Started
        </Button>
      </div>

      {/* Sponsorship Form Modal */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[600px] p-0 bg-white rounded-2xl shadow-2xl border-0">
          {/* Header with gradient background */}
          <div className="relative bg-gradient-to-r from-[#5cc8ff]/30 to-[#5cc8ff]/20 p-6 rounded-t-2xl">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white rounded-xl shadow-sm">
                <DollarSign className="h-6 w-6 text-[#5cc8ff]" />
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-[#3d405b] leading-tight">
                  Sponsorship Inquiry
                </DialogTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Tell us about your sponsorship goals and we&apos;ll get back to you
                </p>
              </div>
            </div>
          </div>

          {/* Form Content */}
          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Company and Contact Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyName" className="text-sm font-semibold text-gray-700">
                    Company Name *
                  </Label>
                  <Input
                    id="companyName"
                    type="text"
                    required
                    value={formData.companyName}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, companyName: e.target.value }))
                    }
                    className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                    placeholder="Enter your company name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactName" className="text-sm font-semibold text-gray-700">
                    Contact Name *
                  </Label>
                  <Input
                    id="contactName"
                    type="text"
                    required
                    value={formData.contactName}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, contactName: e.target.value }))
                    }
                    className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                    placeholder="Enter contact name"
                  />
                </div>
              </div>

              {/* Email and Phone Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-semibold text-gray-700">
                    Email Address *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                    className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                    placeholder="Enter your email"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-sm font-semibold text-gray-700">
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                    className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                    placeholder="Enter phone number"
                  />
                </div>
              </div>

              {/* Budget Row */}
              <div className="space-y-2">
                <Label htmlFor="budget" className="text-sm font-semibold text-gray-700">
                  Budget Range *
                </Label>
                <Select
                  value={formData.budget}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, budget: value }))}
                  required
                >
                  <SelectTrigger className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200">
                    <SelectValue placeholder="Select budget" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="500-1000">$500 - $1,000/month</SelectItem>
                    <SelectItem value="1000-2500">$1,000 - $2,500/month</SelectItem>
                    <SelectItem value="2500-5000">$2,500 - $5,000/month</SelectItem>
                    <SelectItem value="5000+">$5,000+/month</SelectItem>
                    <SelectItem value="custom">Custom Budget</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Message */}
              <div className="space-y-2">
                <Label htmlFor="message" className="text-sm font-semibold text-gray-700">
                  Tell us about your goals *
                </Label>
                <Textarea
                  id="message"
                  required
                  value={formData.message}
                  onChange={(e) => setFormData((prev) => ({ ...prev, message: e.target.value }))}
                  className="min-h-[120px] px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200 resize-none"
                  placeholder="Please describe your sponsorship goals, target audience, and any specific requirements..."
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsFormOpen(false)}
                  className="px-8 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 font-semibold rounded-full transition-all duration-200"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-8 py-3 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-bold rounded-full transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Inquiry'}
                </Button>
              </div>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="sm:max-w-md">
          <div className="flex flex-col items-center text-center p-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <DialogTitle className="text-xl font-semibold text-green-600 mb-2">
              Success!
            </DialogTitle>
            <p className="text-gray-600 mb-6">
              Sponsorship inquiry submitted successfully! We'll get back to you soon.
            </p>
            <Button
              onClick={() => setShowSuccessDialog(false)}
              className="px-8 py-2 bg-green-600 hover:bg-green-700 text-white rounded-full"
            >
              OK
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
