(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7842],{4516:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});let n=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5196:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});let n=(0,s(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},22436:(e,t,s)=>{"use strict";var n=s(12115),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,a=n.useEffect,l=n.useLayoutEffect,o=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!r(e,s)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),n=i({inst:{value:s,getSnapshot:t}}),r=n[0].inst,d=n[1];return l(function(){r.value=s,r.getSnapshot=t,c(r)&&d({inst:r})},[e,s,t]),a(function(){return c(r)&&d({inst:r}),e(function(){c(r)&&d({inst:r})})},[e]),o(s),s};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d},24422:(e,t,s)=>{"use strict";s.d(t,{l:()=>r});var n=s(12115);function r(){let[e,t]=(0,n.useState)({width:0,height:0,shouldUseAccordion:!0}),s=(0,n.useRef)(null),r=(0,n.useCallback)(()=>{let e=window.innerHeight;return{width:window.innerWidth,height:e,shouldUseAccordion:e<1338}},[]);return(0,n.useEffect)(()=>{function e(){s.current&&clearTimeout(s.current),s.current=setTimeout(()=>{t(r())},50)}t(r());let n=setTimeout(()=>{t(r())},16);return window.addEventListener("resize",e,{passive:!0}),()=>{window.removeEventListener("resize",e),clearTimeout(n),s.current&&clearTimeout(s.current)}},[r]),e}},28905:(e,t,s)=>{"use strict";s.d(t,{C:()=>a});var n=s(12115),r=s(6101),i=s(52712),a=e=>{let{present:t,children:s}=e,a=function(e){var t,s;let[r,a]=n.useState(),o=n.useRef(null),c=n.useRef(e),d=n.useRef("none"),[u,m]=(t=e?"mounted":"unmounted",s={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=s[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(o.current);d.current="mounted"===u?e:"none"},[u]),(0,i.N)(()=>{let t=o.current,s=c.current;if(s!==e){let n=d.current,r=l(t);e?m("MOUNT"):"none"===r||(null==t?void 0:t.display)==="none"?m("UNMOUNT"):s&&n!==r?m("ANIMATION_OUT"):m("UNMOUNT"),c.current=e}},[e,m]),(0,i.N)(()=>{if(r){var e;let t,s=null!=(e=r.ownerDocument.defaultView)?e:window,n=e=>{let n=l(o.current).includes(e.animationName);if(e.target===r&&n&&(m("ANIMATION_END"),!c.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=s.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(d.current=l(o.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{s.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}m("ANIMATION_END")},[r,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{o.current=e?getComputedStyle(e):null,a(e)},[])}}(t),o="function"==typeof s?s({present:a.isPresent}):n.Children.only(s),c=(0,r.s)(a.ref,function(e){var t,s;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,r=n&&"isReactWarning"in n&&n.isReactWarning;return r?e.ref:(r=(n=null==(s=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:s.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof s||a.isPresent?n.cloneElement(o,{ref:c}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},47863:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});let n=(0,s(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},49033:(e,t,s)=>{"use strict";e.exports=s(22436)},51154:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});let n=(0,s(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54011:(e,t,s)=>{"use strict";s.d(t,{H4:()=>A,_V:()=>N,bL:()=>w});var n=s(12115),r=s(46081),i=s(39033),a=s(52712),l=s(63655),o=s(49033);function c(){return()=>{}}var d=s(95155),u="Avatar",[m,h]=(0,r.A)(u),[f,x]=m(u),p=n.forwardRef((e,t)=>{let{__scopeAvatar:s,...r}=e,[i,a]=n.useState("idle");return(0,d.jsx)(f,{scope:s,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,d.jsx)(l.sG.span,{...r,ref:t})})});p.displayName=u;var v="AvatarImage",g=n.forwardRef((e,t)=>{let{__scopeAvatar:s,src:r,onLoadingStatusChange:u=()=>{},...m}=e,h=x(v,s),f=function(e,t){let{referrerPolicy:s,crossOrigin:r}=t,i=(0,o.useSyncExternalStore)(c,()=>!0,()=>!1),l=n.useRef(null),d=i?(l.current||(l.current=new window.Image),l.current):null,[u,m]=n.useState(()=>j(d,e));return(0,a.N)(()=>{m(j(d,e))},[d,e]),(0,a.N)(()=>{let e=e=>()=>{m(e)};if(!d)return;let t=e("loaded"),n=e("error");return d.addEventListener("load",t),d.addEventListener("error",n),s&&(d.referrerPolicy=s),"string"==typeof r&&(d.crossOrigin=r),()=>{d.removeEventListener("load",t),d.removeEventListener("error",n)}},[d,r,s]),u}(r,m),p=(0,i.c)(e=>{u(e),h.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==f&&p(f)},[f,p]),"loaded"===f?(0,d.jsx)(l.sG.img,{...m,ref:t,src:r}):null});g.displayName=v;var y="AvatarFallback",b=n.forwardRef((e,t)=>{let{__scopeAvatar:s,delayMs:r,...i}=e,a=x(y,s),[o,c]=n.useState(void 0===r);return n.useEffect(()=>{if(void 0!==r){let e=window.setTimeout(()=>c(!0),r);return()=>window.clearTimeout(e)}},[r]),o&&"loaded"!==a.imageLoadingStatus?(0,d.jsx)(l.sG.span,{...i,ref:t}):null});function j(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=y;var w=p,N=g,A=b},62244:(e,t,s)=>{Promise.resolve().then(s.bind(s,77020))},64614:(e,t,s)=>{"use strict";s.d(t,{iZ:()=>a,w1:()=>i});var n=s(32960),r=s(93853);function i(){let{data:e,isLoading:t,error:s,refetch:i}=(0,n.I)({queryKey:r.lH.auth.status(),queryFn:async()=>{let e=await fetch("/api/wp-proxy/auth/status",{headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok)return{isAuthenticated:!1,user:null};let t=await e.json();return{isAuthenticated:t.isLoggedIn||t.isAuthenticated||!1,user:t.wpUser||t.user||null}},staleTime:3e5,gcTime:6e5,retry:!1,refetchOnWindowFocus:!1,refetchOnReconnect:!0});return{isLoggedIn:(null==e?void 0:e.isAuthenticated)||!1,isLoading:t,user:(null==e?void 0:e.user)||null,error:s,refetch:i}}function a(){let{isLoggedIn:e,isLoading:t,user:s,error:n}=i();return{user:s||null,isAuthenticated:e||!1,isLoading:t,error:n}}},66474:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});let n=(0,s(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});let n=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},75335:(e,t,s)=>{"use strict";s.d(t,{RightSidebar:()=>L});var n=s(95155),r=s(6874),i=s.n(r),a=s(35695),l=s(30285),o=s(51154),c=s(5196),d=s(12318),u=s(75494),m=s(69074),h=s(4516),f=s(27600),x=s(12115),p=s(47996),v=s(93945),g=s(59949),y=s(26715),b=s(32960),j=s(93853),w=s(54165),N=s(47863),A=s(66474),S=s(66695),k=s(59434);function E(e){let{items:t,useAccordionMode:s,className:r}=e,[i,a]=(0,x.useState)(new Set),l=(0,x.useRef)(null),o=(0,x.useCallback)(()=>{if(!s){let e=t.filter(e=>e.defaultOpen).map(e=>e.id);return 0===e.length?t.map(e=>e.id):e}if(!l.current)return t.length>0?[t[0].id]:[];let e=l.current.clientHeight,n=e=>"my-connections"===e.id||"suggested-connections"===e.id||"upcoming-events"===e.id?240:180,r=[...t].sort((e,s)=>{var n,r;return(null!=(n=e.priority)?n:t.indexOf(e))-(null!=(r=s.priority)?r:t.indexOf(s))}),i=0,a=[];for(let t of r){let s=60+50*!!t.cta+12+n(t);if(i+s<=e-100)a.push(t.id),i+=s;else{0===a.length&&a.push(t.id);break}}return 0===a.length&&r.length>0&&a.push(r[0].id),a},[s,t]);(0,x.useEffect)(()=>{a(new Set(o()))},[o]),(0,x.useEffect)(()=>{if(!l.current)return;let e=new ResizeObserver(()=>{a(new Set(o()))});return e.observe(l.current),()=>{e.disconnect()}},[o]);let c=e=>{if(s)a(i.has(e)?new Set:new Set([e]));else{let t=new Set(i);t.has(e)?t.delete(e):t.add(e),a(t)}};return(0,n.jsx)("div",{ref:l,className:(0,k.cn)("space-y-3 h-full overflow-hidden",r),children:t.map(e=>{let t=i.has(e.id);return(0,n.jsxs)(S.Zp,{className:"border-gray-200 rounded-[26px]",children:[(0,n.jsx)(S.aR,{className:(0,k.cn)("pb-2 transition-colors rounded-t-[26px]",s&&"cursor-pointer select-none hover:bg-gray-50",!t&&s&&e.cta&&"pb-4"),onClick:()=>s&&c(e.id),children:(0,n.jsxs)("div",{className:(0,k.cn)("flex items-center justify-between",t&&"pb-2.5"),children:[(0,n.jsx)("h3",{className:"text-lg font-extrabold text-brand-text",children:e.title}),s&&(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),c(e.id)},className:"p-1 rounded-full hover:bg-gray-200 transition-colors","aria-label":t?"Collapse ".concat(e.title):"Expand ".concat(e.title),children:t?(0,n.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}):(0,n.jsx)(A.A,{className:"h-4 w-4 text-gray-500"})})]})}),(0,n.jsx)("div",{className:(0,k.cn)("transition-all duration-300 ease-in-out overflow-hidden",t?"max-h-[800px] opacity-100":"max-h-0 opacity-0"),children:(0,n.jsx)(S.Wu,{className:"pt-2.5",children:e.content})}),e.cta&&(0,n.jsx)("div",{className:"px-6 pb-6 pt-2.5",children:e.cta})]},e.id)})})}var C=s(24422);function _(e){let{memberId:t,memberName:s}=e,{status:r,refreshStatus:i,loading:a}=(0,v.qV)(t),{sendConnectionRequest:m,removeConnection:h}=(0,v.ZY)(),[f,p]=(0,x.useState)(!1),[g,b]=(0,x.useState)(!1),N=(0,y.jE)(),A=async(e,t)=>{p(!0);try{"request"===e&&(await m(t),N.setQueryData(j.lH.connections.status(t),{status:"pending",pending_type:"sent",can_request:!1,can_accept:!1,can_decline:!1,can_remove:!1}),await i())}catch(e){console.error("Connection action error:",e),await i()}finally{p(!1)}},S=async()=>{p(!0);try{await h(t),N.setQueryData(j.lH.connections.status(t),{status:"none",can_request:!0,can_accept:!1,can_decline:!1,can_remove:!1}),await i()}catch(e){console.error("Disconnect error:",e),await i()}finally{p(!1),b(!1)}},k=!!(f||a);if(a&&!r)return(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"h-8 text-xs",disabled:!0,children:(0,n.jsx)(o.A,{className:"h-3 w-3 animate-spin"})});if(!r||"none"===r.status)return(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"h-8 text-xs text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",onClick:()=>A("request",t),disabled:k,children:k?"...":"Connect"});if("pending"===r.status){if("sent"===r.pending_type)return(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"rounded-full w-8 h-8 p-0 text-[#5cc8ff] border-[#5cc8ff] bg-white hover:bg-[#5cc8ff]/10",disabled:!0,children:(0,n.jsx)(c.A,{className:"h-4 w-4"})});else if("received"===r.pending_type)return(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"rounded-full w-8 h-8 p-0 text-orange-600 border-orange-200 bg-white",disabled:!0,children:(0,n.jsx)(d.A,{className:"h-4 w-4"})})}return"accepted"===r.status?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"rounded-full w-8 h-8 p-0 bg-[#5cc8ff]/10 text-[#5cc8ff] border-[#5cc8ff] hover:bg-[#5cc8ff]/20",onClick:()=>b(!0),disabled:k,children:k?(0,n.jsx)(u.A,{className:"h-4 w-4 animate-spin"}):(0,n.jsx)(c.A,{className:"h-4 w-4"})}),(0,n.jsx)(w.lG,{open:g,onOpenChange:b,children:(0,n.jsxs)(w.Cf,{children:[(0,n.jsxs)(w.c7,{children:[(0,n.jsxs)(w.L3,{children:["Disconnect from ",s,"?"]}),(0,n.jsxs)(w.rr,{children:["Are you sure you want to disconnect from ",s,"? This will remove your connection and you'll need to send a new connection request to reconnect."]})]}),(0,n.jsxs)(w.Es,{children:[(0,n.jsx)(l.$,{variant:"outline",onClick:()=>b(!1),disabled:f,children:"Cancel"}),(0,n.jsx)(l.$,{variant:"destructive",onClick:S,disabled:f,children:f?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Disconnecting..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Disconnect"]})})]})]})})]}):null}let q=e=>(0,b.I)({queryKey:["newest-members",null==e?void 0:e.id],queryFn:async()=>{let t=await fetch("/api/wp-proxy/members/custom?per_page=20&page=1",{headers:{"Content-Type":"application/json"},credentials:"include"});if(!t.ok)throw Error("Failed to fetch users: ".concat(t.status));let s=await t.json();return[...s.founders||[],...s.members||[]].filter(t=>{var s;return!(t.id===e.id||t.id===parseInt((null==(s=e.id)?void 0:s.toString())||"0")||t.username===e.username||t.slug===e.username||t.name===e.name||t.username&&e.username&&t.username.toLowerCase()===e.username.toLowerCase()||t.slug&&e.username&&t.slug.toLowerCase()===e.username.toLowerCase())}).slice(0,4).map(e=>({id:e.id.toString(),name:e.name,username:e.slug||e.username||e.id.toString(),role:e.role||"Member",job_title:e.job_title||e.jobTitle||void 0,image:e.avatar||"/images/avatar-placeholder.svg"}))},enabled:!!(null==e?void 0:e.id),staleTime:3e5,gcTime:18e5}),O=e=>(0,b.I)({queryKey:["upcoming-events"],queryFn:async()=>await (0,p.oj)(3)||[],enabled:!e,staleTime:6e5,gcTime:36e5});function L(){let{isAuthenticated:e,user:t}=(0,f.A)(),s=(0,a.useRouter)(),r=(0,a.usePathname)(),[c,d]=(0,x.useState)(!1);(0,x.useEffect)(()=>{d(window.location.pathname.includes("/wp-admin")||document.body.classList.contains("wp-admin")||document.body.classList.contains("post-php"))},[]);let{data:u=[],isLoading:p}=q(t),{data:y=[],isLoading:b}=O(c),{connections:j,loading:w,error:N}=(0,v.n5)(),{shouldUseAccordion:A}=(0,C.l)(),S=[];return e&&S.push({id:"my-connections",title:"My Connections",defaultOpen:!0,priority:1,content:(0,n.jsx)("div",{className:"space-y-4",children:w?(0,n.jsxs)("div",{className:"py-4 flex flex-col items-center justify-center text-sm text-muted-foreground",children:[(0,n.jsx)(o.A,{className:"h-6 w-6 animate-spin mb-2"}),"Loading connections..."]}):N?(0,n.jsx)("div",{className:"py-2 text-center text-sm text-red-500",children:"Error loading connections"}):j.length>0?j.slice(0,3).map(e=>{var t;let s=e.name||e.display_name||e.username||"Unknown",r=("string"==typeof e.profile_picture?e.profile_picture:null==(t=e.profile_picture)?void 0:t.url)||e.avatar||"/images/avatar-placeholder.svg";return(0,n.jsx)("div",{className:"flex items-center justify-between",children:(0,n.jsxs)(i(),{href:"/profile/".concat(e.slug||e.username),className:"flex items-center space-x-3 hover:opacity-80 transition-opacity",children:[(0,n.jsx)(g.UserAvatarWithRank,{userId:"string"==typeof e.user_id?parseInt(e.user_id):e.user_id,avatarUrl:r,displayName:s,size:"h-10 w-10",containerSize:"w-12 h-12",userRoles:e.roles||[]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:s}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:e.job_title||"Member"})]})]})},e.user_id)}):(0,n.jsxs)("div",{className:"py-2 text-center text-sm text-muted-foreground",children:[(0,n.jsx)("p",{className:"mb-3",children:"No connections yet"}),(0,n.jsx)(i(),{href:"/member-directory",passHref:!0,children:(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",children:"Find Connections"})})]})}),cta:e&&!w&&!N&&(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",onClick:()=>{let e="/profile/".concat((null==t?void 0:t.username)||(null==t?void 0:t.id)),n="".concat(e,"?tab=connections");r===e?(window.history.pushState({},"",n),window.dispatchEvent(new CustomEvent("tabChange",{detail:{tab:"connections"}}))):s.push(n)},children:"My Connections"})}),S.push({id:"suggested-connections",title:"Newest Users",defaultOpen:!0,priority:2,content:(0,n.jsx)("div",{className:"space-y-4",children:e?p||!t?(0,n.jsxs)("div",{className:"py-4 flex flex-col items-center justify-center text-sm text-muted-foreground",children:[(0,n.jsx)(o.A,{className:"h-6 w-6 animate-spin mb-2"}),"Loading members..."]}):u.length>0?u.slice(0,4).map(e=>(0,n.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,n.jsxs)(i(),{href:"/profile/".concat(e.username||e.id),className:"flex items-start space-x-3 hover:opacity-80 transition-opacity flex-1 min-w-0",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)(g.UserAvatarWithRank,{userId:parseInt(e.id),avatarUrl:e.image||"/images/avatar-placeholder.svg",displayName:e.name,size:"h-10 w-10",containerSize:"w-12 h-12",userRoles:e.role?[e.role.toLowerCase()]:[]})}),(0,n.jsxs)("div",{className:"min-w-0",children:[(0,n.jsx)("p",{className:"text-sm font-medium truncate",children:e.name}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-1",children:e.job_title||e.role||"Member"})]})]}),(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)(_,{memberId:parseInt(e.id),memberName:e.name})})]},e.id)):(0,n.jsx)("div",{className:"py-2 text-center text-sm text-muted-foreground",children:"No members found"}):(0,n.jsxs)("div",{className:"py-4 flex flex-col items-center justify-center text-sm text-muted-foreground",children:[(0,n.jsx)("p",{className:"mb-3",children:"Log in to find your connections"}),(0,n.jsx)(i(),{href:"/login",passHref:!0,children:(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",children:"Log In"})})]})}),cta:(0,n.jsx)(i(),{href:"/member-directory",passHref:!0,children:(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",children:"View More"})})}),S.push({id:"upcoming-events",title:"Upcoming Events",defaultOpen:!0,priority:3,content:(0,n.jsx)("div",{className:"space-y-3",children:b?(0,n.jsxs)("div",{className:"py-4 flex flex-col items-center justify-center text-sm text-muted-foreground",children:[(0,n.jsx)(o.A,{className:"h-6 w-6 animate-spin mb-2"}),"Loading events..."]}):y.length>0?y.slice(0,3).map(e=>{var t,s;return(0,n.jsxs)(i(),{href:"/posts/".concat(e.slug),className:"block space-y-1 p-2 rounded-md transition-colors hover:bg-gray-50 cursor-pointer",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-brand-text",children:e.title.rendered}),(0,n.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,n.jsx)(m.A,{className:"h-3 w-3 mr-1"}),(0,n.jsx)("span",{children:(null==(t=e.acf)?void 0:t.event_start_date)||e.formatted_date||new Date(e.date).toLocaleDateString()})]}),(0,n.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,n.jsx)(h.A,{className:"h-3 w-3 mr-1"}),(0,n.jsx)("span",{children:(null==(s=e.acf)?void 0:s.event_location)||"Location not specified"})]})]},e.id.toString())}):(0,n.jsx)("div",{className:"py-2 text-center text-sm text-muted-foreground",children:"No upcoming events"})}),cta:!b&&y.length>0&&(0,n.jsx)(i(),{href:"/?category=event",passHref:!0,children:(0,n.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold",children:"View All Events"})})}),(0,n.jsx)("aside",{className:"flex w-full h-full","data-sidebar":"right",children:(0,n.jsx)("div",{className:(0,k.cn)("w-full p-4","overflow-hidden","[&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"),children:(0,n.jsx)(E,{items:S,useAccordionMode:A})})})}},77020:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var n=s(95155),r=s(24422),i=s(75335);function a(){let{width:e,height:t,shouldUseAccordion:s}=(0,r.l)();return(0,n.jsxs)("div",{className:"min-h-screen p-8 flex",children:[(0,n.jsx)("div",{className:"flex-1 max-w-md",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Sidebar Height Test"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Browser Width:"}),(0,n.jsxs)("p",{className:"text-lg font-mono",children:[e,"px"]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Browser Height:"}),(0,n.jsxs)("p",{className:"text-lg font-mono",children:[t,"px"]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Sidebar Mode:"}),(0,n.jsx)("p",{className:"text-lg font-semibold ".concat(s?"text-orange-600":"text-green-600"),children:s?"Accordion Mode":"All Open Mode"})]}),(0,n.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("h3",{className:"font-medium mb-2",children:"Instructions:"}),(0,n.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,n.jsx)("li",{children:"• Resize your browser window vertically"}),(0,n.jsx)("li",{children:"• Watch the sidebar mode change"}),(0,n.jsx)("li",{children:"• Height threshold: ~1338px"}),(0,n.jsx)("li",{children:"• Above threshold: All boxes open"}),(0,n.jsx)("li",{children:"• Below threshold: Accordion mode (priority-based)"}),(0,n.jsxs)("li",{children:["• ",(0,n.jsx)("strong",{children:"No scrollbars will appear during transitions"})]})]})]}),(0,n.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,n.jsx)("h3",{className:"font-medium mb-2",children:"Priority Order:"}),(0,n.jsxs)("ol",{className:"text-sm text-gray-600 space-y-1",children:[(0,n.jsx)("li",{children:"1. My Connections (highest priority)"}),(0,n.jsx)("li",{children:"2. Suggested Connections"}),(0,n.jsx)("li",{children:"3. Upcoming Events (lowest priority)"})]})]})]})]})}),(0,n.jsx)("div",{className:"w-[350px] ml-8",children:(0,n.jsx)("div",{className:"sticky top-4 h-[calc(100vh-32px)]",children:(0,n.jsx)(i.RightSidebar,{})})})]})}},93853:(e,t,s)=>{"use strict";s.d(t,{WG:()=>i,lH:()=>r,qQ:()=>n});let n=new(s(87017)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:3,refetchOnWindowFocus:!1,refetchOnReconnect:!1},mutations:{retry:1}}}),r={posts:{all:["posts"],lists:()=>[...r.posts.all,"list"],list:e=>[...r.posts.lists(),e],details:()=>[...r.posts.all,"detail"],detail:e=>[...r.posts.details(),e],userPosts:(e,t,s)=>[...r.posts.all,"user",e,{page:t,perPage:s}],byCategory:(e,t)=>[...r.posts.all,"category",e,t]},auth:{all:["auth"],status:()=>[...r.auth.all,"status"],profile:()=>[...r.auth.all,"profile"]},members:{all:["members"],lists:()=>[...r.members.all,"list"],list:e=>[...r.members.lists(),e],details:()=>[...r.members.all,"detail"],detail:e=>[...r.members.details(),e],profile:e=>[...r.members.all,"profile",e]},connections:{all:["connections"],status:e=>[...r.connections.all,"status",e],list:e=>[...r.connections.all,"list",e],pending:()=>[...r.connections.all,"pending"]},iqScore:{all:["iqScore"],user:e=>[...r.iqScore.all,"user",e],me:()=>[...r.iqScore.all,"me"],leaderboard:()=>[...r.iqScore.all,"leaderboard"],ranks:()=>[...r.iqScore.all,"ranks"]},forum:{all:["forum"],questions:e=>[...r.forum.all,"questions",e],question:e=>[...r.forum.all,"question",e],userQuestions:e=>[...r.forum.all,"user",e],comments:e=>[...r.forum.all,"comments",e]},messages:{all:["messages"],conversations:()=>[...r.messages.all,"conversations"],conversation:e=>[...r.messages.all,"conversation",e],unreadCount:()=>[...r.messages.all,"unreadCount"]},vendors:{all:["vendors"],lists:()=>[...r.vendors.all,"list"],list:e=>[...r.vendors.lists(),e],details:()=>[...r.vendors.all,"detail"],detail:e=>[...r.vendors.details(),e],posts:e=>[...r.vendors.all,"posts",e],categories:()=>[...r.vendors.all,"categories"]},categories:{all:["categories"],list:()=>[...r.categories.all,"list"]},bookmarks:{all:["bookmarks"],user:e=>[...r.bookmarks.all,"user",e]},notifications:{all:["notifications"],list:()=>[...r.notifications.all,"list"]}},i={allPosts:()=>n.invalidateQueries({queryKey:r.posts.all}),post:e=>n.invalidateQueries({queryKey:r.posts.detail(e)}),userPosts:e=>n.invalidateQueries({queryKey:[...r.posts.all,"user",e]}),auth:()=>n.invalidateQueries({queryKey:r.auth.all}),connections:()=>n.invalidateQueries({queryKey:r.connections.all}),iqScores:()=>n.invalidateQueries({queryKey:r.iqScore.all}),forum:()=>n.invalidateQueries({queryKey:r.forum.all}),messages:()=>n.invalidateQueries({queryKey:r.messages.all}),notifications:()=>n.invalidateQueries({queryKey:r.notifications.all})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,6874,2757,409,6766,5272,3750,7600,65,3091,8441,1684,7358],()=>t(62244)),_N_E=e.O()}]);