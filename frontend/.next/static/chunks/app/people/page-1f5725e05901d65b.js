(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7284],{2870:()=>{},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>n,t:()=>l});var s=t(12115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function l(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function n(...e){return s.useCallback(l(...e),e)}},6654:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return a}});let s=t(12115);function a(e,r){let t=(0,s.useRef)(null),a=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=t.current;e&&(t.current=null,e());let r=a.current;r&&(a.current=null,r())}else e&&(t.current=l(e,s)),r&&(a.current=l(r,s))},[e,r])}function l(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},10130:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var s=t(95155),a=t(12115),l=t(47924),n=t(54416),i=t(62523),o=t(30285),d=t(59434);function c(e){let{onSearch:r,placeholder:t="Search questions...",defaultValue:c="",inputClassName:u=""}=e,[m,f]=(0,a.useState)(c),[p,x]=(0,a.useState)(!1),h=(0,a.useRef)(!0);return(0,a.useEffect)(()=>{x(!0)},[]),(0,a.useEffect)(()=>{if(!p)return;if(h.current){h.current=!1;return}let e=setTimeout(()=>{r(m)},300);return()=>clearTimeout(e)},[m,r,p]),(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),r(m)},className:"relative",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,s.jsx)(i.p,{type:"text",placeholder:t,value:m,onChange:e=>f(e.target.value),className:(0,d.cn)("!pl-10 pr-10",u)}),m&&(0,s.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>{f("")},className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100",children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})]})})}},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),n=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},o=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:c="",children:u,iconNode:m,...f}=e;return(0,s.createElement)("svg",{ref:r,...d,width:a,height:a,stroke:t,strokeWidth:n?24*Number(l)/Number(a):l,className:i("lucide",c),...!u&&!o(f)&&{"aria-hidden":"true"},...f},[...m.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),u=(e,r)=>{let t=(0,s.forwardRef)((t,l)=>{let{className:o,...d}=t;return(0,s.createElement)(c,{ref:l,iconNode:r,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),o),...d})});return t.displayName=n(e),t}},25918:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(95155),a=t(12115),l=t(35695),n=t(66695),i=t(30285),o=t(26126),d=t(54416),c=t(10130),u=t(66766);function m(e){let{images:r}=e,t=e=>e.sizes.large||e.sizes.medium||e.url||"",a=e=>1===e?"grid-cols-1 max-w-[140px]":2===e?"grid-cols-2 max-w-[284px]":3===e?"grid-cols-3 max-w-[426px]":"grid-cols-4",l=(e=>{let r=[];for(let t=0;t<e.length;t+=4)r.push(e.slice(t,t+4));return r})(r);return(0,s.jsx)("div",{className:"w-full py-10 px-10 rounded-lg",style:{backgroundColor:"rgba(170, 215, 238, 0.6)"},children:(0,s.jsxs)("div",{className:"space-y-2",children:[l.map((e,r)=>(0,s.jsx)("div",{className:"grid gap-2 mx-auto ".concat(a(e.length)),children:e.map((e,r)=>(0,s.jsx)("div",{className:"aspect-square overflow-hidden bg-muted rounded-lg max-w-[140px] max-h-[140px]",children:(0,s.jsx)(u.default,{src:t(e),alt:e.alt||"Person image",width:140,height:140,className:"object-cover w-full h-full",priority:!1})},e.ID||r))},r)),r.length>10&&(0,s.jsxs)("div",{className:"text-center text-sm text-gray-500 mt-2",children:["+",r.length-10," more images"]})]})})}function f(){let e=(0,l.useSearchParams)(),r=(0,l.useRouter)(),t=(0,a.useRef)(null),f=parseInt(e.get("page")||"1"),p=e.get("search")||"",[,x]=(0,a.useState)([]),[h,g]=(0,a.useState)({}),[v,b]=(0,a.useState)({}),[,y]=(0,a.useState)(1),[j,N]=(0,a.useState)(0),[w,k]=(0,a.useState)(!0),[C,_]=(0,a.useState)(null),[S,E]=(0,a.useState)(""),[R,P]=(0,a.useState)(p),A=(0,a.useCallback)(async()=>{try{k(!0);let e=new URLSearchParams({page:f.toString(),per_page:"100",...R&&{search:R}}),r=await fetch("/api/wp-proxy/people?".concat(e),{credentials:"include"});if(!r.ok)throw Error("Failed to fetch people");let t=await r.json();x(t.people),y(t.totalPages),N(t.totalItems);let s=t.people.reduce((e,r)=>{let t=new Date(r.date).getFullYear().toString();return e[t]||(e[t]=[]),e[t].push(r),e},{});g(s);let a=Object.keys(s);b(a.reduce((e,r)=>({...e,[r]:!0}),{}))}catch(e){_(e instanceof Error?e.message:"Failed to fetch people")}finally{k(!1)}},[f,R]);(0,a.useEffect)(()=>{E(p)},[p]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{P(S)},300);return()=>clearTimeout(e)},[S]),(0,a.useEffect)(()=>{A()},[A]),(0,a.useEffect)(()=>(document.documentElement.style.overflowY="scroll",()=>{document.documentElement.style.overflowY=""}),[]);let F=e=>{b(r=>({...r,[e]:!r[e]}))},O=Object.values(v).every(e=>!e),L=e=>e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2),T=e=>{let r=document.createElement("textarea");return r.innerHTML=e,r.value};return C?(0,s.jsx)("div",{className:"max-w-6xl mx-auto p-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-[#3d405b] mb-4",children:"Error Loading People"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:C}),(0,s.jsx)("button",{onClick:A,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Try Again"})]})}):(0,s.jsxs)("div",{className:"space-y-0 overflow-x-hidden",children:[(0,s.jsx)("div",{className:"relative w-full aspect-[1118/486] bg-gray-100",children:(0,s.jsx)(u.default,{src:"/images/people-move.jpg",alt:"People on the Move - Tourism Industry Personnel Updates",fill:!0,className:"object-cover",priority:!0})}),(0,s.jsx)(n.Zp,{className:"shadow-sm -mt-6 rounded-none rounded-b-lg",children:(0,s.jsxs)("div",{className:"px-6 py-8",children:[(0,s.jsxs)("div",{ref:t,className:"relative z-10 mb-8",children:[(0,s.jsx)(o.E,{className:"bg-pink-600 text-white mb-2",children:"People on the Move"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-700",children:"It's Who You Know..."})]}),(0,s.jsxs)("div",{className:"space-y-4 mb-8",children:[(0,s.jsx)(c.A,{onSearch:e=>E(e),placeholder:"Search by name, company, or position...",defaultValue:S,inputClassName:"bg-white rounded-full h-12 border border-gray-200 px-6 text-base"}),R&&(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>{E(""),P(""),r.push("/people")},className:"text-gray-600 hover:text-gray-800",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-1"}),"Clear search"]})})]}),!w&&(0,s.jsx)("div",{className:"mb-4 text-sm text-gray-600",children:R?(0,s.jsxs)("p",{children:["Found ",j," result",1!==j?"s":"",' for "',R,'"']}):(0,s.jsxs)("p",{children:["Showing ",j," people"]})}),w&&(0,s.jsx)("div",{className:"space-y-8",children:[void 0,void 0,void 0].map((e,r)=>(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-12 bg-gray-200 rounded mb-4"}),(0,s.jsx)("div",{className:"border-b pb-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-6",children:[(0,s.jsx)("div",{className:"w-full md:w-1/3 h-[300px] bg-gray-200 rounded"}),(0,s.jsxs)("div",{className:"w-full md:w-2/3 space-y-4",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-20"}),(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-24"})]})]})]})})]},r))}),!w&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("div",{className:"flex justify-end mb-2 mt-1",children:(0,s.jsx)("button",{onClick:()=>{let e=Object.values(v).every(e=>!e);b(Object.keys(h).reduce((r,t)=>({...r,[t]:e}),{}))},className:"text-sm text-gray-500 hover:text-gray-700",children:O?"Expand all":"Collapse all"})}),Object.entries(h).sort((e,r)=>{let[t]=e,[s]=r;return parseInt(s)-parseInt(t)}).map(e=>{let[r,t]=e;return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)(i.$,{variant:"ghost",onClick:()=>F(r),className:"w-full flex justify-between items-center py-4 text-xl font-semibold bg-[#EBF5FF]/75 hover:bg-[#EBF5FF]/75 rounded-full",children:[r,(0,s.jsx)("span",{children:v[r]?"−":"+"})]}),v[r]&&(0,s.jsx)("div",{className:"space-y-8",children:t.map(e=>{var r;let t=(null==(r=e.people_meta)?void 0:r.people_gallery)&&e.people_meta.people_gallery.length>0;return(0,s.jsx)("div",{className:"border-b pb-8",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"w-full",children:t?(0,s.jsx)(m,{images:e.people_meta.people_gallery}):e.featured_media_url?(0,s.jsx)("div",{className:"w-full py-10 px-10 rounded-lg",style:{backgroundColor:"rgba(170, 215, 238, 0.6)"},children:(0,s.jsx)("div",{className:"grid grid-cols-1 max-w-[140px] mx-auto",children:(0,s.jsx)("div",{className:"aspect-square overflow-hidden bg-muted rounded-lg max-w-[140px] max-h-[140px]",children:(0,s.jsx)(u.default,{src:e.featured_media_url,alt:e.title.rendered,width:140,height:140,className:"object-cover w-full h-full",priority:!1})})})}):(0,s.jsx)("div",{className:"w-full py-10 px-10 rounded-lg",style:{backgroundColor:"rgba(170, 215, 238, 0.6)"},children:(0,s.jsx)("div",{className:"grid grid-cols-1 max-w-[140px] mx-auto",children:(0,s.jsx)("div",{className:"aspect-square flex items-center justify-center bg-gray-100 rounded-lg text-gray-600 text-3xl max-w-[140px] max-h-[140px]",children:L(T(e.title.rendered)||"Unknown")})})})}),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-[#3d405b] mb-2",children:T(e.title.rendered)}),(0,s.jsx)("div",{className:"text-lg mb-4 prose prose-sm max-w-none",children:(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:e.content.rendered}})})]})]})},e.id)})})]},r)}),0===Object.keys(h).length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-[#3d405b] mb-2",children:"No people found"}),(0,s.jsx)("p",{className:"text-gray-600",children:R?"Try adjusting your search terms.":"No people have been added yet."})]})]})]})})]})}function p(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:"Loading..."}),children:(0,s.jsx)(f,{})})}t(2870)},26126:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var s=t(95155);t(12115);var a=t(74466),l=t(59434);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:t}),r),...a})}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o,r:()=>i});var s=t(95155);t(12115);var a=t(99708),l=t(74466),n=t(59434);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-semibold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20",outline:"border border-primary bg-white text-foreground shadow-xs hover:bg-primary hover:text-primary-foreground",secondary:"border border-gray-300 bg-white text-foreground shadow-xs hover:bg-gray-100 hover:text-foreground",ghost:"text-foreground hover:bg-primary/10 hover:text-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:l,asChild:o=!1,...d}=e,c=o?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:l,className:r})),...d})}},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"notFound")&&t.d(r,{notFound:function(){return s.notFound}}),t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},43946:(e,r,t)=>{Promise.resolve().then(t.bind(t,25918))},47924:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},54416:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var s=t(52596),a=t(39688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var s=t(95155);t(12115);var a=t(59434);function l(e){let{className:r,type:t,...l}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...l})}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>u});var s=t(95155),a=t(12115),l=t(59434);let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6",t),...a})});c.displayName="CardContent";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6",t),...a})});u.displayName="CardFooter"},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i,Dc:()=>d,TL:()=>n});var s=t(12115),a=t(6101),l=t(95155);function n(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...l}=e;if(s.isValidElement(t)){var n;let e,i,o=(n=t,(i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(i=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),d=function(e,r){let t={...r};for(let s in r){let a=e[s],l=r[s];/^on[A-Z]/.test(s)?a&&l?t[s]=(...e)=>{let r=l(...e);return a(...e),r}:a&&(t[s]=a):"style"===s?t[s]={...a,...l}:"className"===s&&(t[s]=[a,l].filter(Boolean).join(" "))}return{...e,...t}}(l,t.props);return t.type!==s.Fragment&&(d.ref=r?(0,a.t)(r,o):o),s.cloneElement(t,d)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:a,...n}=e,i=s.Children.toArray(a),o=i.find(c);if(o){let e=o.props.children,a=i.map(r=>r!==o?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,l.jsx)(r,{...n,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,l.jsx)(r,{...n,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}var i=n("Slot"),o=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=o,r}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var r=r=>e(e.s=r);e.O(0,[9967,5003,6766,8441,1684,7358],()=>r(43946)),_N_E=e.O()}]);