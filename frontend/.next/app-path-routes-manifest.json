{"/api/auth/forgot-password/route": "/api/auth/forgot-password", "/api/auth/google/route": "/api/auth/google", "/api/auth/logout/route": "/api/auth/logout", "/api/auth/reset-password/route": "/api/auth/reset-password", "/api/auth/google/callback/route": "/api/auth/google/callback", "/api/auth/status/route": "/api/auth/status", "/api/auth/register/route": "/api/auth/register", "/api/debug/route": "/api/debug", "/api/google-callback/route": "/api/google-callback", "/api/feedback/route": "/api/feedback", "/api/posts/create/route": "/api/posts/create", "/api/posts/update/route": "/api/posts/update", "/api/posts/upload-media/route": "/api/posts/upload-media", "/api/revalidate/route": "/api/revalidate", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/search/route": "/api/search", "/api/sponsorships/route": "/api/sponsorships", "/api/support/route": "/api/support", "/api/stripe/verify-session/[sessionId]/route": "/api/stripe/verify-session/[sessionId]", "/api/test/sitemap-dates/route": "/api/test/sitemap-dates", "/api/upload/route": "/api/upload", "/api/user/[id]/meta/route": "/api/user/[id]/meta", "/api/user/cover-image/upload/route": "/api/user/cover-image/upload", "/api/user/avatar/upload/route": "/api/user/avatar/upload", "/api/user/cover/upload/route": "/api/user/cover/upload", "/api/posts/user/[userId]/route": "/api/posts/user/[userId]", "/api/user/me/route": "/api/user/me", "/api/user/password/route": "/api/user/password", "/api/vendor-suggestion/route": "/api/vendor-suggestion", "/api/user/profile/route": "/api/user/profile", "/api/wp-proxy/auth/reset-password/route": "/api/wp-proxy/auth/reset-password", "/api/wp-proxy/auth/status/route": "/api/wp-proxy/auth/status", "/api/wp-proxy/cache/invalidations/route": "/api/wp-proxy/cache/invalidations", "/api/wp-proxy/connections/accept/route": "/api/wp-proxy/connections/accept", "/api/wp-proxy/categories/route": "/api/wp-proxy/categories", "/api/wp-proxy/connections/decline/route": "/api/wp-proxy/connections/decline", "/api/wp-proxy/connections/bulk-status/route": "/api/wp-proxy/connections/bulk-status", "/api/wp-proxy/connections/remove/route": "/api/wp-proxy/connections/remove", "/api/wp-proxy/connections/status/[otherUserId]/route": "/api/wp-proxy/connections/status/[otherUserId]", "/api/wp-proxy/connections/route": "/api/wp-proxy/connections", "/api/wp-proxy/forum/[id]/comments/route": "/api/wp-proxy/forum/[id]/comments", "/api/wp-proxy/forum/[id]/route": "/api/wp-proxy/forum/[id]", "/api/wp-proxy/forum/create/route": "/api/wp-proxy/forum/create", "/api/wp-proxy/forum/user/[userId]/route": "/api/wp-proxy/forum/user/[userId]", "/api/wp-proxy/forum/route": "/api/wp-proxy/forum", "/api/wp-proxy/iq-score/award/route": "/api/wp-proxy/iq-score/award", "/api/wp-proxy/iq-score/[userId]/route": "/api/wp-proxy/iq-score/[userId]", "/api/wp-proxy/iq-score/debug/route": "/api/wp-proxy/iq-score/debug", "/api/wp-proxy/iq-score/me/route": "/api/wp-proxy/iq-score/me", "/api/wp-proxy/iq-score/bulk/route": "/api/wp-proxy/iq-score/bulk", "/api/wp-proxy/iq-score/leaderboard/route": "/api/wp-proxy/iq-score/leaderboard", "/api/wp-proxy/iq-score/test-award/route": "/api/wp-proxy/iq-score/test-award", "/api/wp-proxy/iq-score/ranks/route": "/api/wp-proxy/iq-score/ranks", "/api/wp-proxy/jobs/[slug]/route": "/api/wp-proxy/jobs/[slug]", "/api/wp-proxy/jobs/countries/route": "/api/wp-proxy/jobs/countries", "/api/wp-proxy/jobs/search/route": "/api/wp-proxy/jobs/search", "/api/wp-proxy/jobs/route": "/api/wp-proxy/jobs", "/api/wp-proxy/legacy-messages/conversation/[id]/route": "/api/wp-proxy/legacy-messages/conversation/[id]", "/api/wp-proxy/jobs/categories/route": "/api/wp-proxy/jobs/categories", "/api/wp-proxy/members/custom/route": "/api/wp-proxy/members/custom", "/api/wp-proxy/legacy-messages/conversations/route": "/api/wp-proxy/legacy-messages/conversations", "/api/wp-proxy/jobs/states/route": "/api/wp-proxy/jobs/states", "/api/wp-proxy/messages/can-message/[userId]/route": "/api/wp-proxy/messages/can-message/[userId]", "/api/wp-proxy/members/route": "/api/wp-proxy/members", "/api/wp-proxy/messages/mark-read/route": "/api/wp-proxy/messages/mark-read", "/api/wp-proxy/messages/conversation/[userId]/route": "/api/wp-proxy/messages/conversation/[userId]", "/api/wp-proxy/messages/conversations/route": "/api/wp-proxy/messages/conversations", "/api/wp-proxy/notifications/route": "/api/wp-proxy/notifications", "/api/wp-proxy/messages/send/route": "/api/wp-proxy/messages/send", "/api/wp-proxy/notifications/read-all/route": "/api/wp-proxy/notifications/read-all", "/api/wp-proxy/messages/unread-count/route": "/api/wp-proxy/messages/unread-count", "/api/wp-proxy/notifications/send/route": "/api/wp-proxy/notifications/send", "/api/wp-proxy/posts/[postId]/upvote/route": "/api/wp-proxy/posts/[postId]/upvote", "/api/wp-proxy/posts/[postId]/comments/route": "/api/wp-proxy/posts/[postId]/comments", "/api/wp-proxy/posts/[postId]/route": "/api/wp-proxy/posts/[postId]", "/api/wp-proxy/people/route": "/api/wp-proxy/people", "/api/wp-proxy/posts/[postId]/upvotes/route": "/api/wp-proxy/posts/[postId]/upvotes", "/api/wp-proxy/posts/create/route": "/api/wp-proxy/posts/create", "/api/wp-proxy/posts/route": "/api/wp-proxy/posts", "/api/wp-proxy/posts/batch-upvotes/route": "/api/wp-proxy/posts/batch-upvotes", "/api/wp-proxy/posts/by-category/[slug]/route": "/api/wp-proxy/posts/by-category/[slug]", "/api/wp-proxy/connections/pending/route": "/api/wp-proxy/connections/pending", "/api/wp-proxy/posts/user/[userId]/route": "/api/wp-proxy/posts/user/[userId]", "/api/wp-proxy/rfps/[slug]/route": "/api/wp-proxy/rfps/[slug]", "/api/wp-proxy/rfps/by-id/[id]/route": "/api/wp-proxy/rfps/by-id/[id]", "/api/wp-proxy/rfps/countries/route": "/api/wp-proxy/rfps/countries", "/api/wp-proxy/connections/request/route": "/api/wp-proxy/connections/request", "/api/wp-proxy/rfps/states/route": "/api/wp-proxy/rfps/states", "/api/wp-proxy/user/assigned-vendors/route": "/api/wp-proxy/user/assigned-vendors", "/api/wp-proxy/rfps/route": "/api/wp-proxy/rfps", "/api/wp-proxy/users/[userId]/comments-count/route": "/api/wp-proxy/users/[userId]/comments-count", "/api/wp-proxy/vendor-categories/route": "/api/wp-proxy/vendor-categories", "/api/wp-proxy/vendor-subscription/[id]/route": "/api/wp-proxy/vendor-subscription/[id]", "/api/wp-proxy/users/search/route": "/api/wp-proxy/users/search", "/api/wp-proxy/vendor-subscription/[id]/customer-portal/route": "/api/wp-proxy/vendor-subscription/[id]/customer-portal", "/api/wp-proxy/vendor-upgrade/[id]/route": "/api/wp-proxy/vendor-upgrade/[id]", "/api/wp-proxy/vendors/[slug]/route": "/api/wp-proxy/vendors/[slug]", "/api/wp-proxy/vendors/by-id/[id]/request-upgrade-link/route": "/api/wp-proxy/vendors/by-id/[id]/request-upgrade-link", "/api/wp-proxy/vendors/[slug]/posts/route": "/api/wp-proxy/vendors/[slug]/posts", "/api/wp-proxy/rfps/categories/route": "/api/wp-proxy/rfps/categories", "/api/wp-proxy/vendors/by-id/[id]/route": "/api/wp-proxy/vendors/by-id/[id]", "/api/wp-proxy/vendors/paid/route": "/api/wp-proxy/vendors/paid", "/api/wp-proxy/vendors/categories/route": "/api/wp-proxy/vendors/categories", "/api/wp-proxy/vendors/search/route": "/api/wp-proxy/vendors/search", "/api/wp-proxy/vendors/route": "/api/wp-proxy/vendors", "/api/wp-proxy/users/me/cover-image/route": "/api/wp-proxy/users/me/cover-image", "/robots.txt/route": "/robots.txt", "/sitemap.xml/route": "/sitemap.xml", "/sitemap-test.xml/route": "/sitemap-test.xml", "/api/auth/login/route": "/api/auth/login", "/_not-found/page": "/_not-found", "/cache-debug/page": "/cache-debug", "/forgot-password/page": "/forgot-password", "/debug-auth/page": "/debug-auth", "/forum/new/page": "/forum/new", "/forum/[id]/page": "/forum/[id]", "/jobs/[slug]/page": "/jobs/[slug]", "/forum/page": "/forum", "/login/page": "/login", "/dev-people-preview/page": "/dev-people-preview", "/people/page": "/people", "/reset-password/page": "/reset-password", "/register/page": "/register", "/profile/[username]/page": "/profile/[username]", "/member-directory/page": "/member-directory", "/privacy-policy/page": "/privacy-policy", "/sponsorships/page": "/sponsorships", "/rfps/[slug]/page": "/rfps/[slug]", "/terms-of-use/page": "/terms-of-use", "/test-iq-notifications/page": "/test-iq-notifications", "/vendor-upgrade/page": "/vendor-upgrade", "/test-error-dialogs/page": "/test-error-dialogs", "/test-sidebar-height/page": "/test-sidebar-height", "/vendors/page": "/vendors", "/vendors/[slug]/page": "/vendors/[slug]", "/vendor-upgrade-success/page": "/vendor-upgrade-success", "/search/page": "/search", "/jobs/page": "/jobs", "/page": "/", "/category/[slug]/page": "/category/[slug]", "/rfps/page": "/rfps", "/settings/page": "/settings", "/posts/[slug]/page": "/posts/[slug]"}