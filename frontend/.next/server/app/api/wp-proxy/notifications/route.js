/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wp-proxy/notifications/route";
exports.ids = ["app/api/wp-proxy/notifications/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fnotifications%2Froute&page=%2Fapi%2Fwp-proxy%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fnotifications%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fnotifications%2Froute&page=%2Fapi%2Fwp-proxy%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fnotifications%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wp-proxy/notifications/route.ts */ \"(rsc)/./src/app/api/wp-proxy/notifications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wp-proxy/notifications/route\",\n        pathname: \"/api/wp-proxy/notifications\",\n        filename: \"route\",\n        bundlePath: \"app/api/wp-proxy/notifications/route\"\n    },\n    resolvedPagePath: \"/Volumes/Mac Storage/Local Sites/tourismiq-headless/app/public/wp-content/themes/tourismq_wp/frontend/src/app/api/wp-proxy/notifications/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Mac_Storage_Local_Sites_tourismiq_headless_app_public_wp_content_themes_tourismq_wp_frontend_src_app_api_wp_proxy_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fnotifications%2Froute&page=%2Fapi%2Fwp-proxy%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fnotifications%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wp-proxy/notifications/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/wp-proxy/notifications/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/headers.js\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const limit = searchParams.get('limit') || '50';\n        const offset = searchParams.get('offset') || '0';\n        const unread_only = searchParams.get('unread_only') || 'false';\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        const allCookies = cookieStore.getAll();\n        // Build cookie header string from all cookies\n        const cookieHeader = allCookies.map((cookie)=>`${cookie.name}=${cookie.value}`).join('; ');\n        // Get WordPress URL from env or use default\n        const WORDPRESS_API_URL = \"http://mytourismiq.wpenginepowered.com/\" || 0;\n        // Prepare headers for WordPress request - only using Cookie header for authentication\n        const headers = {\n            'Content-Type': 'application/json',\n            Cookie: cookieHeader\n        };\n        // Build query string\n        const queryParams = new URLSearchParams({\n            limit,\n            offset,\n            unread_only\n        });\n        // Forward request to WordPress REST API\n        const wpEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/notifications?${queryParams}`;\n        const response = await fetch(wpEndpoint, {\n            method: 'GET',\n            headers\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `WordPress API returned ${response.status}`,\n                details: errorText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        // Don't cache notification responses as they need to be real-time\n        const nextResponse = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');\n        nextResponse.headers.set('Pragma', 'no-cache');\n        nextResponse.headers.set('Expires', '0');\n        return nextResponse;\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to proxy notification request to WordPress',\n            details: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Notification ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        const allCookies = cookieStore.getAll();\n        // Build cookie header string from all cookies\n        const cookieHeader = allCookies.map((cookie)=>`${cookie.name}=${cookie.value}`).join('; ');\n        // Get WordPress URL from env or use default\n        const WORDPRESS_API_URL = \"http://mytourismiq.wpenginepowered.com/\" || 0;\n        // Prepare headers for WordPress request\n        const headers = {\n            'Content-Type': 'application/json',\n            Cookie: cookieHeader\n        };\n        // Forward DELETE request to WordPress REST API\n        const wpEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/notifications/${id}`;\n        const response = await fetch(wpEndpoint, {\n            method: 'DELETE',\n            headers\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `WordPress API returned ${response.status}`,\n                details: errorText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        // Don't cache deletion responses\n        const nextResponse = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');\n        nextResponse.headers.set('Pragma', 'no-cache');\n        nextResponse.headers.set('Expires', '0');\n        return nextResponse;\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete notification',\n            details: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wp-proxy/notifications/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwp-proxy%2Fnotifications%2Froute&page=%2Fapi%2Fwp-proxy%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwp-proxy%2Fnotifications%2Froute.ts&appDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FMac%20Storage%2FLocal%20Sites%2Ftourismiq-headless%2Fapp%2Fpublic%2Fwp-content%2Fthemes%2Ftourismq_wp%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();